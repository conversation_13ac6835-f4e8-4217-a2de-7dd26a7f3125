
/* !!! This is code generated by Prisma. Do not edit directly. !!!
/* eslint-disable */

Object.defineProperty(exports, "__esModule", { value: true });

const {
  PrismaClientKnownRequestError,
  PrismaClientUnknownRequestError,
  PrismaClientRustPanicError,
  PrismaClientInitializationError,
  PrismaClientValidationError,
  getPrismaClient,
  sqltag,
  empty,
  join,
  raw,
  skip,
  Decimal,
  Debug,
  objectEnumValues,
  makeStrictEnum,
  Extensions,
  warnOnce,
  defineDmmfProperty,
  Public,
  getRuntime,
  createParam,
} = require('./runtime/edge.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 6.10.1
 * Query Engine version: 9b628578b3b7cae625e8c927178f15a170e74a9c
 */
Prisma.prismaVersion = {
  client: "6.10.1",
  engine: "9b628578b3b7cae625e8c927178f15a170e74a9c"
}

Prisma.PrismaClientKnownRequestError = PrismaClientKnownRequestError;
Prisma.PrismaClientUnknownRequestError = PrismaClientUnknownRequestError
Prisma.PrismaClientRustPanicError = PrismaClientRustPanicError
Prisma.PrismaClientInitializationError = PrismaClientInitializationError
Prisma.PrismaClientValidationError = PrismaClientValidationError
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = sqltag
Prisma.empty = empty
Prisma.join = join
Prisma.raw = raw
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = Extensions.getExtensionContext
Prisma.defineExtension = Extensions.defineExtension

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}





/**
 * Enums
 */
exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
});

exports.Prisma.UserScalarFieldEnum = {
  id: 'id',
  authUserId: 'authUserId',
  username: 'username',
  displayName: 'displayName',
  email: 'email',
  role: 'role',
  status: 'status',
  technicalLevel: 'technicalLevel',
  profilePictureUrl: 'profilePictureUrl',
  bio: 'bio',
  socialLinks: 'socialLinks',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  lastLogin: 'lastLogin',
  bookmarksCount: 'bookmarksCount',
  reputationScore: 'reputationScore',
  requestsFulfilled: 'requestsFulfilled',
  requestsMade: 'requestsMade',
  reviewsCount: 'reviewsCount',
  toolsApproved: 'toolsApproved',
  toolsSubmitted: 'toolsSubmitted'
};

exports.Prisma.EntityTypeScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  slug: 'slug',
  iconUrl: 'iconUrl',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.EntityScalarFieldEnum = {
  id: 'id',
  entityTypeId: 'entityTypeId',
  name: 'name',
  shortDescription: 'shortDescription',
  description: 'description',
  logoUrl: 'logoUrl',
  websiteUrl: 'websiteUrl',
  documentationUrl: 'documentationUrl',
  contactUrl: 'contactUrl',
  privacyPolicyUrl: 'privacyPolicyUrl',
  foundedYear: 'foundedYear',
  socialLinks: 'socialLinks',
  status: 'status',
  avgRating: 'avgRating',
  reviewCount: 'reviewCount',
  upvoteCount: 'upvoteCount',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  legacyId: 'legacyId',
  submitterId: 'submitterId',
  affiliateStatus: 'affiliateStatus',
  locationSummary: 'locationSummary',
  metaDescription: 'metaDescription',
  metaTitle: 'metaTitle',
  refLink: 'refLink',
  scrapedReviewCount: 'scrapedReviewCount',
  scrapedReviewSentimentLabel: 'scrapedReviewSentimentLabel',
  scrapedReviewSentimentScore: 'scrapedReviewSentimentScore',
  employeeCountRange: 'employeeCountRange',
  fundingStage: 'fundingStage',
  slug: 'slug'
};

exports.Prisma.CategoryScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  slug: 'slug',
  iconUrl: 'iconUrl',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  parentId: 'parentId'
};

exports.Prisma.TagScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  slug: 'slug',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.EntityTagScalarFieldEnum = {
  entityId: 'entityId',
  tagId: 'tagId',
  assignedBy: 'assignedBy',
  createdAt: 'createdAt',
  id: 'id'
};

exports.Prisma.EntityCategoryScalarFieldEnum = {
  entityId: 'entityId',
  categoryId: 'categoryId',
  assignedAt: 'assignedAt',
  assignedBy: 'assignedBy'
};

exports.Prisma.ReviewScalarFieldEnum = {
  id: 'id',
  entityId: 'entityId',
  userId: 'userId',
  rating: 'rating',
  title: 'title',
  status: 'status',
  moderationNotes: 'moderationNotes',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  content: 'content',
  downvotes: 'downvotes',
  moderatorId: 'moderatorId',
  upvotes: 'upvotes'
};

exports.Prisma.ReviewVoteScalarFieldEnum = {
  reviewId: 'reviewId',
  userId: 'userId',
  createdAt: 'createdAt',
  id: 'id',
  isUpvote: 'isUpvote'
};

exports.Prisma.EntityDetailsToolScalarFieldEnum = {
  entityId: 'entityId',
  learningCurve: 'learningCurve',
  targetAudience: 'targetAudience',
  keyFeatures: 'keyFeatures',
  useCases: 'useCases',
  pricingModel: 'pricingModel',
  priceRange: 'priceRange',
  pricingDetails: 'pricingDetails',
  pricingUrl: 'pricingUrl',
  hasFreeTier: 'hasFreeTier',
  integrations: 'integrations',
  apiAccess: 'apiAccess',
  communityUrl: 'communityUrl',
  customizationLevel: 'customizationLevel',
  demoAvailable: 'demoAvailable',
  deploymentOptions: 'deploymentOptions',
  frameworks: 'frameworks',
  hasLiveChat: 'hasLiveChat',
  libraries: 'libraries',
  mobileSupport: 'mobileSupport',
  openSource: 'openSource',
  programmingLanguages: 'programmingLanguages',
  supportChannels: 'supportChannels',
  supportEmail: 'supportEmail',
  supportedOs: 'supportedOs',
  trialAvailable: 'trialAvailable',
  hasApi: 'hasApi',
  id: 'id',
  platforms: 'platforms',
  technicalLevel: 'technicalLevel'
};

exports.Prisma.EntityDetailsAgencyScalarFieldEnum = {
  entityId: 'entityId',
  servicesOffered: 'servicesOffered',
  industryFocus: 'industryFocus',
  targetClientSize: 'targetClientSize',
  targetAudience: 'targetAudience',
  locationSummary: 'locationSummary',
  portfolioUrl: 'portfolioUrl',
  pricingInfo: 'pricingInfo',
  id: 'id'
};

exports.Prisma.EntityDetailsContentCreatorScalarFieldEnum = {
  entityId: 'entityId',
  creatorName: 'creatorName',
  primaryPlatform: 'primaryPlatform',
  focusAreas: 'focusAreas',
  followerCount: 'followerCount',
  exampleContentUrl: 'exampleContentUrl',
  id: 'id'
};

exports.Prisma.EntityDetailsCommunityScalarFieldEnum = {
  entityId: 'entityId',
  platform: 'platform',
  memberCount: 'memberCount',
  focusTopics: 'focusTopics',
  rulesUrl: 'rulesUrl',
  inviteUrl: 'inviteUrl',
  mainChannelUrl: 'mainChannelUrl',
  id: 'id'
};

exports.Prisma.EntityDetailsNewsletterScalarFieldEnum = {
  entityId: 'entityId',
  frequency: 'frequency',
  mainTopics: 'mainTopics',
  archiveUrl: 'archiveUrl',
  subscribeUrl: 'subscribeUrl',
  authorName: 'authorName',
  subscriberCount: 'subscriberCount',
  id: 'id'
};

exports.Prisma.EntityDetailsCourseScalarFieldEnum = {
  entityId: 'entityId',
  instructorName: 'instructorName',
  durationText: 'durationText',
  skillLevel: 'skillLevel',
  prerequisites: 'prerequisites',
  syllabusUrl: 'syllabusUrl',
  enrollmentCount: 'enrollmentCount',
  certificateAvailable: 'certificateAvailable',
  id: 'id'
};

exports.Prisma.UserSavedEntityScalarFieldEnum = {
  userId: 'userId',
  entityId: 'entityId',
  createdAt: 'createdAt',
  id: 'id'
};

exports.Prisma.UserUpvoteScalarFieldEnum = {
  userId: 'userId',
  entityId: 'entityId',
  createdAt: 'createdAt'
};

exports.Prisma.UserFollowedTagScalarFieldEnum = {
  userId: 'userId',
  tagId: 'tagId',
  createdAt: 'createdAt',
  id: 'id'
};

exports.Prisma.UserFollowedCategoryScalarFieldEnum = {
  userId: 'userId',
  categoryId: 'categoryId',
  followedAt: 'followedAt'
};

exports.Prisma.UserActivityLogScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  actionType: 'actionType',
  entityId: 'entityId',
  categoryId: 'categoryId',
  tagId: 'tagId',
  reviewId: 'reviewId',
  targetUserId: 'targetUserId',
  details: 'details',
  createdAt: 'createdAt'
};

exports.Prisma.UserNotificationSettingsScalarFieldEnum = {
  userId: 'userId',
  emailNewsletter: 'emailNewsletter',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  emailMarketing: 'emailMarketing',
  emailOnNewEntityInFollowed: 'emailOnNewEntityInFollowed',
  emailOnNewFollower: 'emailOnNewFollower',
  emailOnNewReview: 'emailOnNewReview',
  emailOnReviewResponse: 'emailOnReviewResponse',
  id: 'id'
};

exports.Prisma.BadgeTypeScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  iconUrl: 'iconUrl',
  scope: 'scope',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  criteria: 'criteria'
};

exports.Prisma.UserBadgeScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  badgeTypeId: 'badgeTypeId',
  grantedAt: 'grantedAt',
  notes: 'notes',
  grantedBy: 'grantedBy'
};

exports.Prisma.EntityBadgeScalarFieldEnum = {
  id: 'id',
  entityId: 'entityId',
  badgeTypeId: 'badgeTypeId',
  grantedAt: 'grantedAt',
  notes: 'notes',
  expiresAt: 'expiresAt',
  grantedBy: 'grantedBy'
};

exports.Prisma.EntityDetailsDatasetScalarFieldEnum = {
  entityId: 'entityId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  accessNotes: 'accessNotes',
  description: 'description',
  license: 'license',
  sizeInBytes: 'sizeInBytes',
  sourceUrl: 'sourceUrl',
  collectionMethod: 'collectionMethod',
  id: 'id',
  updateFrequency: 'updateFrequency',
  format: 'format'
};

exports.Prisma.EntityDetailsResearchPaperScalarFieldEnum = {
  entityId: 'entityId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  abstract: 'abstract',
  citationCount: 'citationCount',
  doi: 'doi',
  journalOrConference: 'journalOrConference',
  publicationDate: 'publicationDate',
  id: 'id',
  pdfUrl: 'pdfUrl',
  authors: 'authors',
  researchAreas: 'researchAreas',
  publicationVenues: 'publicationVenues',
  keywords: 'keywords',
  arxivId: 'arxivId'
};

exports.Prisma.EntityDetailsSoftwareScalarFieldEnum = {
  entityId: 'entityId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  currentVersion: 'currentVersion',
  licenseType: 'licenseType',
  communityUrl: 'communityUrl',
  hasFreeTier: 'hasFreeTier',
  hasLiveChat: 'hasLiveChat',
  priceRange: 'priceRange',
  pricingDetails: 'pricingDetails',
  pricingModel: 'pricingModel',
  pricingUrl: 'pricingUrl',
  supportEmail: 'supportEmail',
  apiAccess: 'apiAccess',
  customizationLevel: 'customizationLevel',
  demoAvailable: 'demoAvailable',
  deploymentOptions: 'deploymentOptions',
  frameworks: 'frameworks',
  hasApi: 'hasApi',
  id: 'id',
  keyFeatures: 'keyFeatures',
  libraries: 'libraries',
  mobileSupport: 'mobileSupport',
  openSource: 'openSource',
  supportChannels: 'supportChannels',
  supportedOs: 'supportedOs',
  targetAudience: 'targetAudience',
  trialAvailable: 'trialAvailable',
  integrations: 'integrations',
  platformCompatibility: 'platformCompatibility',
  programmingLanguages: 'programmingLanguages',
  useCases: 'useCases',
  repositoryUrl: 'repositoryUrl',
  releaseDate: 'releaseDate'
};

exports.Prisma.EntityDetailsModelScalarFieldEnum = {
  entityId: 'entityId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  license: 'license',
  modelArchitecture: 'modelArchitecture',
  trainingDataset: 'trainingDataset',
  deploymentOptions: 'deploymentOptions',
  frameworks: 'frameworks',
  id: 'id',
  inputDataTypes: 'inputDataTypes',
  libraries: 'libraries',
  outputDataTypes: 'outputDataTypes',
  performanceMetrics: 'performanceMetrics',
  targetAudience: 'targetAudience',
  useCases: 'useCases'
};

exports.Prisma.EntityDetailsProjectReferenceScalarFieldEnum = {
  entityId: 'entityId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  forks: 'forks',
  id: 'id',
  keyTechnologies: 'keyTechnologies',
  license: 'license',
  repositoryUrl: 'repositoryUrl',
  stars: 'stars',
  status: 'status',
  useCases: 'useCases',
  contributors: 'contributors'
};

exports.Prisma.EntityDetailsServiceProviderScalarFieldEnum = {
  entityId: 'entityId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  companySizeFocus: 'companySizeFocus',
  id: 'id',
  industrySpecializations: 'industrySpecializations',
  locationSummary: 'locationSummary',
  portfolioUrl: 'portfolioUrl',
  pricingInfo: 'pricingInfo',
  servicesOffered: 'servicesOffered',
  targetAudience: 'targetAudience'
};

exports.Prisma.EntityDetailsInvestorScalarFieldEnum = {
  entityId: 'entityId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  contactEmail: 'contactEmail',
  applicationUrl: 'applicationUrl',
  focusAreas: 'focusAreas',
  id: 'id',
  investmentStages: 'investmentStages',
  investorType: 'investorType',
  locationSummary: 'locationSummary',
  notableInvestments: 'notableInvestments',
  portfolioSize: 'portfolioSize'
};

exports.Prisma.EntityDetailsEventScalarFieldEnum = {
  entityId: 'entityId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  endDate: 'endDate',
  location: 'location',
  price: 'price',
  registrationUrl: 'registrationUrl',
  startDate: 'startDate',
  eventType: 'eventType',
  id: 'id',
  isOnline: 'isOnline',
  keySpeakers: 'keySpeakers',
  targetAudience: 'targetAudience',
  topics: 'topics',
  registrationRequired: 'registrationRequired',
  capacity: 'capacity',
  organizer: 'organizer',
  eventFormat: 'eventFormat'
};

exports.Prisma.EntityDetailsJobScalarFieldEnum = {
  entityId: 'entityId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  applicationUrl: 'applicationUrl',
  companyName: 'companyName',
  experienceLevel: 'experienceLevel',
  id: 'id',
  isRemote: 'isRemote',
  jobType: 'jobType',
  keyResponsibilities: 'keyResponsibilities',
  location: 'location',
  requiredSkills: 'requiredSkills',
  salaryMax: 'salaryMax',
  salaryMin: 'salaryMin',
  jobDescription: 'jobDescription',
  employmentTypes: 'employmentTypes',
  locationTypes: 'locationTypes',
  benefits: 'benefits',
  remotePolicy: 'remotePolicy',
  visaSponsorship: 'visaSponsorship'
};

exports.Prisma.EntityDetailsGrantScalarFieldEnum = {
  entityId: 'entityId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  applicationUrl: 'applicationUrl',
  amount: 'amount',
  deadline: 'deadline',
  eligibility: 'eligibility',
  focusAreas: 'focusAreas',
  funderName: 'funderName',
  grantType: 'grantType',
  id: 'id',
  location: 'location'
};

exports.Prisma.EntityDetailsBountyScalarFieldEnum = {
  entityId: 'entityId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  amount: 'amount',
  deadline: 'deadline',
  id: 'id',
  platform: 'platform',
  requiredSkills: 'requiredSkills',
  status: 'status',
  taskDescription: 'taskDescription',
  url: 'url'
};

exports.Prisma.EntityDetailsHardwareScalarFieldEnum = {
  entityId: 'entityId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  availability: 'availability',
  gpu: 'gpu',
  id: 'id',
  memory: 'memory',
  powerConsumption: 'powerConsumption',
  price: 'price',
  processor: 'processor',
  storage: 'storage',
  useCases: 'useCases',
  hardwareType: 'hardwareType',
  manufacturer: 'manufacturer',
  releaseDate: 'releaseDate',
  specifications: 'specifications',
  datasheetUrl: 'datasheetUrl'
};

exports.Prisma.EntityDetailsNewsScalarFieldEnum = {
  entityId: 'entityId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  articleUrl: 'articleUrl',
  author: 'author',
  publicationDate: 'publicationDate',
  sourceName: 'sourceName',
  summary: 'summary',
  id: 'id',
  tags: 'tags'
};

exports.Prisma.EntityDetailsBookScalarFieldEnum = {
  entityId: 'entityId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  isbn: 'isbn',
  pageCount: 'pageCount',
  publisher: 'publisher',
  purchaseUrl: 'purchaseUrl',
  summary: 'summary',
  author: 'author',
  format: 'format',
  id: 'id',
  publicationDate: 'publicationDate'
};

exports.Prisma.EntityDetailsPodcastScalarFieldEnum = {
  entityId: 'entityId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  frequency: 'frequency',
  applePodcastsUrl: 'applePodcastsUrl',
  averageLength: 'averageLength',
  googlePodcastsUrl: 'googlePodcastsUrl',
  host: 'host',
  id: 'id',
  mainTopics: 'mainTopics',
  spotifyUrl: 'spotifyUrl',
  youtubeUrl: 'youtubeUrl'
};

exports.Prisma.EntityDetailsPlatformScalarFieldEnum = {
  entityId: 'entityId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  documentationUrl: 'documentationUrl',
  platformType: 'platformType',
  communityUrl: 'communityUrl',
  hasFreeTier: 'hasFreeTier',
  hasLiveChat: 'hasLiveChat',
  priceRange: 'priceRange',
  pricingDetails: 'pricingDetails',
  pricingUrl: 'pricingUrl',
  supportEmail: 'supportEmail',
  pricingModel: 'pricingModel',
  apiAccess: 'apiAccess',
  customizationLevel: 'customizationLevel',
  demoAvailable: 'demoAvailable',
  deploymentOptions: 'deploymentOptions',
  hasApi: 'hasApi',
  id: 'id',
  mobileSupport: 'mobileSupport',
  openSource: 'openSource',
  supportChannels: 'supportChannels',
  supportedOs: 'supportedOs',
  targetAudience: 'targetAudience',
  trialAvailable: 'trialAvailable',
  integrations: 'integrations',
  keyServices: 'keyServices',
  useCases: 'useCases'
};

exports.Prisma.EntityFeatureScalarFieldEnum = {
  entityId: 'entityId',
  featureId: 'featureId',
  assignedBy: 'assignedBy',
  createdAt: 'createdAt',
  id: 'id'
};

exports.Prisma.AppSettingScalarFieldEnum = {
  key: 'key',
  value: 'value',
  description: 'description',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.FeatureScalarFieldEnum = {
  id: 'id',
  name: 'name',
  slug: 'slug',
  description: 'description',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  iconUrl: 'iconUrl'
};

exports.Prisma.UserPreferencesScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  emailNotifications: 'emailNotifications',
  marketingEmails: 'marketingEmails',
  weeklyDigest: 'weeklyDigest',
  newToolAlerts: 'newToolAlerts',
  profileVisibility: 'profileVisibility',
  showBookmarks: 'showBookmarks',
  showReviews: 'showReviews',
  showActivity: 'showActivity',
  theme: 'theme',
  itemsPerPage: 'itemsPerPage',
  defaultView: 'defaultView',
  preferredCategories: 'preferredCategories',
  blockedCategories: 'blockedCategories',
  contentLanguage: 'contentLanguage',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ToolRequestScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  toolName: 'toolName',
  description: 'description',
  reason: 'reason',
  categorySuggestion: 'categorySuggestion',
  websiteUrl: 'websiteUrl',
  priority: 'priority',
  status: 'status',
  adminNotes: 'adminNotes',
  votes: 'votes',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.UserSubmittedToolScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  entityId: 'entityId',
  submissionStatus: 'submissionStatus',
  submittedAt: 'submittedAt',
  reviewedAt: 'reviewedAt',
  reviewerId: 'reviewerId',
  reviewerNotes: 'reviewerNotes',
  changesRequested: 'changesRequested'
};

exports.Prisma.ProfileActivityScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  type: 'type',
  description: 'description',
  entityId: 'entityId',
  entityName: 'entityName',
  entitySlug: 'entitySlug',
  createdAt: 'createdAt'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.NullableJsonNullValueInput = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull
};

exports.Prisma.QueryMode = {
  default: 'default',
  insensitive: 'insensitive'
};

exports.Prisma.JsonNullValueFilter = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull,
  AnyNull: Prisma.AnyNull
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};

exports.Prisma.UserOrderByRelevanceFieldEnum = {
  id: 'id',
  authUserId: 'authUserId',
  username: 'username',
  displayName: 'displayName',
  email: 'email',
  profilePictureUrl: 'profilePictureUrl',
  bio: 'bio'
};

exports.Prisma.EntityTypeOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  slug: 'slug',
  iconUrl: 'iconUrl'
};

exports.Prisma.EntityOrderByRelevanceFieldEnum = {
  id: 'id',
  entityTypeId: 'entityTypeId',
  name: 'name',
  shortDescription: 'shortDescription',
  description: 'description',
  logoUrl: 'logoUrl',
  websiteUrl: 'websiteUrl',
  documentationUrl: 'documentationUrl',
  contactUrl: 'contactUrl',
  privacyPolicyUrl: 'privacyPolicyUrl',
  legacyId: 'legacyId',
  submitterId: 'submitterId',
  locationSummary: 'locationSummary',
  metaDescription: 'metaDescription',
  metaTitle: 'metaTitle',
  refLink: 'refLink',
  scrapedReviewSentimentLabel: 'scrapedReviewSentimentLabel',
  slug: 'slug'
};

exports.Prisma.CategoryOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  slug: 'slug',
  iconUrl: 'iconUrl',
  parentId: 'parentId'
};

exports.Prisma.TagOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  slug: 'slug'
};

exports.Prisma.EntityTagOrderByRelevanceFieldEnum = {
  entityId: 'entityId',
  tagId: 'tagId',
  assignedBy: 'assignedBy',
  id: 'id'
};

exports.Prisma.EntityCategoryOrderByRelevanceFieldEnum = {
  entityId: 'entityId',
  categoryId: 'categoryId',
  assignedBy: 'assignedBy'
};

exports.Prisma.ReviewOrderByRelevanceFieldEnum = {
  id: 'id',
  entityId: 'entityId',
  userId: 'userId',
  title: 'title',
  moderationNotes: 'moderationNotes',
  content: 'content',
  moderatorId: 'moderatorId'
};

exports.Prisma.ReviewVoteOrderByRelevanceFieldEnum = {
  reviewId: 'reviewId',
  userId: 'userId',
  id: 'id'
};

exports.Prisma.EntityDetailsToolOrderByRelevanceFieldEnum = {
  entityId: 'entityId',
  pricingDetails: 'pricingDetails',
  pricingUrl: 'pricingUrl',
  communityUrl: 'communityUrl',
  customizationLevel: 'customizationLevel',
  supportEmail: 'supportEmail',
  id: 'id'
};

exports.Prisma.EntityDetailsAgencyOrderByRelevanceFieldEnum = {
  entityId: 'entityId',
  locationSummary: 'locationSummary',
  portfolioUrl: 'portfolioUrl',
  pricingInfo: 'pricingInfo',
  id: 'id'
};

exports.Prisma.EntityDetailsContentCreatorOrderByRelevanceFieldEnum = {
  entityId: 'entityId',
  creatorName: 'creatorName',
  primaryPlatform: 'primaryPlatform',
  exampleContentUrl: 'exampleContentUrl',
  id: 'id'
};

exports.Prisma.EntityDetailsCommunityOrderByRelevanceFieldEnum = {
  entityId: 'entityId',
  platform: 'platform',
  rulesUrl: 'rulesUrl',
  inviteUrl: 'inviteUrl',
  mainChannelUrl: 'mainChannelUrl',
  id: 'id'
};

exports.Prisma.EntityDetailsNewsletterOrderByRelevanceFieldEnum = {
  entityId: 'entityId',
  frequency: 'frequency',
  archiveUrl: 'archiveUrl',
  subscribeUrl: 'subscribeUrl',
  authorName: 'authorName',
  id: 'id'
};

exports.Prisma.EntityDetailsCourseOrderByRelevanceFieldEnum = {
  entityId: 'entityId',
  instructorName: 'instructorName',
  durationText: 'durationText',
  prerequisites: 'prerequisites',
  syllabusUrl: 'syllabusUrl',
  id: 'id'
};

exports.Prisma.UserSavedEntityOrderByRelevanceFieldEnum = {
  userId: 'userId',
  entityId: 'entityId',
  id: 'id'
};

exports.Prisma.UserUpvoteOrderByRelevanceFieldEnum = {
  userId: 'userId',
  entityId: 'entityId'
};

exports.Prisma.UserFollowedTagOrderByRelevanceFieldEnum = {
  userId: 'userId',
  tagId: 'tagId',
  id: 'id'
};

exports.Prisma.UserFollowedCategoryOrderByRelevanceFieldEnum = {
  userId: 'userId',
  categoryId: 'categoryId'
};

exports.Prisma.UserActivityLogOrderByRelevanceFieldEnum = {
  id: 'id',
  userId: 'userId',
  entityId: 'entityId',
  categoryId: 'categoryId',
  tagId: 'tagId',
  reviewId: 'reviewId',
  targetUserId: 'targetUserId'
};

exports.Prisma.UserNotificationSettingsOrderByRelevanceFieldEnum = {
  userId: 'userId',
  id: 'id'
};

exports.Prisma.BadgeTypeOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  iconUrl: 'iconUrl'
};

exports.Prisma.UserBadgeOrderByRelevanceFieldEnum = {
  id: 'id',
  userId: 'userId',
  badgeTypeId: 'badgeTypeId',
  notes: 'notes',
  grantedBy: 'grantedBy'
};

exports.Prisma.EntityBadgeOrderByRelevanceFieldEnum = {
  id: 'id',
  entityId: 'entityId',
  badgeTypeId: 'badgeTypeId',
  notes: 'notes',
  grantedBy: 'grantedBy'
};

exports.Prisma.EntityDetailsDatasetOrderByRelevanceFieldEnum = {
  entityId: 'entityId',
  accessNotes: 'accessNotes',
  description: 'description',
  license: 'license',
  sourceUrl: 'sourceUrl',
  collectionMethod: 'collectionMethod',
  id: 'id',
  updateFrequency: 'updateFrequency',
  format: 'format'
};

exports.Prisma.EntityDetailsResearchPaperOrderByRelevanceFieldEnum = {
  entityId: 'entityId',
  abstract: 'abstract',
  doi: 'doi',
  journalOrConference: 'journalOrConference',
  id: 'id',
  pdfUrl: 'pdfUrl',
  authors: 'authors',
  researchAreas: 'researchAreas',
  publicationVenues: 'publicationVenues',
  keywords: 'keywords',
  arxivId: 'arxivId'
};

exports.Prisma.EntityDetailsSoftwareOrderByRelevanceFieldEnum = {
  entityId: 'entityId',
  currentVersion: 'currentVersion',
  licenseType: 'licenseType',
  communityUrl: 'communityUrl',
  pricingDetails: 'pricingDetails',
  pricingUrl: 'pricingUrl',
  supportEmail: 'supportEmail',
  customizationLevel: 'customizationLevel',
  deploymentOptions: 'deploymentOptions',
  frameworks: 'frameworks',
  id: 'id',
  keyFeatures: 'keyFeatures',
  libraries: 'libraries',
  supportChannels: 'supportChannels',
  supportedOs: 'supportedOs',
  targetAudience: 'targetAudience',
  integrations: 'integrations',
  platformCompatibility: 'platformCompatibility',
  programmingLanguages: 'programmingLanguages',
  useCases: 'useCases',
  repositoryUrl: 'repositoryUrl'
};

exports.Prisma.EntityDetailsModelOrderByRelevanceFieldEnum = {
  entityId: 'entityId',
  license: 'license',
  modelArchitecture: 'modelArchitecture',
  trainingDataset: 'trainingDataset',
  deploymentOptions: 'deploymentOptions',
  frameworks: 'frameworks',
  id: 'id',
  inputDataTypes: 'inputDataTypes',
  libraries: 'libraries',
  outputDataTypes: 'outputDataTypes',
  targetAudience: 'targetAudience',
  useCases: 'useCases'
};

exports.Prisma.EntityDetailsProjectReferenceOrderByRelevanceFieldEnum = {
  entityId: 'entityId',
  id: 'id',
  keyTechnologies: 'keyTechnologies',
  license: 'license',
  repositoryUrl: 'repositoryUrl',
  status: 'status',
  useCases: 'useCases'
};

exports.Prisma.EntityDetailsServiceProviderOrderByRelevanceFieldEnum = {
  entityId: 'entityId',
  companySizeFocus: 'companySizeFocus',
  id: 'id',
  industrySpecializations: 'industrySpecializations',
  locationSummary: 'locationSummary',
  portfolioUrl: 'portfolioUrl',
  pricingInfo: 'pricingInfo',
  servicesOffered: 'servicesOffered',
  targetAudience: 'targetAudience'
};

exports.Prisma.EntityDetailsInvestorOrderByRelevanceFieldEnum = {
  entityId: 'entityId',
  contactEmail: 'contactEmail',
  applicationUrl: 'applicationUrl',
  focusAreas: 'focusAreas',
  id: 'id',
  investmentStages: 'investmentStages',
  investorType: 'investorType',
  locationSummary: 'locationSummary',
  notableInvestments: 'notableInvestments'
};

exports.Prisma.EntityDetailsEventOrderByRelevanceFieldEnum = {
  entityId: 'entityId',
  location: 'location',
  price: 'price',
  registrationUrl: 'registrationUrl',
  eventType: 'eventType',
  id: 'id',
  keySpeakers: 'keySpeakers',
  targetAudience: 'targetAudience',
  topics: 'topics',
  organizer: 'organizer'
};

exports.Prisma.EntityDetailsJobOrderByRelevanceFieldEnum = {
  entityId: 'entityId',
  applicationUrl: 'applicationUrl',
  companyName: 'companyName',
  id: 'id',
  jobType: 'jobType',
  keyResponsibilities: 'keyResponsibilities',
  location: 'location',
  requiredSkills: 'requiredSkills',
  jobDescription: 'jobDescription',
  benefits: 'benefits',
  remotePolicy: 'remotePolicy'
};

exports.Prisma.EntityDetailsGrantOrderByRelevanceFieldEnum = {
  entityId: 'entityId',
  applicationUrl: 'applicationUrl',
  amount: 'amount',
  eligibility: 'eligibility',
  focusAreas: 'focusAreas',
  funderName: 'funderName',
  grantType: 'grantType',
  id: 'id',
  location: 'location'
};

exports.Prisma.EntityDetailsBountyOrderByRelevanceFieldEnum = {
  entityId: 'entityId',
  amount: 'amount',
  id: 'id',
  platform: 'platform',
  requiredSkills: 'requiredSkills',
  status: 'status',
  taskDescription: 'taskDescription',
  url: 'url'
};

exports.Prisma.EntityDetailsHardwareOrderByRelevanceFieldEnum = {
  entityId: 'entityId',
  availability: 'availability',
  gpu: 'gpu',
  id: 'id',
  memory: 'memory',
  powerConsumption: 'powerConsumption',
  price: 'price',
  processor: 'processor',
  storage: 'storage',
  useCases: 'useCases',
  manufacturer: 'manufacturer',
  datasheetUrl: 'datasheetUrl'
};

exports.Prisma.EntityDetailsNewsOrderByRelevanceFieldEnum = {
  entityId: 'entityId',
  articleUrl: 'articleUrl',
  author: 'author',
  sourceName: 'sourceName',
  summary: 'summary',
  id: 'id',
  tags: 'tags'
};

exports.Prisma.EntityDetailsBookOrderByRelevanceFieldEnum = {
  entityId: 'entityId',
  isbn: 'isbn',
  publisher: 'publisher',
  purchaseUrl: 'purchaseUrl',
  summary: 'summary',
  author: 'author',
  format: 'format',
  id: 'id'
};

exports.Prisma.EntityDetailsPodcastOrderByRelevanceFieldEnum = {
  entityId: 'entityId',
  frequency: 'frequency',
  applePodcastsUrl: 'applePodcastsUrl',
  averageLength: 'averageLength',
  googlePodcastsUrl: 'googlePodcastsUrl',
  host: 'host',
  id: 'id',
  mainTopics: 'mainTopics',
  spotifyUrl: 'spotifyUrl',
  youtubeUrl: 'youtubeUrl'
};

exports.Prisma.EntityDetailsPlatformOrderByRelevanceFieldEnum = {
  entityId: 'entityId',
  documentationUrl: 'documentationUrl',
  platformType: 'platformType',
  communityUrl: 'communityUrl',
  pricingDetails: 'pricingDetails',
  pricingUrl: 'pricingUrl',
  supportEmail: 'supportEmail',
  customizationLevel: 'customizationLevel',
  deploymentOptions: 'deploymentOptions',
  id: 'id',
  supportChannels: 'supportChannels',
  supportedOs: 'supportedOs',
  targetAudience: 'targetAudience',
  integrations: 'integrations',
  keyServices: 'keyServices',
  useCases: 'useCases'
};

exports.Prisma.EntityFeatureOrderByRelevanceFieldEnum = {
  entityId: 'entityId',
  featureId: 'featureId',
  assignedBy: 'assignedBy',
  id: 'id'
};

exports.Prisma.AppSettingOrderByRelevanceFieldEnum = {
  key: 'key',
  value: 'value',
  description: 'description'
};

exports.Prisma.FeatureOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  slug: 'slug',
  description: 'description',
  iconUrl: 'iconUrl'
};

exports.Prisma.UserPreferencesOrderByRelevanceFieldEnum = {
  id: 'id',
  userId: 'userId',
  preferredCategories: 'preferredCategories',
  blockedCategories: 'blockedCategories',
  contentLanguage: 'contentLanguage'
};

exports.Prisma.ToolRequestOrderByRelevanceFieldEnum = {
  id: 'id',
  userId: 'userId',
  toolName: 'toolName',
  description: 'description',
  reason: 'reason',
  categorySuggestion: 'categorySuggestion',
  websiteUrl: 'websiteUrl',
  adminNotes: 'adminNotes'
};

exports.Prisma.UserSubmittedToolOrderByRelevanceFieldEnum = {
  id: 'id',
  userId: 'userId',
  entityId: 'entityId',
  reviewerId: 'reviewerId',
  reviewerNotes: 'reviewerNotes',
  changesRequested: 'changesRequested'
};

exports.Prisma.ProfileActivityOrderByRelevanceFieldEnum = {
  id: 'id',
  userId: 'userId',
  description: 'description',
  entityId: 'entityId',
  entityName: 'entityName',
  entitySlug: 'entitySlug'
};
exports.UserRole = exports.$Enums.UserRole = {
  USER: 'USER',
  ADMIN: 'ADMIN',
  MODERATOR: 'MODERATOR'
};

exports.UserStatus = exports.$Enums.UserStatus = {
  ACTIVE: 'ACTIVE',
  INACTIVE: 'INACTIVE',
  PENDING: 'PENDING',
  SUSPENDED: 'SUSPENDED',
  DELETED: 'DELETED'
};

exports.TechnicalLevel = exports.$Enums.TechnicalLevel = {
  BEGINNER: 'BEGINNER',
  INTERMEDIATE: 'INTERMEDIATE',
  ADVANCED: 'ADVANCED',
  EXPERT: 'EXPERT'
};

exports.EntityStatus = exports.$Enums.EntityStatus = {
  PENDING: 'PENDING',
  ACTIVE: 'ACTIVE',
  REJECTED: 'REJECTED',
  INACTIVE: 'INACTIVE',
  ARCHIVED: 'ARCHIVED',
  NEEDS_REVISION: 'NEEDS_REVISION'
};

exports.AffiliateStatus = exports.$Enums.AffiliateStatus = {
  NONE: 'NONE',
  APPLIED: 'APPLIED',
  APPROVED: 'APPROVED',
  REJECTED: 'REJECTED'
};

exports.ReviewStatus = exports.$Enums.ReviewStatus = {
  PENDING: 'PENDING',
  APPROVED: 'APPROVED',
  REJECTED: 'REJECTED'
};

exports.LearningCurve = exports.$Enums.LearningCurve = {
  LOW: 'LOW',
  MEDIUM: 'MEDIUM',
  HIGH: 'HIGH'
};

exports.PricingModel = exports.$Enums.PricingModel = {
  FREE: 'FREE',
  FREEMIUM: 'FREEMIUM',
  SUBSCRIPTION: 'SUBSCRIPTION',
  PAY_PER_USE: 'PAY_PER_USE',
  ONE_TIME_PURCHASE: 'ONE_TIME_PURCHASE',
  CONTACT_SALES: 'CONTACT_SALES',
  OPEN_SOURCE: 'OPEN_SOURCE'
};

exports.PriceRange = exports.$Enums.PriceRange = {
  FREE: 'FREE',
  LOW: 'LOW',
  MEDIUM: 'MEDIUM',
  HIGH: 'HIGH',
  ENTERPRISE: 'ENTERPRISE'
};

exports.SkillLevel = exports.$Enums.SkillLevel = {
  BEGINNER: 'BEGINNER',
  INTERMEDIATE: 'INTERMEDIATE',
  ADVANCED: 'ADVANCED',
  EXPERT: 'EXPERT'
};

exports.ActionType = exports.$Enums.ActionType = {
  VIEW_ENTITY: 'VIEW_ENTITY',
  CLICK_ENTITY_LINK: 'CLICK_ENTITY_LINK',
  SAVE_ENTITY: 'SAVE_ENTITY',
  UNSAVE_ENTITY: 'UNSAVE_ENTITY',
  SUBMIT_REVIEW: 'SUBMIT_REVIEW',
  VOTE_REVIEW: 'VOTE_REVIEW',
  FOLLOW_TAG: 'FOLLOW_TAG',
  UNFOLLOW_TAG: 'UNFOLLOW_TAG',
  FOLLOW_CATEGORY: 'FOLLOW_CATEGORY',
  UNFOLLOW_CATEGORY: 'UNFOLLOW_CATEGORY',
  SEARCH: 'SEARCH',
  LOGIN: 'LOGIN',
  LOGOUT: 'LOGOUT',
  SIGNUP: 'SIGNUP',
  UPDATE_PROFILE: 'UPDATE_PROFILE',
  GRANT_BADGE: 'GRANT_BADGE',
  REVOKE_BADGE: 'REVOKE_BADGE'
};

exports.BadgeScope = exports.$Enums.BadgeScope = {
  USER: 'USER',
  ENTITY: 'ENTITY'
};

exports.EmployeeCountRange = exports.$Enums.EmployeeCountRange = {
  C1_10: 'C1_10',
  C11_50: 'C11_50',
  C51_200: 'C51_200',
  C201_500: 'C201_500',
  C501_1000: 'C501_1000',
  C1001_5000: 'C1001_5000',
  C5001_PLUS: 'C5001_PLUS'
};

exports.FundingStage = exports.$Enums.FundingStage = {
  SEED: 'SEED',
  PRE_SEED: 'PRE_SEED',
  SERIES_A: 'SERIES_A',
  SERIES_B: 'SERIES_B',
  SERIES_C: 'SERIES_C',
  SERIES_D_PLUS: 'SERIES_D_PLUS',
  PUBLIC: 'PUBLIC'
};

exports.ProfileVisibility = exports.$Enums.ProfileVisibility = {
  PUBLIC: 'PUBLIC',
  PRIVATE: 'PRIVATE',
  FRIENDS: 'FRIENDS'
};

exports.Theme = exports.$Enums.Theme = {
  LIGHT: 'LIGHT',
  DARK: 'DARK',
  SYSTEM: 'SYSTEM'
};

exports.DefaultView = exports.$Enums.DefaultView = {
  GRID: 'GRID',
  LIST: 'LIST'
};

exports.ToolRequestPriority = exports.$Enums.ToolRequestPriority = {
  LOW: 'LOW',
  MEDIUM: 'MEDIUM',
  HIGH: 'HIGH'
};

exports.ToolRequestStatus = exports.$Enums.ToolRequestStatus = {
  PENDING: 'PENDING',
  UNDER_REVIEW: 'UNDER_REVIEW',
  APPROVED: 'APPROVED',
  REJECTED: 'REJECTED',
  COMPLETED: 'COMPLETED'
};

exports.SubmissionStatus = exports.$Enums.SubmissionStatus = {
  PENDING: 'PENDING',
  UNDER_REVIEW: 'UNDER_REVIEW',
  APPROVED: 'APPROVED',
  REJECTED: 'REJECTED',
  PUBLISHED: 'PUBLISHED'
};

exports.ProfileActivityType = exports.$Enums.ProfileActivityType = {
  BOOKMARK: 'BOOKMARK',
  REVIEW: 'REVIEW',
  SUBMISSION: 'SUBMISSION',
  REQUEST: 'REQUEST',
  VOTE: 'VOTE'
};

exports.HardwareTypeEnum = exports.$Enums.HardwareTypeEnum = {
  GPU: 'GPU',
  CPU: 'CPU',
  FPGA: 'FPGA',
  TPU: 'TPU',
  ASIC: 'ASIC',
  NPU: 'NPU',
  Memory: 'Memory',
  Storage: 'Storage'
};

exports.EmploymentTypeEnum = exports.$Enums.EmploymentTypeEnum = {
  FULL_TIME: 'FULL_TIME',
  PART_TIME: 'PART_TIME',
  CONTRACT: 'CONTRACT',
  FREELANCE: 'FREELANCE',
  INTERNSHIP: 'INTERNSHIP',
  TEMPORARY: 'TEMPORARY'
};

exports.ExperienceLevelEnum = exports.$Enums.ExperienceLevelEnum = {
  ENTRY: 'ENTRY',
  JUNIOR: 'JUNIOR',
  MID: 'MID',
  SENIOR: 'SENIOR',
  LEAD: 'LEAD',
  PRINCIPAL: 'PRINCIPAL',
  DIRECTOR: 'DIRECTOR'
};

exports.LocationTypeEnum = exports.$Enums.LocationTypeEnum = {
  Remote: 'Remote',
  On_site: 'On_site',
  Hybrid: 'Hybrid'
};

exports.EventFormatEnum = exports.$Enums.EventFormatEnum = {
  in_person: 'in_person',
  virtual: 'virtual',
  hybrid: 'hybrid'
};

exports.Prisma.ModelName = {
  User: 'User',
  EntityType: 'EntityType',
  Entity: 'Entity',
  Category: 'Category',
  Tag: 'Tag',
  EntityTag: 'EntityTag',
  EntityCategory: 'EntityCategory',
  Review: 'Review',
  ReviewVote: 'ReviewVote',
  EntityDetailsTool: 'EntityDetailsTool',
  EntityDetailsAgency: 'EntityDetailsAgency',
  EntityDetailsContentCreator: 'EntityDetailsContentCreator',
  EntityDetailsCommunity: 'EntityDetailsCommunity',
  EntityDetailsNewsletter: 'EntityDetailsNewsletter',
  EntityDetailsCourse: 'EntityDetailsCourse',
  UserSavedEntity: 'UserSavedEntity',
  UserUpvote: 'UserUpvote',
  UserFollowedTag: 'UserFollowedTag',
  UserFollowedCategory: 'UserFollowedCategory',
  UserActivityLog: 'UserActivityLog',
  UserNotificationSettings: 'UserNotificationSettings',
  BadgeType: 'BadgeType',
  UserBadge: 'UserBadge',
  EntityBadge: 'EntityBadge',
  EntityDetailsDataset: 'EntityDetailsDataset',
  EntityDetailsResearchPaper: 'EntityDetailsResearchPaper',
  EntityDetailsSoftware: 'EntityDetailsSoftware',
  EntityDetailsModel: 'EntityDetailsModel',
  EntityDetailsProjectReference: 'EntityDetailsProjectReference',
  EntityDetailsServiceProvider: 'EntityDetailsServiceProvider',
  EntityDetailsInvestor: 'EntityDetailsInvestor',
  EntityDetailsEvent: 'EntityDetailsEvent',
  EntityDetailsJob: 'EntityDetailsJob',
  EntityDetailsGrant: 'EntityDetailsGrant',
  EntityDetailsBounty: 'EntityDetailsBounty',
  EntityDetailsHardware: 'EntityDetailsHardware',
  EntityDetailsNews: 'EntityDetailsNews',
  EntityDetailsBook: 'EntityDetailsBook',
  EntityDetailsPodcast: 'EntityDetailsPodcast',
  EntityDetailsPlatform: 'EntityDetailsPlatform',
  EntityFeature: 'EntityFeature',
  AppSetting: 'AppSetting',
  Feature: 'Feature',
  UserPreferences: 'UserPreferences',
  ToolRequest: 'ToolRequest',
  UserSubmittedTool: 'UserSubmittedTool',
  ProfileActivity: 'ProfileActivity'
};
/**
 * Create the Client
 */
const config = {
  "generator": {
    "name": "client",
    "provider": {
      "fromEnvVar": null,
      "value": "prisma-client-js"
    },
    "output": {
      "value": "/Users/<USER>/code-server/AI Nav Backend/generated/prisma",
      "fromEnvVar": null
    },
    "config": {
      "engineType": "library"
    },
    "binaryTargets": [
      {
        "fromEnvVar": null,
        "value": "darwin-arm64",
        "native": true
      },
      {
        "fromEnvVar": null,
        "value": "debian-openssl-3.0.x"
      }
    ],
    "previewFeatures": [
      "fullTextSearchPostgres",
      "multiSchema"
    ],
    "sourceFilePath": "/Users/<USER>/code-server/AI Nav Backend/prisma/schema.prisma",
    "isCustomOutput": true
  },
  "relativeEnvPaths": {
    "rootEnvPath": null,
    "schemaEnvPath": "../../.env"
  },
  "relativePath": "../../prisma",
  "clientVersion": "6.10.1",
  "engineVersion": "9b628578b3b7cae625e8c927178f15a170e74a9c",
  "datasourceNames": [
    "db"
  ],
  "activeProvider": "postgresql",
  "postinstall": false,
  "inlineDatasources": {
    "db": {
      "url": {
        "fromEnvVar": "DATABASE_URL",
        "value": null
      }
    }
  },
  "inlineSchema": "generator client {\n  provider        = \"prisma-client-js\"\n  output          = \"../generated/prisma\"\n  previewFeatures = [\"fullTextSearchPostgres\", \"multiSchema\"]\n  binaryTargets   = [\"native\", \"debian-openssl-3.0.x\"]\n}\n\ndatasource db {\n  provider = \"postgresql\"\n  url      = env(\"DATABASE_URL\")\n  schemas  = [\"public\"]\n}\n\nmodel User {\n  id                       String                    @id @default(uuid()) @db.Uuid\n  authUserId               String                    @unique @map(\"auth_user_id\") @db.Uuid\n  username                 String?                   @unique\n  displayName              String?                   @map(\"display_name\")\n  email                    String                    @unique\n  role                     UserRole                  @default(USER)\n  status                   UserStatus                @default(ACTIVE)\n  technicalLevel           TechnicalLevel?           @map(\"technical_level\")\n  profilePictureUrl        String?                   @map(\"profile_picture_url\")\n  bio                      String?\n  socialLinks              Json?                     @map(\"social_links\")\n  createdAt                DateTime                  @default(now()) @map(\"created_at\")\n  updatedAt                DateTime                  @updatedAt @map(\"updated_at\")\n  lastLogin                DateTime?                 @map(\"last_login\")\n  bookmarksCount           Int                       @default(0) @map(\"bookmarks_count\")\n  reputationScore          Int                       @default(0) @map(\"reputation_score\")\n  requestsFulfilled        Int                       @default(0) @map(\"requests_fulfilled\")\n  requestsMade             Int                       @default(0) @map(\"requests_made\")\n  reviewsCount             Int                       @default(0) @map(\"reviews_count\")\n  toolsApproved            Int                       @default(0) @map(\"tools_approved\")\n  toolsSubmitted           Int                       @default(0) @map(\"tools_submitted\")\n  submittedEntities        Entity[]                  @relation(\"Submitter\")\n  entityBadgesGranted      EntityBadge[]             @relation(\"EntityBadgesGranted\")\n  profileActivities        ProfileActivity[]\n  reviewVotes              ReviewVote[]              @relation(\"UserReviewVotes\")\n  reviewsModerated         Review[]                  @relation(\"ModeratorReviews\")\n  reviews                  Review[]\n  toolRequests             ToolRequest[]\n  userActivityLogTargets   UserActivityLog[]         @relation(\"TargetUserLogs\")\n  userActivityLogs         UserActivityLog[]         @relation(\"UserLogs\")\n  badgesGranted            UserBadge[]               @relation(\"UserBadgesGranted\")\n  userBadges               UserBadge[]               @relation(\"UserBadges\")\n  userFollowedCategories   UserFollowedCategory[]    @relation(\"UserFollowedCategories\")\n  userFollowedTags         UserFollowedTag[]         @relation(\"UserFollowedTags\")\n  userNotificationSettings UserNotificationSettings?\n  userPreferences          UserPreferences?\n  userSavedEntities        UserSavedEntity[]         @relation(\"UserSavedEntities\")\n  userUpvotes              UserUpvote[]\n  reviewedSubmissions      UserSubmittedTool[]       @relation(\"ReviewerSubmissions\")\n  userSubmittedTools       UserSubmittedTool[]\n\n  @@map(\"users\")\n  @@schema(\"public\")\n}\n\nmodel EntityType {\n  id          String   @id @default(uuid()) @db.Uuid\n  name        String   @unique\n  description String?\n  slug        String   @unique\n  iconUrl     String?  @map(\"icon_url\")\n  createdAt   DateTime @default(now()) @map(\"created_at\")\n  updatedAt   DateTime @updatedAt @map(\"updated_at\")\n  entities    Entity[]\n\n  @@map(\"entity_types\")\n  @@schema(\"public\")\n}\n\nmodel Entity {\n  id                            String                         @id @default(uuid()) @db.Uuid\n  entityTypeId                  String                         @map(\"entity_type_id\") @db.Uuid\n  name                          String                         @unique\n  shortDescription              String?                        @map(\"short_description\")\n  description                   String?\n  logoUrl                       String?                        @map(\"logo_url\")\n  websiteUrl                    String?                        @map(\"website_url\")\n  documentationUrl              String?                        @map(\"documentation_url\")\n  contactUrl                    String?                        @map(\"contact_url\")\n  privacyPolicyUrl              String?                        @map(\"privacy_policy_url\")\n  foundedYear                   Int?                           @map(\"founded_year\")\n  socialLinks                   Json?                          @map(\"social_links\")\n  status                        EntityStatus                   @default(PENDING)\n  avgRating                     Float                          @default(0) @map(\"avg_rating\")\n  reviewCount                   Int                            @default(0) @map(\"review_count\")\n  upvoteCount                   Int                            @default(0) @map(\"upvote_count\")\n  createdAt                     DateTime                       @default(now()) @map(\"created_at\")\n  updatedAt                     DateTime                       @updatedAt @map(\"updated_at\")\n  legacyId                      String?                        @map(\"legacy_id\")\n  submitterId                   String                         @map(\"submitter_id\") @db.Uuid\n  affiliateStatus               AffiliateStatus?               @default(NONE)\n  locationSummary               String?                        @map(\"location_summary\")\n  metaDescription               String?                        @map(\"meta_description\")\n  metaTitle                     String?                        @map(\"meta_title\")\n  refLink                       String?                        @map(\"ref_link\")\n  scrapedReviewCount            Int?                           @map(\"scraped_review_count\")\n  scrapedReviewSentimentLabel   String?                        @map(\"scraped_review_sentiment_label\")\n  scrapedReviewSentimentScore   Float?                         @map(\"scraped_review_sentiment_score\")\n  vectorEmbedding               Unsupported(\"vector\")?         @map(\"vector_embedding\")\n  ftsDocument                   Unsupported(\"tsvector\")?       @map(\"ftsDocument\")\n  employeeCountRange            EmployeeCountRange?            @map(\"employee_count_range\")\n  fundingStage                  FundingStage?                  @map(\"funding_stage\")\n  slug                          String                         @unique\n  entityType                    EntityType                     @relation(fields: [entityTypeId], references: [id])\n  submitter                     User                           @relation(\"Submitter\", fields: [submitterId], references: [id])\n  entityBadges                  EntityBadge[]\n  entityCategories              EntityCategory[]\n  entityDetailsAgency           EntityDetailsAgency?\n  entityDetailsBook             EntityDetailsBook?\n  entityDetailsBounty           EntityDetailsBounty?\n  entityDetailsCommunity        EntityDetailsCommunity?\n  entityDetailsContentCreator   EntityDetailsContentCreator?\n  entityDetailsCourse           EntityDetailsCourse?\n  entityDetailsDataset          EntityDetailsDataset?\n  entityDetailsEvent            EntityDetailsEvent?\n  entityDetailsGrant            EntityDetailsGrant?\n  entityDetailsHardware         EntityDetailsHardware?\n  entityDetailsInvestor         EntityDetailsInvestor?\n  entityDetailsJob              EntityDetailsJob?\n  entityDetailsModel            EntityDetailsModel?\n  entityDetailsNews             EntityDetailsNews?\n  entityDetailsNewsletter       EntityDetailsNewsletter?\n  entityDetailsPlatform         EntityDetailsPlatform?\n  entityDetailsPodcast          EntityDetailsPodcast?\n  entityDetailsProjectReference EntityDetailsProjectReference?\n  entityDetailsResearchPaper    EntityDetailsResearchPaper?\n  entityDetailsServiceProvider  EntityDetailsServiceProvider?\n  entityDetailsSoftware         EntityDetailsSoftware?\n  entityDetailsTool             EntityDetailsTool?\n  entityFeatures                EntityFeature[]\n  entityTags                    EntityTag[]\n  profileActivities             ProfileActivity[]\n  reviews                       Review[]\n  userActivityLogs              UserActivityLog[]              @relation(\"EntityLogs\")\n  userSavedEntities             UserSavedEntity[]\n  userUpvotes                   UserUpvote[]\n  userSubmittedTools            UserSubmittedTool[]\n\n  @@map(\"entities\")\n  @@schema(\"public\")\n}\n\nmodel Category {\n  id                     String                 @id @default(uuid()) @db.Uuid\n  name                   String                 @unique\n  description            String?\n  slug                   String                 @unique\n  iconUrl                String?                @map(\"icon_url\")\n  createdAt              DateTime               @default(now()) @map(\"created_at\")\n  updatedAt              DateTime               @updatedAt @map(\"updated_at\")\n  parentId               String?                @map(\"parent_id\") @db.Uuid\n  parent                 Category?              @relation(\"CategoryToParent\", fields: [parentId], references: [id])\n  children               Category[]             @relation(\"CategoryToParent\")\n  entityCategories       EntityCategory[]\n  userActivityLogs       UserActivityLog[]\n  userFollowedCategories UserFollowedCategory[]\n\n  @@map(\"categories\")\n  @@schema(\"public\")\n}\n\nmodel Tag {\n  id               String            @id @default(uuid()) @db.Uuid\n  name             String            @unique\n  description      String?\n  slug             String            @unique\n  createdAt        DateTime          @default(now()) @map(\"created_at\")\n  updatedAt        DateTime          @updatedAt @map(\"updated_at\")\n  entityTags       EntityTag[]\n  userActivityLogs UserActivityLog[] @relation(\"TagLogs\")\n  userFollowed     UserFollowedTag[]\n\n  @@map(\"tags\")\n  @@schema(\"public\")\n}\n\nmodel EntityTag {\n  entityId   String   @map(\"entity_id\") @db.Uuid\n  tagId      String   @map(\"tag_id\") @db.Uuid\n  assignedBy String   @map(\"assigned_by\") @db.Uuid\n  createdAt  DateTime @default(now()) @map(\"created_at\")\n  id         String   @id @default(uuid()) @db.Uuid\n  entity     Entity   @relation(fields: [entityId], references: [id], onDelete: Cascade)\n  tag        Tag      @relation(fields: [tagId], references: [id], onDelete: Cascade)\n\n  @@unique([entityId, tagId], map: \"uq_entity_tag\")\n  @@map(\"entity_tags\")\n  @@schema(\"public\")\n}\n\nmodel EntityCategory {\n  entityId   String   @map(\"entity_id\") @db.Uuid\n  categoryId String   @map(\"category_id\") @db.Uuid\n  assignedAt DateTime @default(now()) @map(\"assigned_at\")\n  assignedBy String   @map(\"assigned_by\") @db.Uuid\n  category   Category @relation(fields: [categoryId], references: [id], onDelete: Cascade)\n  entity     Entity   @relation(fields: [entityId], references: [id], onDelete: Cascade)\n\n  @@id([entityId, categoryId])\n  @@map(\"entity_categories\")\n  @@schema(\"public\")\n}\n\nmodel Review {\n  id               String            @id @default(uuid()) @db.Uuid\n  entityId         String            @map(\"entity_id\") @db.Uuid\n  userId           String            @map(\"user_id\") @db.Uuid\n  rating           Int\n  title            String?\n  status           ReviewStatus      @default(PENDING)\n  moderationNotes  String?           @map(\"moderation_notes\")\n  createdAt        DateTime          @default(now()) @map(\"created_at\")\n  updatedAt        DateTime          @updatedAt @map(\"updated_at\")\n  content          String?\n  downvotes        Int               @default(0)\n  moderatorId      String?           @map(\"moderator_id\") @db.Uuid\n  upvotes          Int               @default(0)\n  reviewVotes      ReviewVote[]\n  entity           Entity            @relation(fields: [entityId], references: [id], onDelete: Cascade)\n  moderator        User?             @relation(\"ModeratorReviews\", fields: [moderatorId], references: [id])\n  user             User              @relation(fields: [userId], references: [id], onDelete: Cascade)\n  userActivityLogs UserActivityLog[] @relation(\"ReviewLogs\")\n\n  @@unique([entityId, userId], map: \"uq_review_entity_user\")\n  @@map(\"reviews\")\n  @@schema(\"public\")\n}\n\nmodel ReviewVote {\n  reviewId  String   @map(\"review_id\") @db.Uuid\n  userId    String   @map(\"user_id\") @db.Uuid\n  createdAt DateTime @default(now()) @map(\"created_at\")\n  id        String   @id @default(uuid()) @db.Uuid\n  isUpvote  Boolean  @map(\"is_upvote\")\n  review    Review   @relation(fields: [reviewId], references: [id], onDelete: Cascade)\n  user      User     @relation(\"UserReviewVotes\", fields: [userId], references: [id], onDelete: Cascade)\n\n  @@unique([reviewId, userId], map: \"uq_review_vote_user\")\n  @@map(\"review_votes\")\n  @@schema(\"public\")\n}\n\nmodel EntityDetailsTool {\n  entityId             String          @unique @map(\"entity_id\") @db.Uuid\n  learningCurve        LearningCurve?  @map(\"learning_curve\")\n  targetAudience       Json?           @map(\"target_audience\")\n  keyFeatures          Json?           @map(\"key_features\")\n  useCases             Json?           @map(\"use_cases\")\n  pricingModel         PricingModel?   @map(\"pricing_model\")\n  priceRange           PriceRange?     @map(\"price_range\")\n  pricingDetails       String?         @map(\"pricing_details\")\n  pricingUrl           String?         @map(\"pricing_url\")\n  hasFreeTier          Boolean?        @map(\"has_free_tier\")\n  integrations         Json?\n  apiAccess            Boolean?        @map(\"api_access\")\n  communityUrl         String?         @map(\"community_url\")\n  customizationLevel   String?         @map(\"customization_level\")\n  demoAvailable        Boolean?        @map(\"demo_available\")\n  deploymentOptions    Json?           @map(\"deployment_options\")\n  frameworks           Json?\n  hasLiveChat          Boolean?        @map(\"has_live_chat\")\n  libraries            Json?\n  mobileSupport        Boolean?        @map(\"mobile_support\")\n  openSource           Boolean?        @map(\"open_source\")\n  programmingLanguages Json?           @map(\"programming_languages\")\n  supportChannels      Json?           @map(\"support_channels\")\n  supportEmail         String?         @map(\"support_email\")\n  supportedOs          Json?           @map(\"supported_os\")\n  trialAvailable       Boolean?        @map(\"trial_available\")\n  hasApi               Boolean?        @map(\"has_api\")\n  id                   String          @id @default(uuid()) @db.Uuid\n  platforms            Json?\n  technicalLevel       TechnicalLevel? @map(\"technical_level\")\n  entity               Entity          @relation(fields: [entityId], references: [id], onDelete: Cascade)\n\n  @@map(\"entity_details_tool\")\n  @@schema(\"public\")\n}\n\nmodel EntityDetailsAgency {\n  entityId         String  @unique @map(\"entity_id\") @db.Uuid\n  servicesOffered  Json?   @map(\"services_offered\")\n  industryFocus    Json?   @map(\"industry_focus\")\n  targetClientSize Json?   @map(\"target_client_size\")\n  targetAudience   Json?   @map(\"target_audience\")\n  locationSummary  String? @map(\"location_summary\")\n  portfolioUrl     String? @map(\"portfolio_url\")\n  pricingInfo      String? @map(\"pricing_info\")\n  id               String  @id @default(uuid()) @db.Uuid\n  entity           Entity  @relation(fields: [entityId], references: [id], onDelete: Cascade)\n\n  @@map(\"entity_details_agency\")\n  @@schema(\"public\")\n}\n\nmodel EntityDetailsContentCreator {\n  entityId          String  @unique @map(\"entity_id\") @db.Uuid\n  creatorName       String? @map(\"creator_name\")\n  primaryPlatform   String? @map(\"primary_platform\")\n  focusAreas        Json?   @map(\"focus_areas\")\n  followerCount     Int?    @map(\"follower_count\")\n  exampleContentUrl String? @map(\"example_content_url\")\n  id                String  @id @default(uuid()) @db.Uuid\n  entity            Entity  @relation(fields: [entityId], references: [id], onDelete: Cascade)\n\n  @@map(\"entity_details_content_creator\")\n  @@schema(\"public\")\n}\n\nmodel EntityDetailsCommunity {\n  entityId       String  @unique @map(\"entity_id\") @db.Uuid\n  platform       String?\n  memberCount    Int?    @map(\"member_count\")\n  focusTopics    Json?   @map(\"focus_topics\")\n  rulesUrl       String? @map(\"rules_url\")\n  inviteUrl      String? @map(\"invite_url\")\n  mainChannelUrl String? @map(\"main_channel_url\")\n  id             String  @id @default(uuid()) @db.Uuid\n  entity         Entity  @relation(fields: [entityId], references: [id], onDelete: Cascade)\n\n  @@map(\"entity_details_community\")\n  @@schema(\"public\")\n}\n\nmodel EntityDetailsNewsletter {\n  entityId        String  @unique @map(\"entity_id\") @db.Uuid\n  frequency       String?\n  mainTopics      Json?   @map(\"main_topics\")\n  archiveUrl      String? @map(\"archive_url\")\n  subscribeUrl    String? @map(\"subscribe_url\")\n  authorName      String? @map(\"author_name\")\n  subscriberCount Int?    @default(0) @map(\"subscriber_count\")\n  id              String  @id @default(uuid()) @db.Uuid\n  entity          Entity  @relation(fields: [entityId], references: [id], onDelete: Cascade)\n\n  @@map(\"entity_details_newsletter\")\n  @@schema(\"public\")\n}\n\nmodel EntityDetailsCourse {\n  entityId             String      @unique @map(\"entity_id\") @db.Uuid\n  instructorName       String?     @map(\"instructor_name\")\n  durationText         String?     @map(\"duration_text\")\n  skillLevel           SkillLevel? @map(\"skill_level\")\n  prerequisites        String?\n  syllabusUrl          String?     @map(\"syllabus_url\")\n  enrollmentCount      Int?        @map(\"enrollment_count\")\n  certificateAvailable Boolean?    @default(false) @map(\"certificate_available\")\n  id                   String      @id @default(uuid()) @db.Uuid\n  entity               Entity      @relation(fields: [entityId], references: [id], onDelete: Cascade)\n\n  @@map(\"entity_details_course\")\n  @@schema(\"public\")\n}\n\nmodel UserSavedEntity {\n  userId    String   @map(\"user_id\") @db.Uuid\n  entityId  String   @map(\"entity_id\") @db.Uuid\n  createdAt DateTime @default(now()) @map(\"created_at\")\n  id        String   @id @default(uuid()) @db.Uuid\n  entity    Entity   @relation(fields: [entityId], references: [id], onDelete: Cascade)\n  user      User     @relation(\"UserSavedEntities\", fields: [userId], references: [id], onDelete: Cascade)\n\n  @@unique([userId, entityId], map: \"uq_user_saved_entity\")\n  @@map(\"user_saved_entities\")\n  @@schema(\"public\")\n}\n\nmodel UserUpvote {\n  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)\n  userId    String   @map(\"user_id\") @db.Uuid\n  entity    Entity   @relation(fields: [entityId], references: [id], onDelete: Cascade)\n  entityId  String   @map(\"entity_id\") @db.Uuid\n  createdAt DateTime @default(now()) @map(\"created_at\")\n\n  @@id([userId, entityId]) // Compound primary key ensures a user can only upvote an entity once\n  @@map(\"user_upvotes\")\n  @@schema(\"public\")\n}\n\nmodel UserFollowedTag {\n  userId    String   @map(\"user_id\") @db.Uuid\n  tagId     String   @map(\"tag_id\") @db.Uuid\n  createdAt DateTime @default(now()) @map(\"created_at\")\n  id        String   @id @default(uuid()) @db.Uuid\n  tag       Tag      @relation(fields: [tagId], references: [id], onDelete: Cascade)\n  user      User     @relation(\"UserFollowedTags\", fields: [userId], references: [id], onDelete: Cascade)\n\n  @@unique([userId, tagId], map: \"uq_user_followed_tag\")\n  @@map(\"user_followed_tags\")\n  @@schema(\"public\")\n}\n\nmodel UserFollowedCategory {\n  userId     String   @map(\"user_id\") @db.Uuid\n  categoryId String   @map(\"category_id\") @db.Uuid\n  followedAt DateTime @default(now()) @map(\"followed_at\")\n  category   Category @relation(fields: [categoryId], references: [id], onDelete: Cascade)\n  user       User     @relation(\"UserFollowedCategories\", fields: [userId], references: [id], onDelete: Cascade)\n\n  @@id([userId, categoryId])\n  @@map(\"user_followed_categories\")\n  @@schema(\"public\")\n}\n\nmodel UserActivityLog {\n  id           String     @id @default(uuid()) @db.Uuid\n  userId       String     @map(\"user_id\") @db.Uuid\n  actionType   ActionType @map(\"action_type\")\n  entityId     String?    @map(\"entity_id\") @db.Uuid\n  categoryId   String?    @map(\"category_id\") @db.Uuid\n  tagId        String?    @map(\"tag_id\") @db.Uuid\n  reviewId     String?    @map(\"review_id\") @db.Uuid\n  targetUserId String?    @map(\"target_user_id\") @db.Uuid\n  details      Json?\n  createdAt    DateTime   @default(now()) @map(\"created_at\")\n  category     Category?  @relation(fields: [categoryId], references: [id])\n  entity       Entity?    @relation(\"EntityLogs\", fields: [entityId], references: [id])\n  review       Review?    @relation(\"ReviewLogs\", fields: [reviewId], references: [id])\n  tag          Tag?       @relation(\"TagLogs\", fields: [tagId], references: [id])\n  targetUser   User?      @relation(\"TargetUserLogs\", fields: [targetUserId], references: [id])\n  user         User       @relation(\"UserLogs\", fields: [userId], references: [id], onDelete: Cascade)\n\n  @@map(\"user_activity_logs\")\n  @@schema(\"public\")\n}\n\nmodel UserNotificationSettings {\n  userId                     String   @unique @map(\"user_id\") @db.Uuid\n  emailNewsletter            Boolean  @default(true) @map(\"email_newsletter\")\n  createdAt                  DateTime @default(now()) @map(\"created_at\")\n  updatedAt                  DateTime @updatedAt @map(\"updated_at\")\n  emailMarketing             Boolean  @default(true) @map(\"email_marketing\")\n  emailOnNewEntityInFollowed Boolean  @default(true) @map(\"email_on_new_entity_in_followed\")\n  emailOnNewFollower         Boolean  @default(true) @map(\"email_on_new_follower\")\n  emailOnNewReview           Boolean  @default(true) @map(\"email_on_new_review\")\n  emailOnReviewResponse      Boolean  @default(true) @map(\"email_on_review_response\")\n  id                         String   @id @default(uuid()) @db.Uuid\n  user                       User     @relation(fields: [userId], references: [id], onDelete: Cascade)\n\n  @@map(\"user_notification_settings\")\n  @@schema(\"public\")\n}\n\nmodel BadgeType {\n  id           String        @id @default(uuid()) @db.Uuid\n  name         String        @unique\n  description  String?\n  iconUrl      String?       @map(\"icon_url\")\n  scope        BadgeScope\n  createdAt    DateTime      @default(now()) @map(\"created_at\")\n  updatedAt    DateTime      @updatedAt @map(\"updated_at\")\n  criteria     Json?\n  entityBadges EntityBadge[] @relation(\"BadgeTypeEntity\")\n  userBadges   UserBadge[]   @relation(\"BadgeTypeUser\")\n\n  @@map(\"badge_types\")\n  @@schema(\"public\")\n}\n\nmodel UserBadge {\n  id            String    @id @default(uuid()) @db.Uuid\n  userId        String    @map(\"user_id\") @db.Uuid\n  badgeTypeId   String    @map(\"badge_type_id\") @db.Uuid\n  grantedAt     DateTime  @default(now()) @map(\"granted_at\")\n  notes         String?\n  grantedBy     String?   @map(\"granted_by\") @db.Uuid\n  badgeType     BadgeType @relation(\"BadgeTypeUser\", fields: [badgeTypeId], references: [id], onDelete: Cascade)\n  grantedByUser User?     @relation(\"UserBadgesGranted\", fields: [grantedBy], references: [id])\n  user          User      @relation(\"UserBadges\", fields: [userId], references: [id], onDelete: Cascade)\n\n  @@unique([userId, badgeTypeId], map: \"uq_user_badge\")\n  @@map(\"user_badges\")\n  @@schema(\"public\")\n}\n\nmodel EntityBadge {\n  id            String    @id @default(uuid()) @db.Uuid\n  entityId      String    @map(\"entity_id\") @db.Uuid\n  badgeTypeId   String    @map(\"badge_type_id\") @db.Uuid\n  grantedAt     DateTime  @default(now()) @map(\"granted_at\")\n  notes         String?\n  expiresAt     DateTime? @map(\"expires_at\")\n  grantedBy     String?   @map(\"granted_by\") @db.Uuid\n  badgeType     BadgeType @relation(\"BadgeTypeEntity\", fields: [badgeTypeId], references: [id], onDelete: Cascade)\n  entity        Entity    @relation(fields: [entityId], references: [id], onDelete: Cascade)\n  grantedByUser User?     @relation(\"EntityBadgesGranted\", fields: [grantedBy], references: [id])\n\n  @@unique([entityId, badgeTypeId], map: \"uq_entity_badge\")\n  @@map(\"entity_badges\")\n  @@schema(\"public\")\n}\n\nmodel EntityDetailsDataset {\n  entityId         String   @unique @map(\"entity_id\") @db.Uuid\n  createdAt        DateTime @default(now()) @map(\"created_at\")\n  updatedAt        DateTime @updatedAt @map(\"updated_at\")\n  accessNotes      String?  @map(\"access_notes\")\n  description      String?\n  license          String?\n  sizeInBytes      BigInt?  @map(\"size_in_bytes\")\n  sourceUrl        String?  @map(\"source_url\")\n  collectionMethod String?  @map(\"collection_method\")\n  id               String   @id @default(uuid()) @db.Uuid\n  updateFrequency  String?  @map(\"update_frequency\")\n  format           String?\n  entity           Entity   @relation(fields: [entityId], references: [id], onDelete: Cascade)\n\n  @@map(\"entity_details_dataset\")\n  @@schema(\"public\")\n}\n\nmodel EntityDetailsResearchPaper {\n  entityId            String    @unique @map(\"entity_id\") @db.Uuid\n  createdAt           DateTime  @default(now()) @map(\"created_at\")\n  updatedAt           DateTime  @updatedAt @map(\"updated_at\")\n  abstract            String?\n  citationCount       Int?      @map(\"citation_count\")\n  doi                 String?\n  journalOrConference String?   @map(\"journal_or_conference\")\n  publicationDate     DateTime? @map(\"publication_date\")\n  id                  String    @id @default(uuid()) @db.Uuid\n  pdfUrl              String?   @map(\"pdf_url\")\n  authors             String[]\n  researchAreas       String[]  @map(\"research_areas\")\n  publicationVenues   String[]  @map(\"publication_venues\")\n  keywords            String[]\n  arxivId             String?   @map(\"arxiv_id\")\n  entity              Entity    @relation(fields: [entityId], references: [id], onDelete: Cascade)\n\n  @@map(\"entity_details_research_paper\")\n  @@schema(\"public\")\n}\n\nmodel EntityDetailsSoftware {\n  entityId              String        @unique @map(\"entity_id\") @db.Uuid\n  createdAt             DateTime      @default(now()) @map(\"created_at\")\n  updatedAt             DateTime      @updatedAt @map(\"updated_at\")\n  currentVersion        String?       @map(\"current_version\")\n  licenseType           String?       @map(\"license_type\")\n  communityUrl          String?       @map(\"community_url\")\n  hasFreeTier           Boolean?      @map(\"has_free_tier\")\n  hasLiveChat           Boolean?      @map(\"has_live_chat\")\n  priceRange            PriceRange?   @map(\"price_range\")\n  pricingDetails        String?       @map(\"pricing_details\")\n  pricingModel          PricingModel? @map(\"pricing_model\")\n  pricingUrl            String?       @map(\"pricing_url\")\n  supportEmail          String?       @map(\"support_email\")\n  apiAccess             Boolean?      @map(\"api_access\")\n  customizationLevel    String?       @map(\"customization_level\")\n  demoAvailable         Boolean?      @map(\"demo_available\")\n  deploymentOptions     String[]      @map(\"deployment_options\")\n  frameworks            String[]\n  hasApi                Boolean?      @map(\"has_api\")\n  id                    String        @id @default(uuid()) @db.Uuid\n  keyFeatures           String[]      @map(\"key_features\")\n  libraries             String[]\n  mobileSupport         Boolean?      @map(\"mobile_support\")\n  openSource            Boolean?      @map(\"open_source\")\n  supportChannels       String[]      @map(\"support_channels\")\n  supportedOs           String[]      @map(\"supported_os\")\n  targetAudience        String[]      @map(\"target_audience\")\n  trialAvailable        Boolean?      @map(\"trial_available\")\n  integrations          String[]\n  platformCompatibility String[]      @map(\"platform_compatibility\")\n  programmingLanguages  String[]      @map(\"programming_languages\")\n  useCases              String[]      @map(\"use_cases\")\n  repositoryUrl         String?       @map(\"repository_url\")\n  releaseDate           DateTime?     @map(\"release_date\")\n  entity                Entity        @relation(fields: [entityId], references: [id], onDelete: Cascade)\n\n  @@map(\"entity_details_software\")\n  @@schema(\"public\")\n}\n\nmodel EntityDetailsModel {\n  entityId           String   @unique @map(\"entity_id\") @db.Uuid\n  createdAt          DateTime @default(now()) @map(\"created_at\")\n  updatedAt          DateTime @updatedAt @map(\"updated_at\")\n  license            String?\n  modelArchitecture  String?  @map(\"model_architecture\")\n  trainingDataset    String?  @map(\"training_dataset\")\n  deploymentOptions  String[] @map(\"deployment_options\")\n  frameworks         String[]\n  id                 String   @id @default(uuid()) @db.Uuid\n  inputDataTypes     String[] @map(\"input_data_types\")\n  libraries          String[]\n  outputDataTypes    String[] @map(\"output_data_types\")\n  performanceMetrics Json?    @map(\"performance_metrics\")\n  targetAudience     String[] @map(\"target_audience\")\n  useCases           String[] @map(\"use_cases\")\n  entity             Entity   @relation(fields: [entityId], references: [id], onDelete: Cascade)\n\n  @@map(\"entity_details_model\")\n  @@schema(\"public\")\n}\n\nmodel EntityDetailsProjectReference {\n  entityId        String   @unique @map(\"entity_id\") @db.Uuid\n  createdAt       DateTime @default(now()) @map(\"created_at\")\n  updatedAt       DateTime @updatedAt @map(\"updated_at\")\n  forks           Int?\n  id              String   @id @default(uuid()) @db.Uuid\n  keyTechnologies String[] @map(\"key_technologies\")\n  license         String?\n  repositoryUrl   String?  @map(\"repository_url\")\n  stars           Int?\n  status          String?\n  useCases        String[] @map(\"use_cases\")\n  contributors    Int?\n  entity          Entity   @relation(fields: [entityId], references: [id], onDelete: Cascade)\n\n  @@map(\"entity_details_project_reference\")\n  @@schema(\"public\")\n}\n\nmodel EntityDetailsServiceProvider {\n  entityId                String   @unique @map(\"entity_id\") @db.Uuid\n  createdAt               DateTime @default(now()) @map(\"created_at\")\n  updatedAt               DateTime @updatedAt @map(\"updated_at\")\n  companySizeFocus        String?  @map(\"company_size_focus\")\n  id                      String   @id @default(uuid()) @db.Uuid\n  industrySpecializations String[] @map(\"industry_specializations\")\n  locationSummary         String?  @map(\"location_summary\")\n  portfolioUrl            String?  @map(\"portfolio_url\")\n  pricingInfo             String?  @map(\"pricing_info\")\n  servicesOffered         String[] @map(\"services_offered\")\n  targetAudience          String[] @map(\"target_audience\")\n  entity                  Entity   @relation(fields: [entityId], references: [id], onDelete: Cascade)\n\n  @@map(\"entity_details_service_provider\")\n  @@schema(\"public\")\n}\n\nmodel EntityDetailsInvestor {\n  entityId           String   @unique @map(\"entity_id\") @db.Uuid\n  createdAt          DateTime @default(now()) @map(\"created_at\")\n  updatedAt          DateTime @updatedAt @map(\"updated_at\")\n  contactEmail       String?  @map(\"contact_email\")\n  applicationUrl     String?  @map(\"application_url\")\n  focusAreas         String[] @map(\"focus_areas\")\n  id                 String   @id @default(uuid()) @db.Uuid\n  investmentStages   String[] @map(\"investment_stages\")\n  investorType       String?  @map(\"investor_type\")\n  locationSummary    String?  @map(\"location_summary\")\n  notableInvestments String[] @map(\"notable_investments\")\n  portfolioSize      Int?     @map(\"portfolio_size\")\n  entity             Entity   @relation(fields: [entityId], references: [id], onDelete: Cascade)\n\n  @@map(\"entity_details_investor\")\n  @@schema(\"public\")\n}\n\nmodel EntityDetailsEvent {\n  entityId             String           @unique @map(\"entity_id\") @db.Uuid\n  createdAt            DateTime         @default(now()) @map(\"created_at\")\n  updatedAt            DateTime         @updatedAt @map(\"updated_at\")\n  endDate              DateTime?        @map(\"end_date\")\n  location             String?\n  price                String?\n  registrationUrl      String?          @map(\"registration_url\")\n  startDate            DateTime?        @map(\"start_date\")\n  eventType            String?          @map(\"event_type\")\n  id                   String           @id @default(uuid()) @db.Uuid\n  isOnline             Boolean?         @map(\"is_online\")\n  keySpeakers          String[]         @map(\"key_speakers\")\n  targetAudience       String[]         @map(\"target_audience\")\n  topics               String[]\n  registrationRequired Boolean?         @default(false) @map(\"registration_required\")\n  capacity             Int?\n  organizer            String?\n  eventFormat          EventFormatEnum? @map(\"event_format\")\n  entity               Entity           @relation(fields: [entityId], references: [id], onDelete: Cascade)\n\n  @@map(\"entity_details_event\")\n  @@schema(\"public\")\n}\n\nmodel EntityDetailsJob {\n  entityId            String               @unique @map(\"entity_id\") @db.Uuid\n  createdAt           DateTime             @default(now()) @map(\"created_at\")\n  updatedAt           DateTime             @updatedAt @map(\"updated_at\")\n  applicationUrl      String?              @map(\"application_url\")\n  companyName         String?              @map(\"company_name\")\n  experienceLevel     ExperienceLevelEnum? @map(\"experience_level\")\n  id                  String               @id @default(uuid()) @db.Uuid\n  isRemote            Boolean?             @map(\"is_remote\")\n  jobType             String?              @map(\"job_type\")\n  keyResponsibilities String[]             @map(\"key_responsibilities\")\n  location            String?\n  requiredSkills      String[]             @map(\"required_skills\")\n  salaryMax           Float?               @map(\"salary_max\")\n  salaryMin           Float?               @map(\"salary_min\")\n  jobDescription      String?              @map(\"job_description\")\n  employmentTypes     EmploymentTypeEnum[] @map(\"employment_types\")\n  locationTypes       LocationTypeEnum[]   @map(\"location_types\")\n  benefits            String[]\n  remotePolicy        String?              @map(\"remote_policy\")\n  visaSponsorship     Boolean?             @default(false) @map(\"visa_sponsorship\")\n  entity              Entity               @relation(fields: [entityId], references: [id], onDelete: Cascade)\n\n  @@map(\"entity_details_job\")\n  @@schema(\"public\")\n}\n\nmodel EntityDetailsGrant {\n  entityId       String    @unique @map(\"entity_id\") @db.Uuid\n  createdAt      DateTime  @default(now()) @map(\"created_at\")\n  updatedAt      DateTime  @updatedAt @map(\"updated_at\")\n  applicationUrl String?   @map(\"application_url\")\n  amount         String?\n  deadline       DateTime?\n  eligibility    String?\n  focusAreas     String[]  @map(\"focus_areas\")\n  funderName     String?   @map(\"funder_name\")\n  grantType      String?   @map(\"grant_type\")\n  id             String    @id @default(uuid()) @db.Uuid\n  location       String?\n  entity         Entity    @relation(fields: [entityId], references: [id], onDelete: Cascade)\n\n  @@map(\"entity_details_grant\")\n  @@schema(\"public\")\n}\n\nmodel EntityDetailsBounty {\n  entityId        String    @unique @map(\"entity_id\") @db.Uuid\n  createdAt       DateTime  @default(now()) @map(\"created_at\")\n  updatedAt       DateTime  @updatedAt @map(\"updated_at\")\n  amount          String?\n  deadline        DateTime?\n  id              String    @id @default(uuid()) @db.Uuid\n  platform        String?\n  requiredSkills  String[]  @map(\"required_skills\")\n  status          String?\n  taskDescription String?   @map(\"task_description\")\n  url             String?\n  entity          Entity    @relation(fields: [entityId], references: [id], onDelete: Cascade)\n\n  @@map(\"entity_details_bounty\")\n  @@schema(\"public\")\n}\n\nmodel EntityDetailsHardware {\n  entityId         String            @unique @map(\"entity_id\") @db.Uuid\n  createdAt        DateTime          @default(now()) @map(\"created_at\")\n  updatedAt        DateTime          @updatedAt @map(\"updated_at\")\n  availability     String?\n  gpu              String?\n  id               String            @id @default(uuid()) @db.Uuid\n  memory           String?\n  powerConsumption String?           @map(\"power_consumption\")\n  price            String?\n  processor        String?\n  storage          String?\n  useCases         String[]          @map(\"use_cases\")\n  hardwareType     HardwareTypeEnum? @map(\"hardware_type\")\n  manufacturer     String?\n  releaseDate      DateTime?         @map(\"release_date\")\n  specifications   Json?\n  datasheetUrl     String?           @map(\"datasheet_url\")\n  entity           Entity            @relation(fields: [entityId], references: [id], onDelete: Cascade)\n\n  @@map(\"entity_details_hardware\")\n  @@schema(\"public\")\n}\n\nmodel EntityDetailsNews {\n  entityId        String    @unique @map(\"entity_id\") @db.Uuid\n  createdAt       DateTime  @default(now()) @map(\"created_at\")\n  updatedAt       DateTime  @updatedAt @map(\"updated_at\")\n  articleUrl      String?   @map(\"article_url\")\n  author          String?\n  publicationDate DateTime? @map(\"publication_date\")\n  sourceName      String?   @map(\"source_name\")\n  summary         String?\n  id              String    @id @default(uuid()) @db.Uuid\n  tags            String[]\n  entity          Entity    @relation(fields: [entityId], references: [id], onDelete: Cascade)\n\n  @@map(\"entity_details_news\")\n  @@schema(\"public\")\n}\n\nmodel EntityDetailsBook {\n  entityId        String    @unique @map(\"entity_id\") @db.Uuid\n  createdAt       DateTime  @default(now()) @map(\"created_at\")\n  updatedAt       DateTime  @updatedAt @map(\"updated_at\")\n  isbn            String?\n  pageCount       Int?      @map(\"page_count\")\n  publisher       String?\n  purchaseUrl     String?   @map(\"purchase_url\")\n  summary         String?\n  author          String?\n  format          String?\n  id              String    @id @default(uuid()) @db.Uuid\n  publicationDate DateTime? @map(\"publication_date\")\n  entity          Entity    @relation(fields: [entityId], references: [id], onDelete: Cascade)\n\n  @@map(\"entity_details_book\")\n  @@schema(\"public\")\n}\n\nmodel EntityDetailsPodcast {\n  entityId          String   @unique @map(\"entity_id\") @db.Uuid\n  createdAt         DateTime @default(now()) @map(\"created_at\")\n  updatedAt         DateTime @updatedAt @map(\"updated_at\")\n  frequency         String?\n  applePodcastsUrl  String?  @map(\"apple_podcasts_url\")\n  averageLength     String?  @map(\"average_length\")\n  googlePodcastsUrl String?  @map(\"google_podcasts_url\")\n  host              String?\n  id                String   @id @default(uuid()) @db.Uuid\n  mainTopics        String[] @map(\"main_topics\")\n  spotifyUrl        String?  @map(\"spotify_url\")\n  youtubeUrl        String?  @map(\"youtube_url\")\n  entity            Entity   @relation(fields: [entityId], references: [id], onDelete: Cascade)\n\n  @@map(\"entity_details_podcast\")\n  @@schema(\"public\")\n}\n\nmodel EntityDetailsPlatform {\n  entityId           String        @unique @map(\"entity_id\") @db.Uuid\n  createdAt          DateTime      @default(now()) @map(\"created_at\")\n  updatedAt          DateTime      @updatedAt @map(\"updated_at\")\n  documentationUrl   String?       @map(\"documentation_url\")\n  platformType       String?       @map(\"platform_type\")\n  communityUrl       String?       @map(\"community_url\")\n  hasFreeTier        Boolean?      @map(\"has_free_tier\")\n  hasLiveChat        Boolean?      @map(\"has_live_chat\")\n  priceRange         PriceRange?   @map(\"price_range\")\n  pricingDetails     String?       @map(\"pricing_details\")\n  pricingUrl         String?       @map(\"pricing_url\")\n  supportEmail       String?       @map(\"support_email\")\n  pricingModel       PricingModel? @map(\"pricing_model\")\n  apiAccess          Boolean?      @map(\"api_access\")\n  customizationLevel String?       @map(\"customization_level\")\n  demoAvailable      Boolean?      @map(\"demo_available\")\n  deploymentOptions  String[]      @map(\"deployment_options\")\n  hasApi             Boolean?      @map(\"has_api\")\n  id                 String        @id @default(uuid()) @db.Uuid\n  mobileSupport      Boolean?      @map(\"mobile_support\")\n  openSource         Boolean?      @map(\"open_source\")\n  supportChannels    String[]      @map(\"support_channels\")\n  supportedOs        String[]      @map(\"supported_os\")\n  targetAudience     String[]      @map(\"target_audience\")\n  trialAvailable     Boolean?      @map(\"trial_available\")\n  integrations       String[]\n  keyServices        String[]      @map(\"key_services\")\n  useCases           String[]      @map(\"use_cases\")\n  entity             Entity        @relation(fields: [entityId], references: [id], onDelete: Cascade)\n\n  @@map(\"entity_details_platform\")\n  @@schema(\"public\")\n}\n\nmodel EntityFeature {\n  entityId   String   @map(\"entity_id\") @db.Uuid\n  featureId  String   @map(\"feature_id\") @db.Uuid\n  assignedBy String   @map(\"assigned_by\") @db.Uuid\n  createdAt  DateTime @default(now()) @map(\"created_at\")\n  id         String   @id @default(uuid()) @db.Uuid\n  entity     Entity   @relation(fields: [entityId], references: [id], onDelete: Cascade)\n  feature    Feature  @relation(fields: [featureId], references: [id], onDelete: Cascade)\n\n  @@unique([entityId, featureId], map: \"uq_entity_feature\")\n  @@map(\"entity_features\")\n  @@schema(\"public\")\n}\n\nmodel AppSetting {\n  key         String   @id\n  value       String\n  description String?\n  createdAt   DateTime @default(now()) @map(\"created_at\")\n  updatedAt   DateTime @updatedAt @map(\"updated_at\")\n\n  @@map(\"app_settings\")\n  @@schema(\"public\")\n}\n\nmodel Feature {\n  id             String          @id @default(uuid()) @db.Uuid\n  name           String          @unique\n  slug           String          @unique\n  description    String?\n  createdAt      DateTime        @default(now()) @map(\"created_at\")\n  updatedAt      DateTime        @updatedAt @map(\"updated_at\")\n  iconUrl        String?         @map(\"icon_url\")\n  entityFeatures EntityFeature[]\n\n  @@map(\"features\")\n  @@schema(\"public\")\n}\n\nmodel UserPreferences {\n  id                  String            @id @default(uuid()) @db.Uuid\n  userId              String            @unique @map(\"user_id\") @db.Uuid\n  emailNotifications  Boolean           @default(true) @map(\"email_notifications\")\n  marketingEmails     Boolean           @default(false) @map(\"marketing_emails\")\n  weeklyDigest        Boolean           @default(true) @map(\"weekly_digest\")\n  newToolAlerts       Boolean           @default(true) @map(\"new_tool_alerts\")\n  profileVisibility   ProfileVisibility @default(PUBLIC) @map(\"profile_visibility\")\n  showBookmarks       Boolean           @default(true) @map(\"show_bookmarks\")\n  showReviews         Boolean           @default(true) @map(\"show_reviews\")\n  showActivity        Boolean           @default(true) @map(\"show_activity\")\n  theme               Theme             @default(LIGHT)\n  itemsPerPage        Int               @default(20) @map(\"items_per_page\")\n  defaultView         DefaultView       @default(GRID) @map(\"default_view\")\n  preferredCategories String[]          @map(\"preferred_categories\")\n  blockedCategories   String[]          @map(\"blocked_categories\")\n  contentLanguage     String            @default(\"en\") @map(\"content_language\")\n  createdAt           DateTime          @default(now()) @map(\"created_at\")\n  updatedAt           DateTime          @updatedAt @map(\"updated_at\")\n  user                User              @relation(fields: [userId], references: [id], onDelete: Cascade)\n\n  @@map(\"user_preferences\")\n  @@schema(\"public\")\n}\n\nmodel ToolRequest {\n  id                 String              @id @default(uuid()) @db.Uuid\n  userId             String              @map(\"user_id\") @db.Uuid\n  toolName           String              @map(\"tool_name\")\n  description        String\n  reason             String\n  categorySuggestion String?             @map(\"category_suggestion\")\n  websiteUrl         String?             @map(\"website_url\")\n  priority           ToolRequestPriority @default(MEDIUM)\n  status             ToolRequestStatus   @default(PENDING)\n  adminNotes         String?             @map(\"admin_notes\")\n  votes              Int                 @default(0)\n  createdAt          DateTime            @default(now()) @map(\"created_at\")\n  updatedAt          DateTime            @updatedAt @map(\"updated_at\")\n  user               User                @relation(fields: [userId], references: [id], onDelete: Cascade)\n\n  @@map(\"tool_requests\")\n  @@schema(\"public\")\n}\n\nmodel UserSubmittedTool {\n  id               String           @id @default(uuid()) @db.Uuid\n  userId           String           @map(\"user_id\") @db.Uuid\n  entityId         String           @map(\"entity_id\") @db.Uuid\n  submissionStatus SubmissionStatus @default(PENDING) @map(\"submission_status\")\n  submittedAt      DateTime         @default(now()) @map(\"submitted_at\")\n  reviewedAt       DateTime?        @map(\"reviewed_at\")\n  reviewerId       String?          @map(\"reviewer_id\") @db.Uuid\n  reviewerNotes    String?          @map(\"reviewer_notes\")\n  changesRequested String?          @map(\"changes_requested\")\n  entity           Entity           @relation(fields: [entityId], references: [id], onDelete: Cascade)\n  reviewer         User?            @relation(\"ReviewerSubmissions\", fields: [reviewerId], references: [id])\n  user             User             @relation(fields: [userId], references: [id], onDelete: Cascade)\n\n  @@unique([userId, entityId])\n  @@map(\"user_submitted_tools\")\n  @@schema(\"public\")\n}\n\nmodel ProfileActivity {\n  id          String              @id @default(uuid()) @db.Uuid\n  userId      String              @map(\"user_id\") @db.Uuid\n  type        ProfileActivityType\n  description String\n  entityId    String?             @map(\"entity_id\") @db.Uuid\n  entityName  String?             @map(\"entity_name\")\n  entitySlug  String?             @map(\"entity_slug\")\n  createdAt   DateTime            @default(now()) @map(\"created_at\")\n  entity      Entity?             @relation(fields: [entityId], references: [id])\n  user        User                @relation(fields: [userId], references: [id], onDelete: Cascade)\n\n  @@map(\"profile_activities\")\n  @@schema(\"public\")\n}\n\nenum UserRole {\n  USER\n  ADMIN\n  MODERATOR\n\n  @@schema(\"public\")\n}\n\nenum UserStatus {\n  ACTIVE\n  INACTIVE\n  PENDING\n  SUSPENDED\n  DELETED\n\n  @@schema(\"public\")\n}\n\nenum TechnicalLevel {\n  BEGINNER\n  INTERMEDIATE\n  ADVANCED\n  EXPERT\n\n  @@schema(\"public\")\n}\n\nenum EntityStatus {\n  PENDING\n  ACTIVE\n  REJECTED\n  INACTIVE\n  ARCHIVED\n  NEEDS_REVISION\n\n  @@schema(\"public\")\n}\n\nenum AffiliateStatus {\n  NONE\n  APPLIED\n  APPROVED\n  REJECTED\n\n  @@schema(\"public\")\n}\n\nenum ReviewStatus {\n  PENDING\n  APPROVED\n  REJECTED\n\n  @@schema(\"public\")\n}\n\nenum LearningCurve {\n  LOW\n  MEDIUM\n  HIGH\n\n  @@schema(\"public\")\n}\n\nenum PricingModel {\n  FREE\n  FREEMIUM\n  SUBSCRIPTION\n  PAY_PER_USE\n  ONE_TIME_PURCHASE\n  CONTACT_SALES\n  OPEN_SOURCE\n\n  @@schema(\"public\")\n}\n\nenum PriceRange {\n  FREE\n  LOW\n  MEDIUM\n  HIGH\n  ENTERPRISE\n\n  @@schema(\"public\")\n}\n\nenum SkillLevel {\n  BEGINNER\n  INTERMEDIATE\n  ADVANCED\n  EXPERT\n\n  @@schema(\"public\")\n}\n\nenum ActionType {\n  VIEW_ENTITY\n  CLICK_ENTITY_LINK\n  SAVE_ENTITY\n  UNSAVE_ENTITY\n  SUBMIT_REVIEW\n  VOTE_REVIEW\n  FOLLOW_TAG\n  UNFOLLOW_TAG\n  FOLLOW_CATEGORY\n  UNFOLLOW_CATEGORY\n  SEARCH\n  LOGIN\n  LOGOUT\n  SIGNUP\n  UPDATE_PROFILE\n  GRANT_BADGE\n  REVOKE_BADGE\n\n  @@schema(\"public\")\n}\n\nenum BadgeScope {\n  USER\n  ENTITY\n\n  @@schema(\"public\")\n}\n\nenum EmployeeCountRange {\n  C1_10\n  C11_50\n  C51_200\n  C201_500\n  C501_1000\n  C1001_5000\n  C5001_PLUS\n\n  @@schema(\"public\")\n}\n\nenum FundingStage {\n  SEED\n  PRE_SEED\n  SERIES_A\n  SERIES_B\n  SERIES_C\n  SERIES_D_PLUS\n  PUBLIC\n\n  @@schema(\"public\")\n}\n\nenum ProfileVisibility {\n  PUBLIC\n  PRIVATE\n  FRIENDS\n\n  @@schema(\"public\")\n}\n\nenum Theme {\n  LIGHT\n  DARK\n  SYSTEM\n\n  @@schema(\"public\")\n}\n\nenum DefaultView {\n  GRID\n  LIST\n\n  @@schema(\"public\")\n}\n\nenum ToolRequestPriority {\n  LOW\n  MEDIUM\n  HIGH\n\n  @@schema(\"public\")\n}\n\nenum ToolRequestStatus {\n  PENDING\n  UNDER_REVIEW\n  APPROVED\n  REJECTED\n  COMPLETED\n\n  @@schema(\"public\")\n}\n\nenum SubmissionStatus {\n  PENDING\n  UNDER_REVIEW\n  APPROVED\n  REJECTED\n  PUBLISHED\n\n  @@schema(\"public\")\n}\n\nenum ProfileActivityType {\n  BOOKMARK\n  REVIEW\n  SUBMISSION\n  REQUEST\n  VOTE\n\n  @@schema(\"public\")\n}\n\nenum HardwareTypeEnum {\n  GPU\n  CPU\n  FPGA\n  TPU\n  ASIC\n  NPU\n  Memory\n  Storage\n\n  @@schema(\"public\")\n}\n\nenum EmploymentTypeEnum {\n  FULL_TIME\n  PART_TIME\n  CONTRACT\n  FREELANCE\n  INTERNSHIP\n  TEMPORARY\n\n  @@schema(\"public\")\n}\n\nenum ExperienceLevelEnum {\n  ENTRY\n  JUNIOR\n  MID\n  SENIOR\n  LEAD\n  PRINCIPAL\n  DIRECTOR\n\n  @@schema(\"public\")\n}\n\nenum LocationTypeEnum {\n  Remote\n  On_site\n  Hybrid\n\n  @@schema(\"public\")\n}\n\nenum EventFormatEnum {\n  in_person\n  virtual\n  hybrid\n\n  @@schema(\"public\")\n}\n",
  "inlineSchemaHash": "ead146d2063b20fc89b1407cabb1109eaf073236935963a34d3f95db87a04f7d",
  "copyEngine": true
}
config.dirname = '/'

config.runtimeDataModel = JSON.parse("{\"models\":{\"User\":{\"dbName\":\"users\",\"schema\":\"public\",\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"default\":{\"name\":\"uuid\",\"args\":[4]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"authUserId\",\"dbName\":\"auth_user_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":true,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"username\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":true,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"displayName\",\"dbName\":\"display_name\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"email\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":true,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"role\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"UserRole\",\"nativeType\":null,\"default\":\"USER\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"status\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"UserStatus\",\"nativeType\":null,\"default\":\"ACTIVE\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"technicalLevel\",\"dbName\":\"technical_level\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"TechnicalLevel\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"profilePictureUrl\",\"dbName\":\"profile_picture_url\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"bio\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"socialLinks\",\"dbName\":\"social_links\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Json\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"dbName\":\"created_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedAt\",\"dbName\":\"updated_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":true},{\"name\":\"lastLogin\",\"dbName\":\"last_login\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"bookmarksCount\",\"dbName\":\"bookmarks_count\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Int\",\"nativeType\":null,\"default\":0,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"reputationScore\",\"dbName\":\"reputation_score\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Int\",\"nativeType\":null,\"default\":0,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"requestsFulfilled\",\"dbName\":\"requests_fulfilled\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Int\",\"nativeType\":null,\"default\":0,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"requestsMade\",\"dbName\":\"requests_made\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Int\",\"nativeType\":null,\"default\":0,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"reviewsCount\",\"dbName\":\"reviews_count\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Int\",\"nativeType\":null,\"default\":0,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"toolsApproved\",\"dbName\":\"tools_approved\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Int\",\"nativeType\":null,\"default\":0,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"toolsSubmitted\",\"dbName\":\"tools_submitted\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Int\",\"nativeType\":null,\"default\":0,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"submittedEntities\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Entity\",\"nativeType\":null,\"relationName\":\"Submitter\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entityBadgesGranted\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"EntityBadge\",\"nativeType\":null,\"relationName\":\"EntityBadgesGranted\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"profileActivities\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"ProfileActivity\",\"nativeType\":null,\"relationName\":\"ProfileActivityToUser\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"reviewVotes\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"ReviewVote\",\"nativeType\":null,\"relationName\":\"UserReviewVotes\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"reviewsModerated\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Review\",\"nativeType\":null,\"relationName\":\"ModeratorReviews\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"reviews\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Review\",\"nativeType\":null,\"relationName\":\"ReviewToUser\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"toolRequests\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"ToolRequest\",\"nativeType\":null,\"relationName\":\"ToolRequestToUser\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"userActivityLogTargets\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"UserActivityLog\",\"nativeType\":null,\"relationName\":\"TargetUserLogs\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"userActivityLogs\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"UserActivityLog\",\"nativeType\":null,\"relationName\":\"UserLogs\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"badgesGranted\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"UserBadge\",\"nativeType\":null,\"relationName\":\"UserBadgesGranted\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"userBadges\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"UserBadge\",\"nativeType\":null,\"relationName\":\"UserBadges\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"userFollowedCategories\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"UserFollowedCategory\",\"nativeType\":null,\"relationName\":\"UserFollowedCategories\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"userFollowedTags\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"UserFollowedTag\",\"nativeType\":null,\"relationName\":\"UserFollowedTags\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"userNotificationSettings\",\"kind\":\"object\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"UserNotificationSettings\",\"nativeType\":null,\"relationName\":\"UserToUserNotificationSettings\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"userPreferences\",\"kind\":\"object\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"UserPreferences\",\"nativeType\":null,\"relationName\":\"UserToUserPreferences\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"userSavedEntities\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"UserSavedEntity\",\"nativeType\":null,\"relationName\":\"UserSavedEntities\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"userUpvotes\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"UserUpvote\",\"nativeType\":null,\"relationName\":\"UserToUserUpvote\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"reviewedSubmissions\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"UserSubmittedTool\",\"nativeType\":null,\"relationName\":\"ReviewerSubmissions\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"userSubmittedTools\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"UserSubmittedTool\",\"nativeType\":null,\"relationName\":\"UserToUserSubmittedTool\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"EntityType\":{\"dbName\":\"entity_types\",\"schema\":\"public\",\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"default\":{\"name\":\"uuid\",\"args\":[4]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"name\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":true,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"description\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"slug\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":true,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"iconUrl\",\"dbName\":\"icon_url\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"dbName\":\"created_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedAt\",\"dbName\":\"updated_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":true},{\"name\":\"entities\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Entity\",\"nativeType\":null,\"relationName\":\"EntityToEntityType\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"Entity\":{\"dbName\":\"entities\",\"schema\":\"public\",\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"default\":{\"name\":\"uuid\",\"args\":[4]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entityTypeId\",\"dbName\":\"entity_type_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"name\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":true,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"shortDescription\",\"dbName\":\"short_description\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"description\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"logoUrl\",\"dbName\":\"logo_url\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"websiteUrl\",\"dbName\":\"website_url\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"documentationUrl\",\"dbName\":\"documentation_url\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"contactUrl\",\"dbName\":\"contact_url\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"privacyPolicyUrl\",\"dbName\":\"privacy_policy_url\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"foundedYear\",\"dbName\":\"founded_year\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Int\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"socialLinks\",\"dbName\":\"social_links\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Json\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"status\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"EntityStatus\",\"nativeType\":null,\"default\":\"PENDING\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"avgRating\",\"dbName\":\"avg_rating\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Float\",\"nativeType\":null,\"default\":0,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"reviewCount\",\"dbName\":\"review_count\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Int\",\"nativeType\":null,\"default\":0,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"upvoteCount\",\"dbName\":\"upvote_count\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Int\",\"nativeType\":null,\"default\":0,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"dbName\":\"created_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedAt\",\"dbName\":\"updated_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":true},{\"name\":\"legacyId\",\"dbName\":\"legacy_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"submitterId\",\"dbName\":\"submitter_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"affiliateStatus\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"AffiliateStatus\",\"nativeType\":null,\"default\":\"NONE\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"locationSummary\",\"dbName\":\"location_summary\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"metaDescription\",\"dbName\":\"meta_description\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"metaTitle\",\"dbName\":\"meta_title\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"refLink\",\"dbName\":\"ref_link\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"scrapedReviewCount\",\"dbName\":\"scraped_review_count\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Int\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"scrapedReviewSentimentLabel\",\"dbName\":\"scraped_review_sentiment_label\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"scrapedReviewSentimentScore\",\"dbName\":\"scraped_review_sentiment_score\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Float\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"employeeCountRange\",\"dbName\":\"employee_count_range\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"EmployeeCountRange\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"fundingStage\",\"dbName\":\"funding_stage\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"FundingStage\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"slug\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":true,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entityType\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"EntityType\",\"nativeType\":null,\"relationName\":\"EntityToEntityType\",\"relationFromFields\":[\"entityTypeId\"],\"relationToFields\":[\"id\"],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"submitter\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"User\",\"nativeType\":null,\"relationName\":\"Submitter\",\"relationFromFields\":[\"submitterId\"],\"relationToFields\":[\"id\"],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entityBadges\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"EntityBadge\",\"nativeType\":null,\"relationName\":\"EntityToEntityBadge\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entityCategories\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"EntityCategory\",\"nativeType\":null,\"relationName\":\"EntityToEntityCategory\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entityDetailsAgency\",\"kind\":\"object\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"EntityDetailsAgency\",\"nativeType\":null,\"relationName\":\"EntityToEntityDetailsAgency\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entityDetailsBook\",\"kind\":\"object\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"EntityDetailsBook\",\"nativeType\":null,\"relationName\":\"EntityToEntityDetailsBook\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entityDetailsBounty\",\"kind\":\"object\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"EntityDetailsBounty\",\"nativeType\":null,\"relationName\":\"EntityToEntityDetailsBounty\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entityDetailsCommunity\",\"kind\":\"object\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"EntityDetailsCommunity\",\"nativeType\":null,\"relationName\":\"EntityToEntityDetailsCommunity\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entityDetailsContentCreator\",\"kind\":\"object\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"EntityDetailsContentCreator\",\"nativeType\":null,\"relationName\":\"EntityToEntityDetailsContentCreator\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entityDetailsCourse\",\"kind\":\"object\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"EntityDetailsCourse\",\"nativeType\":null,\"relationName\":\"EntityToEntityDetailsCourse\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entityDetailsDataset\",\"kind\":\"object\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"EntityDetailsDataset\",\"nativeType\":null,\"relationName\":\"EntityToEntityDetailsDataset\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entityDetailsEvent\",\"kind\":\"object\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"EntityDetailsEvent\",\"nativeType\":null,\"relationName\":\"EntityToEntityDetailsEvent\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entityDetailsGrant\",\"kind\":\"object\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"EntityDetailsGrant\",\"nativeType\":null,\"relationName\":\"EntityToEntityDetailsGrant\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entityDetailsHardware\",\"kind\":\"object\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"EntityDetailsHardware\",\"nativeType\":null,\"relationName\":\"EntityToEntityDetailsHardware\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entityDetailsInvestor\",\"kind\":\"object\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"EntityDetailsInvestor\",\"nativeType\":null,\"relationName\":\"EntityToEntityDetailsInvestor\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entityDetailsJob\",\"kind\":\"object\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"EntityDetailsJob\",\"nativeType\":null,\"relationName\":\"EntityToEntityDetailsJob\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entityDetailsModel\",\"kind\":\"object\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"EntityDetailsModel\",\"nativeType\":null,\"relationName\":\"EntityToEntityDetailsModel\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entityDetailsNews\",\"kind\":\"object\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"EntityDetailsNews\",\"nativeType\":null,\"relationName\":\"EntityToEntityDetailsNews\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entityDetailsNewsletter\",\"kind\":\"object\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"EntityDetailsNewsletter\",\"nativeType\":null,\"relationName\":\"EntityToEntityDetailsNewsletter\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entityDetailsPlatform\",\"kind\":\"object\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"EntityDetailsPlatform\",\"nativeType\":null,\"relationName\":\"EntityToEntityDetailsPlatform\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entityDetailsPodcast\",\"kind\":\"object\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"EntityDetailsPodcast\",\"nativeType\":null,\"relationName\":\"EntityToEntityDetailsPodcast\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entityDetailsProjectReference\",\"kind\":\"object\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"EntityDetailsProjectReference\",\"nativeType\":null,\"relationName\":\"EntityToEntityDetailsProjectReference\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entityDetailsResearchPaper\",\"kind\":\"object\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"EntityDetailsResearchPaper\",\"nativeType\":null,\"relationName\":\"EntityToEntityDetailsResearchPaper\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entityDetailsServiceProvider\",\"kind\":\"object\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"EntityDetailsServiceProvider\",\"nativeType\":null,\"relationName\":\"EntityToEntityDetailsServiceProvider\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entityDetailsSoftware\",\"kind\":\"object\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"EntityDetailsSoftware\",\"nativeType\":null,\"relationName\":\"EntityToEntityDetailsSoftware\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entityDetailsTool\",\"kind\":\"object\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"EntityDetailsTool\",\"nativeType\":null,\"relationName\":\"EntityToEntityDetailsTool\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entityFeatures\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"EntityFeature\",\"nativeType\":null,\"relationName\":\"EntityToEntityFeature\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entityTags\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"EntityTag\",\"nativeType\":null,\"relationName\":\"EntityToEntityTag\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"profileActivities\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"ProfileActivity\",\"nativeType\":null,\"relationName\":\"EntityToProfileActivity\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"reviews\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Review\",\"nativeType\":null,\"relationName\":\"EntityToReview\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"userActivityLogs\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"UserActivityLog\",\"nativeType\":null,\"relationName\":\"EntityLogs\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"userSavedEntities\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"UserSavedEntity\",\"nativeType\":null,\"relationName\":\"EntityToUserSavedEntity\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"userUpvotes\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"UserUpvote\",\"nativeType\":null,\"relationName\":\"EntityToUserUpvote\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"userSubmittedTools\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"UserSubmittedTool\",\"nativeType\":null,\"relationName\":\"EntityToUserSubmittedTool\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"Category\":{\"dbName\":\"categories\",\"schema\":\"public\",\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"default\":{\"name\":\"uuid\",\"args\":[4]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"name\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":true,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"description\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"slug\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":true,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"iconUrl\",\"dbName\":\"icon_url\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"dbName\":\"created_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedAt\",\"dbName\":\"updated_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":true},{\"name\":\"parentId\",\"dbName\":\"parent_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"parent\",\"kind\":\"object\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Category\",\"nativeType\":null,\"relationName\":\"CategoryToParent\",\"relationFromFields\":[\"parentId\"],\"relationToFields\":[\"id\"],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"children\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Category\",\"nativeType\":null,\"relationName\":\"CategoryToParent\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entityCategories\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"EntityCategory\",\"nativeType\":null,\"relationName\":\"CategoryToEntityCategory\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"userActivityLogs\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"UserActivityLog\",\"nativeType\":null,\"relationName\":\"CategoryToUserActivityLog\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"userFollowedCategories\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"UserFollowedCategory\",\"nativeType\":null,\"relationName\":\"CategoryToUserFollowedCategory\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"Tag\":{\"dbName\":\"tags\",\"schema\":\"public\",\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"default\":{\"name\":\"uuid\",\"args\":[4]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"name\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":true,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"description\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"slug\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":true,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"dbName\":\"created_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedAt\",\"dbName\":\"updated_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":true},{\"name\":\"entityTags\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"EntityTag\",\"nativeType\":null,\"relationName\":\"EntityTagToTag\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"userActivityLogs\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"UserActivityLog\",\"nativeType\":null,\"relationName\":\"TagLogs\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"userFollowed\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"UserFollowedTag\",\"nativeType\":null,\"relationName\":\"TagToUserFollowedTag\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"EntityTag\":{\"dbName\":\"entity_tags\",\"schema\":\"public\",\"fields\":[{\"name\":\"entityId\",\"dbName\":\"entity_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"tagId\",\"dbName\":\"tag_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"assignedBy\",\"dbName\":\"assigned_by\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"dbName\":\"created_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"default\":{\"name\":\"uuid\",\"args\":[4]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entity\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Entity\",\"nativeType\":null,\"relationName\":\"EntityToEntityTag\",\"relationFromFields\":[\"entityId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"tag\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Tag\",\"nativeType\":null,\"relationName\":\"EntityTagToTag\",\"relationFromFields\":[\"tagId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[[\"entityId\",\"tagId\"]],\"uniqueIndexes\":[{\"name\":null,\"fields\":[\"entityId\",\"tagId\"]}],\"isGenerated\":false},\"EntityCategory\":{\"dbName\":\"entity_categories\",\"schema\":\"public\",\"fields\":[{\"name\":\"entityId\",\"dbName\":\"entity_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"categoryId\",\"dbName\":\"category_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"assignedAt\",\"dbName\":\"assigned_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"assignedBy\",\"dbName\":\"assigned_by\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"category\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Category\",\"nativeType\":null,\"relationName\":\"CategoryToEntityCategory\",\"relationFromFields\":[\"categoryId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entity\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Entity\",\"nativeType\":null,\"relationName\":\"EntityToEntityCategory\",\"relationFromFields\":[\"entityId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":{\"name\":null,\"fields\":[\"entityId\",\"categoryId\"]},\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"Review\":{\"dbName\":\"reviews\",\"schema\":\"public\",\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"default\":{\"name\":\"uuid\",\"args\":[4]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entityId\",\"dbName\":\"entity_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"userId\",\"dbName\":\"user_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"rating\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Int\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"title\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"status\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"ReviewStatus\",\"nativeType\":null,\"default\":\"PENDING\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"moderationNotes\",\"dbName\":\"moderation_notes\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"dbName\":\"created_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedAt\",\"dbName\":\"updated_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":true},{\"name\":\"content\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"downvotes\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Int\",\"nativeType\":null,\"default\":0,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"moderatorId\",\"dbName\":\"moderator_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"upvotes\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Int\",\"nativeType\":null,\"default\":0,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"reviewVotes\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"ReviewVote\",\"nativeType\":null,\"relationName\":\"ReviewToReviewVote\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entity\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Entity\",\"nativeType\":null,\"relationName\":\"EntityToReview\",\"relationFromFields\":[\"entityId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"moderator\",\"kind\":\"object\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"User\",\"nativeType\":null,\"relationName\":\"ModeratorReviews\",\"relationFromFields\":[\"moderatorId\"],\"relationToFields\":[\"id\"],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"user\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"User\",\"nativeType\":null,\"relationName\":\"ReviewToUser\",\"relationFromFields\":[\"userId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"userActivityLogs\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"UserActivityLog\",\"nativeType\":null,\"relationName\":\"ReviewLogs\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[[\"entityId\",\"userId\"]],\"uniqueIndexes\":[{\"name\":null,\"fields\":[\"entityId\",\"userId\"]}],\"isGenerated\":false},\"ReviewVote\":{\"dbName\":\"review_votes\",\"schema\":\"public\",\"fields\":[{\"name\":\"reviewId\",\"dbName\":\"review_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"userId\",\"dbName\":\"user_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"dbName\":\"created_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"default\":{\"name\":\"uuid\",\"args\":[4]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"isUpvote\",\"dbName\":\"is_upvote\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Boolean\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"review\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Review\",\"nativeType\":null,\"relationName\":\"ReviewToReviewVote\",\"relationFromFields\":[\"reviewId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"user\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"User\",\"nativeType\":null,\"relationName\":\"UserReviewVotes\",\"relationFromFields\":[\"userId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[[\"reviewId\",\"userId\"]],\"uniqueIndexes\":[{\"name\":null,\"fields\":[\"reviewId\",\"userId\"]}],\"isGenerated\":false},\"EntityDetailsTool\":{\"dbName\":\"entity_details_tool\",\"schema\":\"public\",\"fields\":[{\"name\":\"entityId\",\"dbName\":\"entity_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":true,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"learningCurve\",\"dbName\":\"learning_curve\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"LearningCurve\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"targetAudience\",\"dbName\":\"target_audience\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Json\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"keyFeatures\",\"dbName\":\"key_features\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Json\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"useCases\",\"dbName\":\"use_cases\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Json\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"pricingModel\",\"dbName\":\"pricing_model\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"PricingModel\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"priceRange\",\"dbName\":\"price_range\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"PriceRange\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"pricingDetails\",\"dbName\":\"pricing_details\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"pricingUrl\",\"dbName\":\"pricing_url\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"hasFreeTier\",\"dbName\":\"has_free_tier\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Boolean\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"integrations\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Json\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"apiAccess\",\"dbName\":\"api_access\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Boolean\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"communityUrl\",\"dbName\":\"community_url\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"customizationLevel\",\"dbName\":\"customization_level\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"demoAvailable\",\"dbName\":\"demo_available\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Boolean\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"deploymentOptions\",\"dbName\":\"deployment_options\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Json\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"frameworks\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Json\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"hasLiveChat\",\"dbName\":\"has_live_chat\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Boolean\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"libraries\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Json\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"mobileSupport\",\"dbName\":\"mobile_support\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Boolean\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"openSource\",\"dbName\":\"open_source\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Boolean\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"programmingLanguages\",\"dbName\":\"programming_languages\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Json\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"supportChannels\",\"dbName\":\"support_channels\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Json\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"supportEmail\",\"dbName\":\"support_email\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"supportedOs\",\"dbName\":\"supported_os\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Json\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"trialAvailable\",\"dbName\":\"trial_available\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Boolean\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"hasApi\",\"dbName\":\"has_api\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Boolean\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"default\":{\"name\":\"uuid\",\"args\":[4]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"platforms\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Json\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"technicalLevel\",\"dbName\":\"technical_level\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"TechnicalLevel\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entity\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Entity\",\"nativeType\":null,\"relationName\":\"EntityToEntityDetailsTool\",\"relationFromFields\":[\"entityId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"EntityDetailsAgency\":{\"dbName\":\"entity_details_agency\",\"schema\":\"public\",\"fields\":[{\"name\":\"entityId\",\"dbName\":\"entity_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":true,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"servicesOffered\",\"dbName\":\"services_offered\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Json\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"industryFocus\",\"dbName\":\"industry_focus\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Json\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"targetClientSize\",\"dbName\":\"target_client_size\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Json\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"targetAudience\",\"dbName\":\"target_audience\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Json\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"locationSummary\",\"dbName\":\"location_summary\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"portfolioUrl\",\"dbName\":\"portfolio_url\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"pricingInfo\",\"dbName\":\"pricing_info\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"default\":{\"name\":\"uuid\",\"args\":[4]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entity\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Entity\",\"nativeType\":null,\"relationName\":\"EntityToEntityDetailsAgency\",\"relationFromFields\":[\"entityId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"EntityDetailsContentCreator\":{\"dbName\":\"entity_details_content_creator\",\"schema\":\"public\",\"fields\":[{\"name\":\"entityId\",\"dbName\":\"entity_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":true,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"creatorName\",\"dbName\":\"creator_name\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"primaryPlatform\",\"dbName\":\"primary_platform\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"focusAreas\",\"dbName\":\"focus_areas\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Json\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"followerCount\",\"dbName\":\"follower_count\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Int\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"exampleContentUrl\",\"dbName\":\"example_content_url\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"default\":{\"name\":\"uuid\",\"args\":[4]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entity\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Entity\",\"nativeType\":null,\"relationName\":\"EntityToEntityDetailsContentCreator\",\"relationFromFields\":[\"entityId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"EntityDetailsCommunity\":{\"dbName\":\"entity_details_community\",\"schema\":\"public\",\"fields\":[{\"name\":\"entityId\",\"dbName\":\"entity_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":true,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"platform\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"memberCount\",\"dbName\":\"member_count\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Int\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"focusTopics\",\"dbName\":\"focus_topics\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Json\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"rulesUrl\",\"dbName\":\"rules_url\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"inviteUrl\",\"dbName\":\"invite_url\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"mainChannelUrl\",\"dbName\":\"main_channel_url\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"default\":{\"name\":\"uuid\",\"args\":[4]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entity\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Entity\",\"nativeType\":null,\"relationName\":\"EntityToEntityDetailsCommunity\",\"relationFromFields\":[\"entityId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"EntityDetailsNewsletter\":{\"dbName\":\"entity_details_newsletter\",\"schema\":\"public\",\"fields\":[{\"name\":\"entityId\",\"dbName\":\"entity_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":true,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"frequency\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"mainTopics\",\"dbName\":\"main_topics\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Json\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"archiveUrl\",\"dbName\":\"archive_url\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"subscribeUrl\",\"dbName\":\"subscribe_url\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"authorName\",\"dbName\":\"author_name\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"subscriberCount\",\"dbName\":\"subscriber_count\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Int\",\"nativeType\":null,\"default\":0,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"default\":{\"name\":\"uuid\",\"args\":[4]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entity\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Entity\",\"nativeType\":null,\"relationName\":\"EntityToEntityDetailsNewsletter\",\"relationFromFields\":[\"entityId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"EntityDetailsCourse\":{\"dbName\":\"entity_details_course\",\"schema\":\"public\",\"fields\":[{\"name\":\"entityId\",\"dbName\":\"entity_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":true,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"instructorName\",\"dbName\":\"instructor_name\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"durationText\",\"dbName\":\"duration_text\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"skillLevel\",\"dbName\":\"skill_level\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"SkillLevel\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"prerequisites\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"syllabusUrl\",\"dbName\":\"syllabus_url\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"enrollmentCount\",\"dbName\":\"enrollment_count\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Int\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"certificateAvailable\",\"dbName\":\"certificate_available\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Boolean\",\"nativeType\":null,\"default\":false,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"default\":{\"name\":\"uuid\",\"args\":[4]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entity\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Entity\",\"nativeType\":null,\"relationName\":\"EntityToEntityDetailsCourse\",\"relationFromFields\":[\"entityId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"UserSavedEntity\":{\"dbName\":\"user_saved_entities\",\"schema\":\"public\",\"fields\":[{\"name\":\"userId\",\"dbName\":\"user_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entityId\",\"dbName\":\"entity_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"dbName\":\"created_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"default\":{\"name\":\"uuid\",\"args\":[4]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entity\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Entity\",\"nativeType\":null,\"relationName\":\"EntityToUserSavedEntity\",\"relationFromFields\":[\"entityId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"user\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"User\",\"nativeType\":null,\"relationName\":\"UserSavedEntities\",\"relationFromFields\":[\"userId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[[\"userId\",\"entityId\"]],\"uniqueIndexes\":[{\"name\":null,\"fields\":[\"userId\",\"entityId\"]}],\"isGenerated\":false},\"UserUpvote\":{\"dbName\":\"user_upvotes\",\"schema\":\"public\",\"fields\":[{\"name\":\"user\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"User\",\"nativeType\":null,\"relationName\":\"UserToUserUpvote\",\"relationFromFields\":[\"userId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"userId\",\"dbName\":\"user_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entity\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Entity\",\"nativeType\":null,\"relationName\":\"EntityToUserUpvote\",\"relationFromFields\":[\"entityId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entityId\",\"dbName\":\"entity_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"dbName\":\"created_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":{\"name\":null,\"fields\":[\"userId\",\"entityId\"]},\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"UserFollowedTag\":{\"dbName\":\"user_followed_tags\",\"schema\":\"public\",\"fields\":[{\"name\":\"userId\",\"dbName\":\"user_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"tagId\",\"dbName\":\"tag_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"dbName\":\"created_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"default\":{\"name\":\"uuid\",\"args\":[4]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"tag\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Tag\",\"nativeType\":null,\"relationName\":\"TagToUserFollowedTag\",\"relationFromFields\":[\"tagId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"user\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"User\",\"nativeType\":null,\"relationName\":\"UserFollowedTags\",\"relationFromFields\":[\"userId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[[\"userId\",\"tagId\"]],\"uniqueIndexes\":[{\"name\":null,\"fields\":[\"userId\",\"tagId\"]}],\"isGenerated\":false},\"UserFollowedCategory\":{\"dbName\":\"user_followed_categories\",\"schema\":\"public\",\"fields\":[{\"name\":\"userId\",\"dbName\":\"user_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"categoryId\",\"dbName\":\"category_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"followedAt\",\"dbName\":\"followed_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"category\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Category\",\"nativeType\":null,\"relationName\":\"CategoryToUserFollowedCategory\",\"relationFromFields\":[\"categoryId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"user\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"User\",\"nativeType\":null,\"relationName\":\"UserFollowedCategories\",\"relationFromFields\":[\"userId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":{\"name\":null,\"fields\":[\"userId\",\"categoryId\"]},\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"UserActivityLog\":{\"dbName\":\"user_activity_logs\",\"schema\":\"public\",\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"default\":{\"name\":\"uuid\",\"args\":[4]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"userId\",\"dbName\":\"user_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"actionType\",\"dbName\":\"action_type\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"ActionType\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entityId\",\"dbName\":\"entity_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"categoryId\",\"dbName\":\"category_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"tagId\",\"dbName\":\"tag_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"reviewId\",\"dbName\":\"review_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"targetUserId\",\"dbName\":\"target_user_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"details\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Json\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"dbName\":\"created_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"category\",\"kind\":\"object\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Category\",\"nativeType\":null,\"relationName\":\"CategoryToUserActivityLog\",\"relationFromFields\":[\"categoryId\"],\"relationToFields\":[\"id\"],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entity\",\"kind\":\"object\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Entity\",\"nativeType\":null,\"relationName\":\"EntityLogs\",\"relationFromFields\":[\"entityId\"],\"relationToFields\":[\"id\"],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"review\",\"kind\":\"object\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Review\",\"nativeType\":null,\"relationName\":\"ReviewLogs\",\"relationFromFields\":[\"reviewId\"],\"relationToFields\":[\"id\"],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"tag\",\"kind\":\"object\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Tag\",\"nativeType\":null,\"relationName\":\"TagLogs\",\"relationFromFields\":[\"tagId\"],\"relationToFields\":[\"id\"],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"targetUser\",\"kind\":\"object\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"User\",\"nativeType\":null,\"relationName\":\"TargetUserLogs\",\"relationFromFields\":[\"targetUserId\"],\"relationToFields\":[\"id\"],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"user\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"User\",\"nativeType\":null,\"relationName\":\"UserLogs\",\"relationFromFields\":[\"userId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"UserNotificationSettings\":{\"dbName\":\"user_notification_settings\",\"schema\":\"public\",\"fields\":[{\"name\":\"userId\",\"dbName\":\"user_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":true,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"emailNewsletter\",\"dbName\":\"email_newsletter\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Boolean\",\"nativeType\":null,\"default\":true,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"dbName\":\"created_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedAt\",\"dbName\":\"updated_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":true},{\"name\":\"emailMarketing\",\"dbName\":\"email_marketing\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Boolean\",\"nativeType\":null,\"default\":true,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"emailOnNewEntityInFollowed\",\"dbName\":\"email_on_new_entity_in_followed\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Boolean\",\"nativeType\":null,\"default\":true,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"emailOnNewFollower\",\"dbName\":\"email_on_new_follower\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Boolean\",\"nativeType\":null,\"default\":true,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"emailOnNewReview\",\"dbName\":\"email_on_new_review\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Boolean\",\"nativeType\":null,\"default\":true,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"emailOnReviewResponse\",\"dbName\":\"email_on_review_response\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Boolean\",\"nativeType\":null,\"default\":true,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"default\":{\"name\":\"uuid\",\"args\":[4]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"user\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"User\",\"nativeType\":null,\"relationName\":\"UserToUserNotificationSettings\",\"relationFromFields\":[\"userId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"BadgeType\":{\"dbName\":\"badge_types\",\"schema\":\"public\",\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"default\":{\"name\":\"uuid\",\"args\":[4]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"name\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":true,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"description\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"iconUrl\",\"dbName\":\"icon_url\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"scope\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"BadgeScope\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"dbName\":\"created_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedAt\",\"dbName\":\"updated_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":true},{\"name\":\"criteria\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Json\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entityBadges\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"EntityBadge\",\"nativeType\":null,\"relationName\":\"BadgeTypeEntity\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"userBadges\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"UserBadge\",\"nativeType\":null,\"relationName\":\"BadgeTypeUser\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"UserBadge\":{\"dbName\":\"user_badges\",\"schema\":\"public\",\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"default\":{\"name\":\"uuid\",\"args\":[4]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"userId\",\"dbName\":\"user_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"badgeTypeId\",\"dbName\":\"badge_type_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"grantedAt\",\"dbName\":\"granted_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"notes\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"grantedBy\",\"dbName\":\"granted_by\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"badgeType\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"BadgeType\",\"nativeType\":null,\"relationName\":\"BadgeTypeUser\",\"relationFromFields\":[\"badgeTypeId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"grantedByUser\",\"kind\":\"object\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"User\",\"nativeType\":null,\"relationName\":\"UserBadgesGranted\",\"relationFromFields\":[\"grantedBy\"],\"relationToFields\":[\"id\"],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"user\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"User\",\"nativeType\":null,\"relationName\":\"UserBadges\",\"relationFromFields\":[\"userId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[[\"userId\",\"badgeTypeId\"]],\"uniqueIndexes\":[{\"name\":null,\"fields\":[\"userId\",\"badgeTypeId\"]}],\"isGenerated\":false},\"EntityBadge\":{\"dbName\":\"entity_badges\",\"schema\":\"public\",\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"default\":{\"name\":\"uuid\",\"args\":[4]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entityId\",\"dbName\":\"entity_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"badgeTypeId\",\"dbName\":\"badge_type_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"grantedAt\",\"dbName\":\"granted_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"notes\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"expiresAt\",\"dbName\":\"expires_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"grantedBy\",\"dbName\":\"granted_by\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"badgeType\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"BadgeType\",\"nativeType\":null,\"relationName\":\"BadgeTypeEntity\",\"relationFromFields\":[\"badgeTypeId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entity\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Entity\",\"nativeType\":null,\"relationName\":\"EntityToEntityBadge\",\"relationFromFields\":[\"entityId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"grantedByUser\",\"kind\":\"object\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"User\",\"nativeType\":null,\"relationName\":\"EntityBadgesGranted\",\"relationFromFields\":[\"grantedBy\"],\"relationToFields\":[\"id\"],\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[[\"entityId\",\"badgeTypeId\"]],\"uniqueIndexes\":[{\"name\":null,\"fields\":[\"entityId\",\"badgeTypeId\"]}],\"isGenerated\":false},\"EntityDetailsDataset\":{\"dbName\":\"entity_details_dataset\",\"schema\":\"public\",\"fields\":[{\"name\":\"entityId\",\"dbName\":\"entity_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":true,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"dbName\":\"created_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedAt\",\"dbName\":\"updated_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":true},{\"name\":\"accessNotes\",\"dbName\":\"access_notes\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"description\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"license\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"sizeInBytes\",\"dbName\":\"size_in_bytes\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"BigInt\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"sourceUrl\",\"dbName\":\"source_url\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"collectionMethod\",\"dbName\":\"collection_method\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"default\":{\"name\":\"uuid\",\"args\":[4]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updateFrequency\",\"dbName\":\"update_frequency\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"format\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entity\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Entity\",\"nativeType\":null,\"relationName\":\"EntityToEntityDetailsDataset\",\"relationFromFields\":[\"entityId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"EntityDetailsResearchPaper\":{\"dbName\":\"entity_details_research_paper\",\"schema\":\"public\",\"fields\":[{\"name\":\"entityId\",\"dbName\":\"entity_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":true,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"dbName\":\"created_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedAt\",\"dbName\":\"updated_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":true},{\"name\":\"abstract\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"citationCount\",\"dbName\":\"citation_count\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Int\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"doi\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"journalOrConference\",\"dbName\":\"journal_or_conference\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"publicationDate\",\"dbName\":\"publication_date\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"default\":{\"name\":\"uuid\",\"args\":[4]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"pdfUrl\",\"dbName\":\"pdf_url\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"authors\",\"kind\":\"scalar\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"researchAreas\",\"dbName\":\"research_areas\",\"kind\":\"scalar\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"publicationVenues\",\"dbName\":\"publication_venues\",\"kind\":\"scalar\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"keywords\",\"kind\":\"scalar\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"arxivId\",\"dbName\":\"arxiv_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entity\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Entity\",\"nativeType\":null,\"relationName\":\"EntityToEntityDetailsResearchPaper\",\"relationFromFields\":[\"entityId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"EntityDetailsSoftware\":{\"dbName\":\"entity_details_software\",\"schema\":\"public\",\"fields\":[{\"name\":\"entityId\",\"dbName\":\"entity_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":true,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"dbName\":\"created_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedAt\",\"dbName\":\"updated_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":true},{\"name\":\"currentVersion\",\"dbName\":\"current_version\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"licenseType\",\"dbName\":\"license_type\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"communityUrl\",\"dbName\":\"community_url\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"hasFreeTier\",\"dbName\":\"has_free_tier\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Boolean\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"hasLiveChat\",\"dbName\":\"has_live_chat\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Boolean\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"priceRange\",\"dbName\":\"price_range\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"PriceRange\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"pricingDetails\",\"dbName\":\"pricing_details\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"pricingModel\",\"dbName\":\"pricing_model\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"PricingModel\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"pricingUrl\",\"dbName\":\"pricing_url\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"supportEmail\",\"dbName\":\"support_email\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"apiAccess\",\"dbName\":\"api_access\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Boolean\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"customizationLevel\",\"dbName\":\"customization_level\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"demoAvailable\",\"dbName\":\"demo_available\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Boolean\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"deploymentOptions\",\"dbName\":\"deployment_options\",\"kind\":\"scalar\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"frameworks\",\"kind\":\"scalar\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"hasApi\",\"dbName\":\"has_api\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Boolean\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"default\":{\"name\":\"uuid\",\"args\":[4]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"keyFeatures\",\"dbName\":\"key_features\",\"kind\":\"scalar\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"libraries\",\"kind\":\"scalar\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"mobileSupport\",\"dbName\":\"mobile_support\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Boolean\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"openSource\",\"dbName\":\"open_source\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Boolean\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"supportChannels\",\"dbName\":\"support_channels\",\"kind\":\"scalar\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"supportedOs\",\"dbName\":\"supported_os\",\"kind\":\"scalar\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"targetAudience\",\"dbName\":\"target_audience\",\"kind\":\"scalar\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"trialAvailable\",\"dbName\":\"trial_available\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Boolean\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"integrations\",\"kind\":\"scalar\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"platformCompatibility\",\"dbName\":\"platform_compatibility\",\"kind\":\"scalar\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"programmingLanguages\",\"dbName\":\"programming_languages\",\"kind\":\"scalar\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"useCases\",\"dbName\":\"use_cases\",\"kind\":\"scalar\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"repositoryUrl\",\"dbName\":\"repository_url\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"releaseDate\",\"dbName\":\"release_date\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entity\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Entity\",\"nativeType\":null,\"relationName\":\"EntityToEntityDetailsSoftware\",\"relationFromFields\":[\"entityId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"EntityDetailsModel\":{\"dbName\":\"entity_details_model\",\"schema\":\"public\",\"fields\":[{\"name\":\"entityId\",\"dbName\":\"entity_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":true,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"dbName\":\"created_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedAt\",\"dbName\":\"updated_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":true},{\"name\":\"license\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"modelArchitecture\",\"dbName\":\"model_architecture\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"trainingDataset\",\"dbName\":\"training_dataset\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"deploymentOptions\",\"dbName\":\"deployment_options\",\"kind\":\"scalar\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"frameworks\",\"kind\":\"scalar\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"default\":{\"name\":\"uuid\",\"args\":[4]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"inputDataTypes\",\"dbName\":\"input_data_types\",\"kind\":\"scalar\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"libraries\",\"kind\":\"scalar\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"outputDataTypes\",\"dbName\":\"output_data_types\",\"kind\":\"scalar\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"performanceMetrics\",\"dbName\":\"performance_metrics\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Json\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"targetAudience\",\"dbName\":\"target_audience\",\"kind\":\"scalar\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"useCases\",\"dbName\":\"use_cases\",\"kind\":\"scalar\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entity\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Entity\",\"nativeType\":null,\"relationName\":\"EntityToEntityDetailsModel\",\"relationFromFields\":[\"entityId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"EntityDetailsProjectReference\":{\"dbName\":\"entity_details_project_reference\",\"schema\":\"public\",\"fields\":[{\"name\":\"entityId\",\"dbName\":\"entity_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":true,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"dbName\":\"created_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedAt\",\"dbName\":\"updated_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":true},{\"name\":\"forks\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Int\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"default\":{\"name\":\"uuid\",\"args\":[4]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"keyTechnologies\",\"dbName\":\"key_technologies\",\"kind\":\"scalar\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"license\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"repositoryUrl\",\"dbName\":\"repository_url\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"stars\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Int\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"status\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"useCases\",\"dbName\":\"use_cases\",\"kind\":\"scalar\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"contributors\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Int\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entity\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Entity\",\"nativeType\":null,\"relationName\":\"EntityToEntityDetailsProjectReference\",\"relationFromFields\":[\"entityId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"EntityDetailsServiceProvider\":{\"dbName\":\"entity_details_service_provider\",\"schema\":\"public\",\"fields\":[{\"name\":\"entityId\",\"dbName\":\"entity_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":true,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"dbName\":\"created_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedAt\",\"dbName\":\"updated_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":true},{\"name\":\"companySizeFocus\",\"dbName\":\"company_size_focus\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"default\":{\"name\":\"uuid\",\"args\":[4]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"industrySpecializations\",\"dbName\":\"industry_specializations\",\"kind\":\"scalar\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"locationSummary\",\"dbName\":\"location_summary\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"portfolioUrl\",\"dbName\":\"portfolio_url\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"pricingInfo\",\"dbName\":\"pricing_info\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"servicesOffered\",\"dbName\":\"services_offered\",\"kind\":\"scalar\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"targetAudience\",\"dbName\":\"target_audience\",\"kind\":\"scalar\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entity\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Entity\",\"nativeType\":null,\"relationName\":\"EntityToEntityDetailsServiceProvider\",\"relationFromFields\":[\"entityId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"EntityDetailsInvestor\":{\"dbName\":\"entity_details_investor\",\"schema\":\"public\",\"fields\":[{\"name\":\"entityId\",\"dbName\":\"entity_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":true,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"dbName\":\"created_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedAt\",\"dbName\":\"updated_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":true},{\"name\":\"contactEmail\",\"dbName\":\"contact_email\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"applicationUrl\",\"dbName\":\"application_url\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"focusAreas\",\"dbName\":\"focus_areas\",\"kind\":\"scalar\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"default\":{\"name\":\"uuid\",\"args\":[4]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"investmentStages\",\"dbName\":\"investment_stages\",\"kind\":\"scalar\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"investorType\",\"dbName\":\"investor_type\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"locationSummary\",\"dbName\":\"location_summary\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"notableInvestments\",\"dbName\":\"notable_investments\",\"kind\":\"scalar\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"portfolioSize\",\"dbName\":\"portfolio_size\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Int\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entity\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Entity\",\"nativeType\":null,\"relationName\":\"EntityToEntityDetailsInvestor\",\"relationFromFields\":[\"entityId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"EntityDetailsEvent\":{\"dbName\":\"entity_details_event\",\"schema\":\"public\",\"fields\":[{\"name\":\"entityId\",\"dbName\":\"entity_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":true,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"dbName\":\"created_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedAt\",\"dbName\":\"updated_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":true},{\"name\":\"endDate\",\"dbName\":\"end_date\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"location\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"price\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"registrationUrl\",\"dbName\":\"registration_url\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"startDate\",\"dbName\":\"start_date\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"eventType\",\"dbName\":\"event_type\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"default\":{\"name\":\"uuid\",\"args\":[4]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"isOnline\",\"dbName\":\"is_online\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Boolean\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"keySpeakers\",\"dbName\":\"key_speakers\",\"kind\":\"scalar\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"targetAudience\",\"dbName\":\"target_audience\",\"kind\":\"scalar\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"topics\",\"kind\":\"scalar\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"registrationRequired\",\"dbName\":\"registration_required\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Boolean\",\"nativeType\":null,\"default\":false,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"capacity\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Int\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"organizer\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"eventFormat\",\"dbName\":\"event_format\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"EventFormatEnum\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entity\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Entity\",\"nativeType\":null,\"relationName\":\"EntityToEntityDetailsEvent\",\"relationFromFields\":[\"entityId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"EntityDetailsJob\":{\"dbName\":\"entity_details_job\",\"schema\":\"public\",\"fields\":[{\"name\":\"entityId\",\"dbName\":\"entity_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":true,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"dbName\":\"created_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedAt\",\"dbName\":\"updated_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":true},{\"name\":\"applicationUrl\",\"dbName\":\"application_url\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"companyName\",\"dbName\":\"company_name\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"experienceLevel\",\"dbName\":\"experience_level\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"ExperienceLevelEnum\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"default\":{\"name\":\"uuid\",\"args\":[4]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"isRemote\",\"dbName\":\"is_remote\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Boolean\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"jobType\",\"dbName\":\"job_type\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"keyResponsibilities\",\"dbName\":\"key_responsibilities\",\"kind\":\"scalar\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"location\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"requiredSkills\",\"dbName\":\"required_skills\",\"kind\":\"scalar\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"salaryMax\",\"dbName\":\"salary_max\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Float\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"salaryMin\",\"dbName\":\"salary_min\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Float\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"jobDescription\",\"dbName\":\"job_description\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"employmentTypes\",\"dbName\":\"employment_types\",\"kind\":\"enum\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"EmploymentTypeEnum\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"locationTypes\",\"dbName\":\"location_types\",\"kind\":\"enum\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"LocationTypeEnum\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"benefits\",\"kind\":\"scalar\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"remotePolicy\",\"dbName\":\"remote_policy\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"visaSponsorship\",\"dbName\":\"visa_sponsorship\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Boolean\",\"nativeType\":null,\"default\":false,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entity\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Entity\",\"nativeType\":null,\"relationName\":\"EntityToEntityDetailsJob\",\"relationFromFields\":[\"entityId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"EntityDetailsGrant\":{\"dbName\":\"entity_details_grant\",\"schema\":\"public\",\"fields\":[{\"name\":\"entityId\",\"dbName\":\"entity_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":true,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"dbName\":\"created_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedAt\",\"dbName\":\"updated_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":true},{\"name\":\"applicationUrl\",\"dbName\":\"application_url\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"amount\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"deadline\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"eligibility\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"focusAreas\",\"dbName\":\"focus_areas\",\"kind\":\"scalar\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"funderName\",\"dbName\":\"funder_name\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"grantType\",\"dbName\":\"grant_type\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"default\":{\"name\":\"uuid\",\"args\":[4]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"location\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entity\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Entity\",\"nativeType\":null,\"relationName\":\"EntityToEntityDetailsGrant\",\"relationFromFields\":[\"entityId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"EntityDetailsBounty\":{\"dbName\":\"entity_details_bounty\",\"schema\":\"public\",\"fields\":[{\"name\":\"entityId\",\"dbName\":\"entity_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":true,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"dbName\":\"created_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedAt\",\"dbName\":\"updated_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":true},{\"name\":\"amount\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"deadline\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"default\":{\"name\":\"uuid\",\"args\":[4]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"platform\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"requiredSkills\",\"dbName\":\"required_skills\",\"kind\":\"scalar\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"status\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"taskDescription\",\"dbName\":\"task_description\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"url\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entity\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Entity\",\"nativeType\":null,\"relationName\":\"EntityToEntityDetailsBounty\",\"relationFromFields\":[\"entityId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"EntityDetailsHardware\":{\"dbName\":\"entity_details_hardware\",\"schema\":\"public\",\"fields\":[{\"name\":\"entityId\",\"dbName\":\"entity_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":true,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"dbName\":\"created_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedAt\",\"dbName\":\"updated_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":true},{\"name\":\"availability\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"gpu\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"default\":{\"name\":\"uuid\",\"args\":[4]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"memory\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"powerConsumption\",\"dbName\":\"power_consumption\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"price\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"processor\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"storage\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"useCases\",\"dbName\":\"use_cases\",\"kind\":\"scalar\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"hardwareType\",\"dbName\":\"hardware_type\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"HardwareTypeEnum\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"manufacturer\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"releaseDate\",\"dbName\":\"release_date\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"specifications\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Json\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"datasheetUrl\",\"dbName\":\"datasheet_url\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entity\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Entity\",\"nativeType\":null,\"relationName\":\"EntityToEntityDetailsHardware\",\"relationFromFields\":[\"entityId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"EntityDetailsNews\":{\"dbName\":\"entity_details_news\",\"schema\":\"public\",\"fields\":[{\"name\":\"entityId\",\"dbName\":\"entity_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":true,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"dbName\":\"created_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedAt\",\"dbName\":\"updated_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":true},{\"name\":\"articleUrl\",\"dbName\":\"article_url\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"author\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"publicationDate\",\"dbName\":\"publication_date\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"sourceName\",\"dbName\":\"source_name\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"summary\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"default\":{\"name\":\"uuid\",\"args\":[4]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"tags\",\"kind\":\"scalar\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entity\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Entity\",\"nativeType\":null,\"relationName\":\"EntityToEntityDetailsNews\",\"relationFromFields\":[\"entityId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"EntityDetailsBook\":{\"dbName\":\"entity_details_book\",\"schema\":\"public\",\"fields\":[{\"name\":\"entityId\",\"dbName\":\"entity_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":true,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"dbName\":\"created_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedAt\",\"dbName\":\"updated_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":true},{\"name\":\"isbn\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"pageCount\",\"dbName\":\"page_count\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Int\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"publisher\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"purchaseUrl\",\"dbName\":\"purchase_url\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"summary\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"author\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"format\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"default\":{\"name\":\"uuid\",\"args\":[4]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"publicationDate\",\"dbName\":\"publication_date\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entity\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Entity\",\"nativeType\":null,\"relationName\":\"EntityToEntityDetailsBook\",\"relationFromFields\":[\"entityId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"EntityDetailsPodcast\":{\"dbName\":\"entity_details_podcast\",\"schema\":\"public\",\"fields\":[{\"name\":\"entityId\",\"dbName\":\"entity_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":true,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"dbName\":\"created_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedAt\",\"dbName\":\"updated_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":true},{\"name\":\"frequency\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"applePodcastsUrl\",\"dbName\":\"apple_podcasts_url\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"averageLength\",\"dbName\":\"average_length\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"googlePodcastsUrl\",\"dbName\":\"google_podcasts_url\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"host\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"default\":{\"name\":\"uuid\",\"args\":[4]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"mainTopics\",\"dbName\":\"main_topics\",\"kind\":\"scalar\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"spotifyUrl\",\"dbName\":\"spotify_url\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"youtubeUrl\",\"dbName\":\"youtube_url\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entity\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Entity\",\"nativeType\":null,\"relationName\":\"EntityToEntityDetailsPodcast\",\"relationFromFields\":[\"entityId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"EntityDetailsPlatform\":{\"dbName\":\"entity_details_platform\",\"schema\":\"public\",\"fields\":[{\"name\":\"entityId\",\"dbName\":\"entity_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":true,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"dbName\":\"created_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedAt\",\"dbName\":\"updated_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":true},{\"name\":\"documentationUrl\",\"dbName\":\"documentation_url\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"platformType\",\"dbName\":\"platform_type\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"communityUrl\",\"dbName\":\"community_url\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"hasFreeTier\",\"dbName\":\"has_free_tier\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Boolean\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"hasLiveChat\",\"dbName\":\"has_live_chat\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Boolean\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"priceRange\",\"dbName\":\"price_range\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"PriceRange\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"pricingDetails\",\"dbName\":\"pricing_details\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"pricingUrl\",\"dbName\":\"pricing_url\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"supportEmail\",\"dbName\":\"support_email\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"pricingModel\",\"dbName\":\"pricing_model\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"PricingModel\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"apiAccess\",\"dbName\":\"api_access\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Boolean\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"customizationLevel\",\"dbName\":\"customization_level\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"demoAvailable\",\"dbName\":\"demo_available\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Boolean\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"deploymentOptions\",\"dbName\":\"deployment_options\",\"kind\":\"scalar\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"hasApi\",\"dbName\":\"has_api\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Boolean\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"default\":{\"name\":\"uuid\",\"args\":[4]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"mobileSupport\",\"dbName\":\"mobile_support\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Boolean\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"openSource\",\"dbName\":\"open_source\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Boolean\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"supportChannels\",\"dbName\":\"support_channels\",\"kind\":\"scalar\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"supportedOs\",\"dbName\":\"supported_os\",\"kind\":\"scalar\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"targetAudience\",\"dbName\":\"target_audience\",\"kind\":\"scalar\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"trialAvailable\",\"dbName\":\"trial_available\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Boolean\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"integrations\",\"kind\":\"scalar\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"keyServices\",\"dbName\":\"key_services\",\"kind\":\"scalar\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"useCases\",\"dbName\":\"use_cases\",\"kind\":\"scalar\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entity\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Entity\",\"nativeType\":null,\"relationName\":\"EntityToEntityDetailsPlatform\",\"relationFromFields\":[\"entityId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"EntityFeature\":{\"dbName\":\"entity_features\",\"schema\":\"public\",\"fields\":[{\"name\":\"entityId\",\"dbName\":\"entity_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"featureId\",\"dbName\":\"feature_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"assignedBy\",\"dbName\":\"assigned_by\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"dbName\":\"created_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"default\":{\"name\":\"uuid\",\"args\":[4]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entity\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Entity\",\"nativeType\":null,\"relationName\":\"EntityToEntityFeature\",\"relationFromFields\":[\"entityId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"feature\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Feature\",\"nativeType\":null,\"relationName\":\"EntityFeatureToFeature\",\"relationFromFields\":[\"featureId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[[\"entityId\",\"featureId\"]],\"uniqueIndexes\":[{\"name\":null,\"fields\":[\"entityId\",\"featureId\"]}],\"isGenerated\":false},\"AppSetting\":{\"dbName\":\"app_settings\",\"schema\":\"public\",\"fields\":[{\"name\":\"key\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"value\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"description\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"dbName\":\"created_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedAt\",\"dbName\":\"updated_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":true}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"Feature\":{\"dbName\":\"features\",\"schema\":\"public\",\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"default\":{\"name\":\"uuid\",\"args\":[4]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"name\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":true,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"slug\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":true,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"description\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"dbName\":\"created_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedAt\",\"dbName\":\"updated_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":true},{\"name\":\"iconUrl\",\"dbName\":\"icon_url\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entityFeatures\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"EntityFeature\",\"nativeType\":null,\"relationName\":\"EntityFeatureToFeature\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"UserPreferences\":{\"dbName\":\"user_preferences\",\"schema\":\"public\",\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"default\":{\"name\":\"uuid\",\"args\":[4]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"userId\",\"dbName\":\"user_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":true,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"emailNotifications\",\"dbName\":\"email_notifications\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Boolean\",\"nativeType\":null,\"default\":true,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"marketingEmails\",\"dbName\":\"marketing_emails\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Boolean\",\"nativeType\":null,\"default\":false,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"weeklyDigest\",\"dbName\":\"weekly_digest\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Boolean\",\"nativeType\":null,\"default\":true,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"newToolAlerts\",\"dbName\":\"new_tool_alerts\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Boolean\",\"nativeType\":null,\"default\":true,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"profileVisibility\",\"dbName\":\"profile_visibility\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"ProfileVisibility\",\"nativeType\":null,\"default\":\"PUBLIC\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"showBookmarks\",\"dbName\":\"show_bookmarks\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Boolean\",\"nativeType\":null,\"default\":true,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"showReviews\",\"dbName\":\"show_reviews\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Boolean\",\"nativeType\":null,\"default\":true,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"showActivity\",\"dbName\":\"show_activity\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Boolean\",\"nativeType\":null,\"default\":true,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"theme\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Theme\",\"nativeType\":null,\"default\":\"LIGHT\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"itemsPerPage\",\"dbName\":\"items_per_page\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Int\",\"nativeType\":null,\"default\":20,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"defaultView\",\"dbName\":\"default_view\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DefaultView\",\"nativeType\":null,\"default\":\"GRID\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"preferredCategories\",\"dbName\":\"preferred_categories\",\"kind\":\"scalar\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"blockedCategories\",\"dbName\":\"blocked_categories\",\"kind\":\"scalar\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"contentLanguage\",\"dbName\":\"content_language\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":null,\"default\":\"en\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"dbName\":\"created_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedAt\",\"dbName\":\"updated_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":true},{\"name\":\"user\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"User\",\"nativeType\":null,\"relationName\":\"UserToUserPreferences\",\"relationFromFields\":[\"userId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"ToolRequest\":{\"dbName\":\"tool_requests\",\"schema\":\"public\",\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"default\":{\"name\":\"uuid\",\"args\":[4]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"userId\",\"dbName\":\"user_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"toolName\",\"dbName\":\"tool_name\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"description\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"reason\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"categorySuggestion\",\"dbName\":\"category_suggestion\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"websiteUrl\",\"dbName\":\"website_url\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"priority\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"ToolRequestPriority\",\"nativeType\":null,\"default\":\"MEDIUM\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"status\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"ToolRequestStatus\",\"nativeType\":null,\"default\":\"PENDING\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"adminNotes\",\"dbName\":\"admin_notes\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"votes\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Int\",\"nativeType\":null,\"default\":0,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"dbName\":\"created_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedAt\",\"dbName\":\"updated_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":true},{\"name\":\"user\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"User\",\"nativeType\":null,\"relationName\":\"ToolRequestToUser\",\"relationFromFields\":[\"userId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"UserSubmittedTool\":{\"dbName\":\"user_submitted_tools\",\"schema\":\"public\",\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"default\":{\"name\":\"uuid\",\"args\":[4]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"userId\",\"dbName\":\"user_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entityId\",\"dbName\":\"entity_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"submissionStatus\",\"dbName\":\"submission_status\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"SubmissionStatus\",\"nativeType\":null,\"default\":\"PENDING\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"submittedAt\",\"dbName\":\"submitted_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"reviewedAt\",\"dbName\":\"reviewed_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"reviewerId\",\"dbName\":\"reviewer_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"reviewerNotes\",\"dbName\":\"reviewer_notes\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"changesRequested\",\"dbName\":\"changes_requested\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entity\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Entity\",\"nativeType\":null,\"relationName\":\"EntityToUserSubmittedTool\",\"relationFromFields\":[\"entityId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"reviewer\",\"kind\":\"object\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"User\",\"nativeType\":null,\"relationName\":\"ReviewerSubmissions\",\"relationFromFields\":[\"reviewerId\"],\"relationToFields\":[\"id\"],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"user\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"User\",\"nativeType\":null,\"relationName\":\"UserToUserSubmittedTool\",\"relationFromFields\":[\"userId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[[\"userId\",\"entityId\"]],\"uniqueIndexes\":[{\"name\":null,\"fields\":[\"userId\",\"entityId\"]}],\"isGenerated\":false},\"ProfileActivity\":{\"dbName\":\"profile_activities\",\"schema\":\"public\",\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"default\":{\"name\":\"uuid\",\"args\":[4]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"userId\",\"dbName\":\"user_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"type\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"ProfileActivityType\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"description\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entityId\",\"dbName\":\"entity_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entityName\",\"dbName\":\"entity_name\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entitySlug\",\"dbName\":\"entity_slug\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"dbName\":\"created_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entity\",\"kind\":\"object\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Entity\",\"nativeType\":null,\"relationName\":\"EntityToProfileActivity\",\"relationFromFields\":[\"entityId\"],\"relationToFields\":[\"id\"],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"user\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"User\",\"nativeType\":null,\"relationName\":\"ProfileActivityToUser\",\"relationFromFields\":[\"userId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false}},\"enums\":{\"UserRole\":{\"values\":[{\"name\":\"USER\",\"dbName\":null},{\"name\":\"ADMIN\",\"dbName\":null},{\"name\":\"MODERATOR\",\"dbName\":null}],\"dbName\":null},\"UserStatus\":{\"values\":[{\"name\":\"ACTIVE\",\"dbName\":null},{\"name\":\"INACTIVE\",\"dbName\":null},{\"name\":\"PENDING\",\"dbName\":null},{\"name\":\"SUSPENDED\",\"dbName\":null},{\"name\":\"DELETED\",\"dbName\":null}],\"dbName\":null},\"TechnicalLevel\":{\"values\":[{\"name\":\"BEGINNER\",\"dbName\":null},{\"name\":\"INTERMEDIATE\",\"dbName\":null},{\"name\":\"ADVANCED\",\"dbName\":null},{\"name\":\"EXPERT\",\"dbName\":null}],\"dbName\":null},\"EntityStatus\":{\"values\":[{\"name\":\"PENDING\",\"dbName\":null},{\"name\":\"ACTIVE\",\"dbName\":null},{\"name\":\"REJECTED\",\"dbName\":null},{\"name\":\"INACTIVE\",\"dbName\":null},{\"name\":\"ARCHIVED\",\"dbName\":null},{\"name\":\"NEEDS_REVISION\",\"dbName\":null}],\"dbName\":null},\"AffiliateStatus\":{\"values\":[{\"name\":\"NONE\",\"dbName\":null},{\"name\":\"APPLIED\",\"dbName\":null},{\"name\":\"APPROVED\",\"dbName\":null},{\"name\":\"REJECTED\",\"dbName\":null}],\"dbName\":null},\"ReviewStatus\":{\"values\":[{\"name\":\"PENDING\",\"dbName\":null},{\"name\":\"APPROVED\",\"dbName\":null},{\"name\":\"REJECTED\",\"dbName\":null}],\"dbName\":null},\"LearningCurve\":{\"values\":[{\"name\":\"LOW\",\"dbName\":null},{\"name\":\"MEDIUM\",\"dbName\":null},{\"name\":\"HIGH\",\"dbName\":null}],\"dbName\":null},\"PricingModel\":{\"values\":[{\"name\":\"FREE\",\"dbName\":null},{\"name\":\"FREEMIUM\",\"dbName\":null},{\"name\":\"SUBSCRIPTION\",\"dbName\":null},{\"name\":\"PAY_PER_USE\",\"dbName\":null},{\"name\":\"ONE_TIME_PURCHASE\",\"dbName\":null},{\"name\":\"CONTACT_SALES\",\"dbName\":null},{\"name\":\"OPEN_SOURCE\",\"dbName\":null}],\"dbName\":null},\"PriceRange\":{\"values\":[{\"name\":\"FREE\",\"dbName\":null},{\"name\":\"LOW\",\"dbName\":null},{\"name\":\"MEDIUM\",\"dbName\":null},{\"name\":\"HIGH\",\"dbName\":null},{\"name\":\"ENTERPRISE\",\"dbName\":null}],\"dbName\":null},\"SkillLevel\":{\"values\":[{\"name\":\"BEGINNER\",\"dbName\":null},{\"name\":\"INTERMEDIATE\",\"dbName\":null},{\"name\":\"ADVANCED\",\"dbName\":null},{\"name\":\"EXPERT\",\"dbName\":null}],\"dbName\":null},\"ActionType\":{\"values\":[{\"name\":\"VIEW_ENTITY\",\"dbName\":null},{\"name\":\"CLICK_ENTITY_LINK\",\"dbName\":null},{\"name\":\"SAVE_ENTITY\",\"dbName\":null},{\"name\":\"UNSAVE_ENTITY\",\"dbName\":null},{\"name\":\"SUBMIT_REVIEW\",\"dbName\":null},{\"name\":\"VOTE_REVIEW\",\"dbName\":null},{\"name\":\"FOLLOW_TAG\",\"dbName\":null},{\"name\":\"UNFOLLOW_TAG\",\"dbName\":null},{\"name\":\"FOLLOW_CATEGORY\",\"dbName\":null},{\"name\":\"UNFOLLOW_CATEGORY\",\"dbName\":null},{\"name\":\"SEARCH\",\"dbName\":null},{\"name\":\"LOGIN\",\"dbName\":null},{\"name\":\"LOGOUT\",\"dbName\":null},{\"name\":\"SIGNUP\",\"dbName\":null},{\"name\":\"UPDATE_PROFILE\",\"dbName\":null},{\"name\":\"GRANT_BADGE\",\"dbName\":null},{\"name\":\"REVOKE_BADGE\",\"dbName\":null}],\"dbName\":null},\"BadgeScope\":{\"values\":[{\"name\":\"USER\",\"dbName\":null},{\"name\":\"ENTITY\",\"dbName\":null}],\"dbName\":null},\"EmployeeCountRange\":{\"values\":[{\"name\":\"C1_10\",\"dbName\":null},{\"name\":\"C11_50\",\"dbName\":null},{\"name\":\"C51_200\",\"dbName\":null},{\"name\":\"C201_500\",\"dbName\":null},{\"name\":\"C501_1000\",\"dbName\":null},{\"name\":\"C1001_5000\",\"dbName\":null},{\"name\":\"C5001_PLUS\",\"dbName\":null}],\"dbName\":null},\"FundingStage\":{\"values\":[{\"name\":\"SEED\",\"dbName\":null},{\"name\":\"PRE_SEED\",\"dbName\":null},{\"name\":\"SERIES_A\",\"dbName\":null},{\"name\":\"SERIES_B\",\"dbName\":null},{\"name\":\"SERIES_C\",\"dbName\":null},{\"name\":\"SERIES_D_PLUS\",\"dbName\":null},{\"name\":\"PUBLIC\",\"dbName\":null}],\"dbName\":null},\"ProfileVisibility\":{\"values\":[{\"name\":\"PUBLIC\",\"dbName\":null},{\"name\":\"PRIVATE\",\"dbName\":null},{\"name\":\"FRIENDS\",\"dbName\":null}],\"dbName\":null},\"Theme\":{\"values\":[{\"name\":\"LIGHT\",\"dbName\":null},{\"name\":\"DARK\",\"dbName\":null},{\"name\":\"SYSTEM\",\"dbName\":null}],\"dbName\":null},\"DefaultView\":{\"values\":[{\"name\":\"GRID\",\"dbName\":null},{\"name\":\"LIST\",\"dbName\":null}],\"dbName\":null},\"ToolRequestPriority\":{\"values\":[{\"name\":\"LOW\",\"dbName\":null},{\"name\":\"MEDIUM\",\"dbName\":null},{\"name\":\"HIGH\",\"dbName\":null}],\"dbName\":null},\"ToolRequestStatus\":{\"values\":[{\"name\":\"PENDING\",\"dbName\":null},{\"name\":\"UNDER_REVIEW\",\"dbName\":null},{\"name\":\"APPROVED\",\"dbName\":null},{\"name\":\"REJECTED\",\"dbName\":null},{\"name\":\"COMPLETED\",\"dbName\":null}],\"dbName\":null},\"SubmissionStatus\":{\"values\":[{\"name\":\"PENDING\",\"dbName\":null},{\"name\":\"UNDER_REVIEW\",\"dbName\":null},{\"name\":\"APPROVED\",\"dbName\":null},{\"name\":\"REJECTED\",\"dbName\":null},{\"name\":\"PUBLISHED\",\"dbName\":null}],\"dbName\":null},\"ProfileActivityType\":{\"values\":[{\"name\":\"BOOKMARK\",\"dbName\":null},{\"name\":\"REVIEW\",\"dbName\":null},{\"name\":\"SUBMISSION\",\"dbName\":null},{\"name\":\"REQUEST\",\"dbName\":null},{\"name\":\"VOTE\",\"dbName\":null}],\"dbName\":null},\"HardwareTypeEnum\":{\"values\":[{\"name\":\"GPU\",\"dbName\":null},{\"name\":\"CPU\",\"dbName\":null},{\"name\":\"FPGA\",\"dbName\":null},{\"name\":\"TPU\",\"dbName\":null},{\"name\":\"ASIC\",\"dbName\":null},{\"name\":\"NPU\",\"dbName\":null},{\"name\":\"Memory\",\"dbName\":null},{\"name\":\"Storage\",\"dbName\":null}],\"dbName\":null},\"EmploymentTypeEnum\":{\"values\":[{\"name\":\"FULL_TIME\",\"dbName\":null},{\"name\":\"PART_TIME\",\"dbName\":null},{\"name\":\"CONTRACT\",\"dbName\":null},{\"name\":\"FREELANCE\",\"dbName\":null},{\"name\":\"INTERNSHIP\",\"dbName\":null},{\"name\":\"TEMPORARY\",\"dbName\":null}],\"dbName\":null},\"ExperienceLevelEnum\":{\"values\":[{\"name\":\"ENTRY\",\"dbName\":null},{\"name\":\"JUNIOR\",\"dbName\":null},{\"name\":\"MID\",\"dbName\":null},{\"name\":\"SENIOR\",\"dbName\":null},{\"name\":\"LEAD\",\"dbName\":null},{\"name\":\"PRINCIPAL\",\"dbName\":null},{\"name\":\"DIRECTOR\",\"dbName\":null}],\"dbName\":null},\"LocationTypeEnum\":{\"values\":[{\"name\":\"Remote\",\"dbName\":null},{\"name\":\"On_site\",\"dbName\":null},{\"name\":\"Hybrid\",\"dbName\":null}],\"dbName\":null},\"EventFormatEnum\":{\"values\":[{\"name\":\"in_person\",\"dbName\":null},{\"name\":\"virtual\",\"dbName\":null},{\"name\":\"hybrid\",\"dbName\":null}],\"dbName\":null}},\"types\":{}}")
defineDmmfProperty(exports.Prisma, config.runtimeDataModel)
config.engineWasm = undefined
config.compilerWasm = undefined

config.injectableEdgeEnv = () => ({
  parsed: {
    DATABASE_URL: typeof globalThis !== 'undefined' && globalThis['DATABASE_URL'] || typeof process !== 'undefined' && process.env && process.env.DATABASE_URL || undefined
  }
})

if (typeof globalThis !== 'undefined' && globalThis['DEBUG'] || typeof process !== 'undefined' && process.env && process.env.DEBUG || undefined) {
  Debug.enable(typeof globalThis !== 'undefined' && globalThis['DEBUG'] || typeof process !== 'undefined' && process.env && process.env.DEBUG || undefined)
}

const PrismaClient = getPrismaClient(config)
exports.PrismaClient = PrismaClient
Object.assign(exports, Prisma)

