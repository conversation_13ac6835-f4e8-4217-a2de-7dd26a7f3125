import { Modu<PERSON> } from '@nestjs/common';
import { RecommendationsController } from './recommendations.controller';
import { RecommendationsService } from './recommendations.service';
import { FilterExtractionService } from './services/filter-extraction.service';
import { AdvancedEntityRankingService } from '../common/ranking/advanced-entity-ranking.service';
import { PerformanceOptimizationService } from '../common/performance/performance-optimization.service';
import { QueryOptimizationService } from '../common/performance/query-optimization.service';
import { EntitiesModule } from '../entities/entities.module';
import { LlmModule } from '../common/llm/llm.module';
import { AuthModule } from '../auth/auth.module';

@Module({
  imports: [
    EntitiesModule, // For EntitiesService to perform vector search
    LlmModule,      // For ILlmService and LlmFactoryService
    AuthModule,     // For authentication guards
  ],
  controllers: [RecommendationsController],
  providers: [
    RecommendationsService,
    FilterExtractionService, // Service for extracting filters from natural language
    AdvancedEntityRankingService, // Advanced multi-factor entity ranking
    PerformanceOptimizationService, // Performance optimization and caching
    QueryOptimizationService, // Database query optimization
  ],
  exports: [
    RecommendationsService,
    FilterExtractionService, // Export for use in ChatModule
  ],
})
export class RecommendationsModule {}
