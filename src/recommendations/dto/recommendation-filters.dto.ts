import { ApiProperty, OmitType } from '@nestjs/swagger';
import { IsOptional, IsInt, Min, Max } from 'class-validator';
import { Type } from 'class-transformer';
import { ListEntitiesDto } from '../../entities/dto/list-entities.dto';

/**
 * Enhanced Recommendation Filters DTO
 *
 * Inherits ALL 80+ filter parameters from ListEntitiesDto including:
 * - Core filters (entity types, categories, tags, features)
 * - Tool filters (technical_levels, learning_curves, has_api, frameworks, etc.)
 * - Course filters (skill_levels, certificate_available, instructor_name, etc.)
 * - Job filters (employment_types, experience_levels, salary ranges, etc.)
 * - Event filters (event_types, is_online, location, date ranges, etc.)
 * - Hardware filters (hardware_types, manufacturers, price ranges, etc.)
 * - Agency filters (services_offered, industry_focus, has_portfolio)
 * - Software filters (license_types, programming_languages, platform_compatibility, etc.)
 * - Research Paper filters (research_areas, authors_search, publication_date_from/to)
 * - Book filters (author_name, isbn, formats)
 * - And many more entity-specific filters
 *
 * This enables the recommendation system to use the same comprehensive
 * filtering capabilities as the main entity search endpoint.
 */
export class RecommendationFiltersDto extends OmitType(ListEntitiesDto, [
  'page',
  'limit',
  'sortBy',
  'sortOrder'
] as const) {
  @ApiProperty({
    description: 'Maximum number of candidate entities to consider for LLM analysis',
    example: 20,
    minimum: 5,
    maximum: 50,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(5)
  @Max(50)
  max_candidates?: number = 20;
}
