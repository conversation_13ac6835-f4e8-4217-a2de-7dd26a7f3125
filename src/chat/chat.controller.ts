import {
  Controller,
  Post,
  Get,
  Delete,
  Body,
  Param,
  Query,
  HttpCode,
  HttpStatus,
  UseGuards,
  BadRequestException,
  NotFoundException,
  ForbiddenException,
  InternalServerErrorException,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiBody,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { Throttle } from '@nestjs/throttler';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { GetReqUser } from '../auth/decorators/get-req-user.decorator';
import { ReqUserObject } from '../auth/strategies/jwt.strategy';
import { ChatService } from './services/chat.service';
import { SendChatMessageDto } from './dto/send-chat-message.dto';
import { ChatResponseDto } from './dto/chat-response.dto';
import { ConversationHistoryResponseDto } from './dto/conversation-history-response.dto';
import { GetConversationHistoryDto } from './dto/get-conversation-history.dto';

@ApiTags('Chat')
@Controller('chat')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class ChatController {
  constructor(private readonly chatService: ChatService) {}

  @Post()
  @Throttle({ default: { limit: 20, ttl: 60000 } }) // 20 requests per minute
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Send a message to the AI chatbot',
    description: `
    Send a message to the conversational AI chatbot that helps users discover the perfect AI tools.
    
    This endpoint:
    1. Processes the user's message in the context of their conversation
    2. Uses vector search to find relevant AI tools
    3. Applies the configured LLM provider to generate intelligent responses
    4. Maintains conversation state and context across messages
    5. Provides personalized recommendations based on user preferences
    
    The chatbot can:
    - Help users explore different AI tool categories
    - Ask clarifying questions to understand specific needs
    - Provide detailed explanations about AI tools and their use cases
    - Guide users through the discovery process
    - Transition to formal recommendations when appropriate
    
    Rate limiting: 20 requests per minute per user to ensure optimal performance.
    `,
  })
  @ApiBody({
    type: SendChatMessageDto,
    description: 'Chat message and optional conversation context',
    examples: {
      'New Conversation': {
        summary: 'Starting a new conversation',
        value: {
          message: 'Hi! I need help finding an AI tool for automated code documentation.',
          user_preferences: {
            budget: 'medium',
            technical_level: 'intermediate',
          },
        },
      },
      'Continue Conversation': {
        summary: 'Continuing an existing conversation',
        value: {
          message: 'I work primarily with Python and JavaScript. Do you have any specific recommendations?',
          session_id: 'chat_123e4567-e89b-12d3-a456-************',
          user_preferences: {
            preferred_categories: ['Developer Tools'],
          },
        },
      },
      'Refinement': {
        summary: 'Refining requirements',
        value: {
          message: 'I prefer free tools, and I need something that integrates with VS Code.',
          session_id: 'chat_123e4567-e89b-12d3-a456-************',
          user_preferences: {
            budget: 'free',
            technical_level: 'intermediate',
          },
        },
      },
    },
  })
  @ApiResponse({
    status: 200,
    description: 'Chat response generated successfully',
    type: ChatResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid request data or malformed message',
  })
  @ApiResponse({
    status: 401,
    description: 'Authentication required - valid JWT token must be provided',
  })
  @ApiResponse({
    status: 429,
    description: 'Rate limit exceeded - too many chat requests',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error during chat processing',
  })
  async sendMessage(
    @Body() sendChatMessageDto: SendChatMessageDto,
    @GetReqUser() reqUser: ReqUserObject,
  ): Promise<ChatResponseDto> {
    try {
      if (!sendChatMessageDto.message?.trim()) {
        throw new BadRequestException('Message cannot be empty');
      }

      // Use the enhanced message processing for better conversation flow
      return await this.chatService.sendEnhancedMessage(reqUser.authData.sub, sendChatMessageDto);
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }

      // Log the error but don't expose internal details
      console.error('Chat service error:', error);
      throw new InternalServerErrorException('Unable to process chat message at this time');
    }
  }

  @Get(':sessionId/history')
  @Throttle({ default: { limit: 30, ttl: 60000 } }) // 30 requests per minute
  @ApiOperation({
    summary: 'Get conversation history',
    description: `
    Retrieve the conversation history for a specific chat session.
    
    This endpoint returns:
    - All messages in the conversation (up to the specified limit)
    - Current conversation stage and metadata
    - Information about discovered AI tools
    - Conversation timestamps and statistics
    
    Use this to:
    - Resume conversations after page refreshes
    - Display conversation history to users
    - Analyze conversation patterns and effectiveness
    - Debug conversation flow issues
    `,
  })
  @ApiParam({
    name: 'sessionId',
    description: 'Unique session identifier for the conversation',
    example: 'chat_123e4567-e89b-12d3-a456-************',
  })
  @ApiQuery({
    name: 'limit',
    description: 'Maximum number of messages to return (1-100)',
    example: 20,
    required: false,
  })
  @ApiResponse({
    status: 200,
    description: 'Conversation history retrieved successfully',
    type: ConversationHistoryResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid session ID or limit parameter',
  })
  @ApiResponse({
    status: 401,
    description: 'Authentication required',
  })
  @ApiResponse({
    status: 403,
    description: 'Session does not belong to the authenticated user',
  })
  @ApiResponse({
    status: 404,
    description: 'Conversation session not found',
  })
  @ApiResponse({
    status: 429,
    description: 'Rate limit exceeded',
  })
  async getConversationHistory(
    @Param('sessionId') sessionId: string,
    @Query() getHistoryDto: GetConversationHistoryDto,
    @GetReqUser() reqUser: ReqUserObject,
  ): Promise<ConversationHistoryResponseDto> {
    try {
      if (!sessionId?.trim()) {
        throw new BadRequestException('Session ID is required');
      }

      return await this.chatService.getConversationHistory(
        reqUser.authData.sub,
        sessionId,
        getHistoryDto,
      );
    } catch (error) {
      if (error.message?.includes('does not belong to the user')) {
        throw new ForbiddenException('Access denied to this conversation');
      }

      if (error.message?.includes('not found')) {
        throw new NotFoundException('Conversation not found');
      }

      if (error instanceof BadRequestException ||
          error instanceof ForbiddenException ||
          error instanceof NotFoundException) {
        throw error;
      }

      console.error('Error getting conversation history:', error);
      throw new InternalServerErrorException('Unable to retrieve conversation history');
    }
  }

  @Delete(':sessionId')
  @Throttle({ default: { limit: 10, ttl: 60000 } }) // 10 requests per minute
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({
    summary: 'End a conversation session',
    description: `
    Permanently end a conversation session and clean up associated data.
    
    This action:
    - Removes the conversation from active memory/storage
    - Clears all conversation state and history
    - Cannot be undone - the conversation will be permanently lost
    
    Use this when:
    - Users explicitly want to end a conversation
    - Cleaning up old or abandoned sessions
    - Implementing privacy features (data deletion)
    - Managing storage/memory usage
    
    Note: This is a destructive operation and cannot be reversed.
    `,
  })
  @ApiParam({
    name: 'sessionId',
    description: 'Unique session identifier for the conversation to end',
    example: 'chat_123e4567-e89b-12d3-a456-************',
  })
  @ApiResponse({
    status: 204,
    description: 'Conversation session ended successfully',
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid session ID format',
  })
  @ApiResponse({
    status: 401,
    description: 'Authentication required',
  })
  @ApiResponse({
    status: 403,
    description: 'Session does not belong to the authenticated user',
  })
  @ApiResponse({
    status: 404,
    description: 'Conversation session not found',
  })
  @ApiResponse({
    status: 429,
    description: 'Rate limit exceeded',
  })
  async endConversation(
    @Param('sessionId') sessionId: string,
    @GetReqUser() reqUser: ReqUserObject,
  ): Promise<void> {
    return this.chatService.endConversation(reqUser.authData.sub, sessionId);
  }

  @Get('sessions')
  @Throttle({ default: { limit: 10, ttl: 60000 } }) // 10 requests per minute
  @ApiOperation({
    summary: 'Get user active chat sessions',
    description: `
    Retrieve all active chat sessions for the authenticated user.
    
    This endpoint returns:
    - List of session IDs for active conversations
    - Useful for displaying ongoing conversations to users
    - Helps with session management and cleanup
    
    Use this to:
    - Show users their active conversations
    - Implement conversation switching in the UI
    - Monitor user engagement with the chatbot
    - Manage session limits per user
    `,
  })
  @ApiResponse({
    status: 200,
    description: 'Active sessions retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        sessions: {
          type: 'array',
          items: { type: 'string' },
          example: [
            'chat_123e4567-e89b-12d3-a456-************',
            'chat_987fcdeb-51a2-43d7-8f9e-123456789abc',
          ],
        },
        total: {
          type: 'number',
          example: 2,
        },
      },
    },
  })
  @ApiResponse({
    status: 401,
    description: 'Authentication required',
  })
  @ApiResponse({
    status: 429,
    description: 'Rate limit exceeded',
  })
  async getUserActiveSessions(
    @GetReqUser() reqUser: ReqUserObject,
  ): Promise<{ sessions: string[]; total: number }> {
    const sessions = await this.chatService.getUserActiveSessions(reqUser.authData.sub);
    return {
      sessions,
      total: sessions.length,
    };
  }

  @Get('admin/metrics')
  @Throttle({ default: { limit: 5, ttl: 60000 } }) // 5 requests per minute
  @ApiOperation({
    summary: 'Get chat performance metrics (Admin only)',
    description: `
    Retrieve comprehensive performance metrics for the chat system.

    This endpoint provides:
    - Request statistics (total, success rate, error rate)
    - Response time metrics (average, percentiles)
    - Session statistics (active, peak concurrent)
    - LLM provider usage distribution
    - Conversation stage distribution
    - Error type breakdown
    - Performance thresholds and health status

    **Admin Access Required**: This endpoint should be restricted to administrators only.
    `,
  })
  @ApiResponse({
    status: 200,
    description: 'Performance metrics retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        requests: {
          type: 'object',
          properties: {
            total: { type: 'number' },
            successful: { type: 'number' },
            failed: { type: 'number' },
            successRate: { type: 'number' },
          },
        },
        performance: {
          type: 'object',
          properties: {
            averageResponseTime: { type: 'number' },
            medianResponseTime: { type: 'number' },
            p95ResponseTime: { type: 'number' },
            p99ResponseTime: { type: 'number' },
          },
        },
        sessions: {
          type: 'object',
          properties: {
            currentActive: { type: 'number' },
            peakConcurrent: { type: 'number' },
          },
        },
        health: {
          type: 'object',
          properties: {
            status: { type: 'string', enum: ['healthy', 'warning', 'critical'] },
            issues: { type: 'array', items: { type: 'string' } },
            recommendations: { type: 'array', items: { type: 'string' } },
          },
        },
      },
    },
  })
  @ApiResponse({
    status: 401,
    description: 'Authentication required',
  })
  @ApiResponse({
    status: 403,
    description: 'Admin access required',
  })
  @ApiResponse({
    status: 429,
    description: 'Rate limit exceeded',
  })
  async getPerformanceMetrics(
    @GetReqUser() reqUser: ReqUserObject,
  ): Promise<any> {
    // TODO: Add admin role check here
    // if (!reqUser.roles?.includes('admin')) {
    //   throw new ForbiddenException('Admin access required');
    // }

    const metrics = this.chatService.getPerformanceMetrics();
    const health = this.chatService.getPerformanceHealth();
    const cacheStats = this.chatService.getCacheStats();

    return {
      ...metrics,
      health,
      cache: cacheStats,
      timestamp: new Date().toISOString(),
    };
  }

  @Post('admin/cache/clear')
  @Throttle({ default: { limit: 2, ttl: 60000 } }) // 2 requests per minute
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({
    summary: 'Clear chat caches (Admin only)',
    description: `
    Clear all chat-related caches to force fresh data retrieval.

    This operation:
    - Clears intent classification cache
    - Clears follow-up questions cache
    - Clears entity search cache
    - Clears response cache

    Use this when:
    - Deploying new AI models or prompts
    - Debugging cache-related issues
    - Forcing fresh responses for testing

    **Admin Access Required**: This is a destructive operation that affects all users.
    `,
  })
  @ApiResponse({
    status: 204,
    description: 'Caches cleared successfully',
  })
  @ApiResponse({
    status: 401,
    description: 'Authentication required',
  })
  @ApiResponse({
    status: 403,
    description: 'Admin access required',
  })
  @ApiResponse({
    status: 429,
    description: 'Rate limit exceeded',
  })
  async clearCaches(
    @GetReqUser() reqUser: ReqUserObject,
  ): Promise<void> {
    // TODO: Add admin role check here
    // if (!reqUser.roles?.includes('admin')) {
    //   throw new ForbiddenException('Admin access required');
    // }

    this.chatService.clearCaches();
  }
}
