import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { MemoryConversationStateService } from './services/memory-conversation-state.service';
import { ConversationManagerService } from './services/conversation-manager.service';
import { ChatService } from './services/chat.service';
import { ChatErrorHandlerService } from './services/chat-error-handler.service';
import { LlmFailoverService } from './services/llm-failover.service';
import { QuestionSuggestionService } from './services/question-suggestion.service';
import { ChatPerformanceMonitorService } from './services/chat-performance-monitor.service';
import { ChatCacheService } from './services/chat-cache.service';
import { EnhancedIntentClassificationService } from './services/enhanced-intent-classification.service';
import { ConversationFlowManagerService } from './services/conversation-flow-manager.service';
import { IntelligentResponseGeneratorService } from './services/intelligent-response-generator.service';
import { AdvancedEntityRankingService } from '../common/ranking/advanced-entity-ranking.service';
import { PerformanceOptimizationService } from '../common/performance/performance-optimization.service';
import { ChatController } from './chat.controller';
import { EntitiesModule } from '../entities/entities.module';
import { LlmModule } from '../common/llm/llm.module';
import { RecommendationsModule } from '../recommendations/recommendations.module';

@Module({
  imports: [
    ConfigModule,
    EntitiesModule,
    LlmModule,
    RecommendationsModule, // For FilterExtractionService
  ],
  controllers: [ChatController],
  providers: [
    {
      provide: 'IConversationStateService',
      useClass: MemoryConversationStateService,
    },
    ConversationManagerService,
    ChatErrorHandlerService,
    LlmFailoverService,
    QuestionSuggestionService, // 🎯 NEW: Question suggestion service
    ChatPerformanceMonitorService,
    ChatCacheService,
    EnhancedIntentClassificationService, // Enhanced intent classification
    ConversationFlowManagerService, // Conversation flow management
    IntelligentResponseGeneratorService, // Intelligent response generation
    AdvancedEntityRankingService, // Advanced multi-factor entity ranking
    PerformanceOptimizationService, // Performance optimization and caching
    ChatService,
  ],
  exports: [
    'IConversationStateService',
    ConversationManagerService,
    ChatService,
  ],
})
export class ChatModule {}
