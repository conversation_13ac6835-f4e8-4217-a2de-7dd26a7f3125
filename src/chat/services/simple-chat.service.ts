import { Injectable, Logger, Inject } from '@nestjs/common';
import { ConversationManagerService } from './conversation-manager.service';
import { ChatErrorHandlerService } from './chat-error-handler.service';
import { LlmFailoverService } from './llm-failover.service';
import { QuestionSuggestionService } from './question-suggestion.service';
import { EntitiesService } from '../../entities/entities.service';
import { ILlmService } from '../../common/llm/interfaces/llm.service.interface';
import { LlmFactoryService } from '../../common/llm/services/llm-factory.service';
import { SendChatMessageDto } from '../dto/send-chat-message.dto';
import { ChatResponseDto } from '../dto/chat-response.dto';
import { ConversationHistoryResponseDto } from '../dto/conversation-history-response.dto';
import { GetConversationHistoryDto } from '../dto/get-conversation-history.dto';

/**
 * Simplified chat service with Question Suggestion integration
 * This is a minimal, working version that adds contextual questions to chat responses
 */
@Injectable()
export class SimpleChatService {
  private readonly logger = new Logger(SimpleChatService.name);

  constructor(
    private readonly conversationManager: ConversationManagerService,
    private readonly chatErrorHandler: ChatErrorHandlerService,
    private readonly llmFailover: LlmFailoverService,
    private readonly questionSuggestion: QuestionSuggestionService,
    private readonly entitiesService: EntitiesService,
    @Inject('ILlmService') private readonly llmService: ILlmService,
    private readonly llmFactoryService: LlmFactoryService,
  ) {}

  /**
   * Process a chat message and generate a response with contextual questions
   */
  async sendMessage(
    userId: string,
    sendChatMessageDto: SendChatMessageDto,
  ): Promise<ChatResponseDto> {
    this.logger.log(
      `Processing chat message for user ${userId}, session: ${sendChatMessageDto.session_id || 'new'}`,
    );

    try {
      // Get or create conversation context
      const context = await this.conversationManager.getOrCreateConversation(
        userId,
        sendChatMessageDto.session_id,
      );

      // Update user preferences if provided
      if (sendChatMessageDto.user_preferences) {
        await this.conversationManager.updateUserPreferences(
          context.sessionId,
          sendChatMessageDto.user_preferences,
        );
      }

      // Add user message to conversation
      const updatedContext = await this.conversationManager.addMessage(
        context.sessionId,
        {
          role: 'user',
          content: sendChatMessageDto.message,
          metadata: sendChatMessageDto.context,
        },
      );

      // Discover relevant entities (simplified)
      const candidateEntities = await this.discoverRelevantEntities(
        sendChatMessageDto.message,
        updatedContext,
      );

      // Generate LLM response
      const llmResponse = await this.generateLlmResponse(
        sendChatMessageDto.message,
        updatedContext,
        candidateEntities,
      );

      // Add assistant message to conversation
      const finalContext = await this.conversationManager.addMessage(
        updatedContext.sessionId,
        {
          role: 'assistant',
          content: llmResponse.message,
          metadata: {
            entities_shown: candidateEntities?.map(e => e.id) || [],
            response_type: llmResponse.response_type || 'general',
          },
        },
      );

      // 🎯 NEW: Generate contextual follow-up questions
      const followUpQuestions = this.questionSuggestion.generateFollowUpQuestions(
        finalContext,
        sendChatMessageDto.message,
        2, // Max 2 questions
      );

      // Build response
      const response: ChatResponseDto = {
        message: llmResponse.message,
        session_id: finalContext.sessionId,
        conversation_stage: finalContext.conversationStage,
        // 🎯 NEW: Include follow-up questions in response
        follow_up_questions: followUpQuestions,
        discovered_entities: candidateEntities?.map(entity => ({
          id: entity.id,
          name: entity.name,
          description: entity.description,
          relevance_score: 0.8,
          why_relevant: 'Matches your search criteria',
        })) || [],
        should_transition_to_recommendations: false,
        metadata: {
          response_time: Date.now() - Date.now(),
          llm_provider: this.llmService.getProviderName(),
        },
        generated_at: new Date(),
      };

      this.logger.log(
        `Chat response generated for session ${finalContext.sessionId} with ${followUpQuestions.length} follow-up questions`,
      );

      return response;
    } catch (error) {
      this.logger.error('Error processing chat message', error.stack);
      return this.chatErrorHandler.handleCriticalError(
        error,
        sendChatMessageDto.session_id || `chat_${Date.now()}`,
        userId,
      );
    }
  }

  /**
   * Simplified entity discovery
   */
  private async discoverRelevantEntities(
    message: string,
    context: any,
  ): Promise<any[]> {
    try {
      // Simple keyword-based entity discovery using findAll
      const searchResults = await this.entitiesService.findAll({
        page: 1,
        limit: 5,
        search: message,
        include_categories: true,
        include_features: true,
      });

      return searchResults.data || [];
    } catch (error) {
      this.logger.warn('Entity discovery failed, continuing without entities', error.message);
      return [];
    }
  }

  /**
   * Generate LLM response with fallback handling
   */
  private async generateLlmResponse(
    message: string,
    context: any,
    entities: any[],
  ): Promise<any> {
    try {
      const prompt = this.buildSimplePrompt(message, context, entities);
      
      const response = await this.llmFailover.generateChatResponse(
        prompt,
        context,
        {
          max_tokens: 500,
          temperature: 0.7,
        },
      );

      return {
        message: response.message || 'I can help you find AI tools. What are you looking for?',
        response_type: 'discovery',
      };
    } catch (error) {
      this.logger.error('LLM response generation failed', error.stack);
      return {
        message: 'I can help you find AI tools. What specific task are you trying to accomplish?',
        response_type: 'fallback',
      };
    }
  }

  /**
   * Build a simple prompt for the LLM
   */
  private buildSimplePrompt(message: string, context: any, entities: any[]): string {
    const userPrefs = context.userPreferences || {};
    const messageHistory = context.messages?.slice(-4) || [];
    
    let prompt = `You are an AI tool discovery assistant. Help users find the right AI tools for their needs.

User Message: ${message}

`;

    // Add context if available
    if (userPrefs.industry) {
      prompt += `User Industry: ${userPrefs.industry}\n`;
    }
    if (userPrefs.technical_level) {
      prompt += `Technical Level: ${userPrefs.technical_level}\n`;
    }

    // Add recent conversation context
    if (messageHistory.length > 1) {
      prompt += `\nRecent Conversation:\n`;
      messageHistory.slice(-3).forEach(msg => {
        prompt += `${msg.role}: ${msg.content}\n`;
      });
    }

    // Add relevant entities if found
    if (entities.length > 0) {
      prompt += `\nRelevant AI Tools Found:\n`;
      entities.slice(0, 3).forEach((entity, index) => {
        prompt += `${index + 1}. ${entity.name}: ${entity.description}\n`;
      });
    }

    prompt += `\nProvide a helpful, conversational response. Be concise but informative. If you mention specific tools, explain why they might be relevant.`;

    return prompt;
  }

  /**
   * Get conversation history
   */
  async getConversationHistory(
    userId: string,
    dto: GetConversationHistoryDto,
  ): Promise<ConversationHistoryResponseDto> {
    try {
      return await this.conversationManager.getConversationHistory(userId, dto);
    } catch (error) {
      this.logger.error('Failed to get conversation history', error.stack);
      throw error;
    }
  }

  /**
   * End a conversation
   */
  async endConversation(userId: string, sessionId: string): Promise<void> {
    try {
      await this.conversationManager.endConversation(userId, sessionId);
      this.logger.log(`Conversation ${sessionId} ended for user ${userId}`);
    } catch (error) {
      this.logger.error('Failed to end conversation', error.stack);
      throw error;
    }
  }

  /**
   * Get user's active sessions
   */
  async getUserActiveSessions(userId: string): Promise<string[]> {
    try {
      return await this.conversationManager.getUserActiveSessions(userId);
    } catch (error) {
      this.logger.error('Failed to get user active sessions', error.stack);
      throw error;
    }
  }
}
