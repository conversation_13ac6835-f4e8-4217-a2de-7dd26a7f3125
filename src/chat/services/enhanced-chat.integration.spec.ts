import { Test, TestingModule } from '@nestjs/testing';
import { ChatService } from './chat.service';
import { EnhancedConversationManagerService } from './enhanced-conversation-manager.service';
import { IntelligentQuestionGeneratorService } from './intelligent-question-generator.service';
import { AdvancedPromptGeneratorService } from './advanced-prompt-generator.service';
import { SendChatMessageDto } from '../dto/send-chat-message.dto';

/**
 * Integration tests for the enhanced chat system
 * Tests the complete flow to ensure no repetitive questions and better conversation flow
 */
describe('Enhanced Chat System Integration', () => {
  let chatService: ChatService;
  let enhancedConversationManager: EnhancedConversationManagerService;
  let questionGenerator: IntelligentQuestionGeneratorService;
  let promptGenerator: AdvancedPromptGeneratorService;

  const mockUserId = 'test-user-123';
  let sessionId: string;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        // Mock all dependencies for focused testing
        {
          provide: ChatService,
          useValue: {
            sendEnhancedMessage: jest.fn(),
          },
        },
        {
          provide: EnhancedConversationManagerService,
          useValue: {
            initializeEnhancedConversation: jest.fn(),
            getOrCreateEnhancedConversation: jest.fn(),
            addMessageWithEnhancedTracking: jest.fn(),
            trackQuestionAsked: jest.fn(),
            shouldAvoidQuestionCategory: jest.fn(),
            getConversationInsights: jest.fn(),
          },
        },
        {
          provide: IntelligentQuestionGeneratorService,
          useValue: {
            generateContextualQuestions: jest.fn(),
            generateTargetedQuestions: jest.fn(),
          },
        },
        {
          provide: AdvancedPromptGeneratorService,
          useValue: {
            generateAdvancedChatPrompt: jest.fn(),
            generateAdvancedIntentPrompt: jest.fn(),
          },
        },
      ],
    }).compile();

    chatService = module.get<ChatService>(ChatService);
    enhancedConversationManager = module.get<EnhancedConversationManagerService>(EnhancedConversationManagerService);
    questionGenerator = module.get<IntelligentQuestionGeneratorService>(IntelligentQuestionGeneratorService);
    promptGenerator = module.get<AdvancedPromptGeneratorService>(AdvancedPromptGeneratorService);
  });

  describe('Conversation Flow Improvements', () => {
    it('should avoid asking the same question twice', async () => {
      // Mock conversation context with question history
      const mockContext = {
        sessionId: 'test-session',
        userId: mockUserId,
        messages: [
          { role: 'user', content: 'I work in education', timestamp: new Date() },
          { role: 'assistant', content: 'What industry do you work in?', timestamp: new Date() },
        ],
        questionHistory: {
          askedQuestions: [
            {
              question: 'What industry do you work in?',
              timestamp: new Date(),
              category: 'industry',
              answered: true,
              answer: 'education',
            },
          ],
          questionCategories: {
            industry: {
              count: 1,
              lastAsked: new Date(),
              effectiveness: 0.8,
              shouldAvoid: false,
            },
          },
          avoidedTopics: [],
          preferredTopics: ['education'],
        },
        conversationMemory: {
          userProfile: {
            industry: 'education',
            workContext: 'education',
          },
        },
        discoveryProgress: {
          phase: 'exploration',
          confidence: 0.4,
        },
      };

      jest.spyOn(enhancedConversationManager, 'shouldAvoidQuestionCategory')
        .mockReturnValue(true); // Should avoid asking about industry again

      jest.spyOn(questionGenerator, 'generateContextualQuestions')
        .mockReturnValue([
          'Are you looking for tools to help with lesson planning or student engagement?',
          'What\'s your experience level with AI tools?',
        ]);

      const questions = questionGenerator.generateContextualQuestions(mockContext as any, 2);

      expect(questions).not.toContain('What industry do you work in?');
      expect(questions).toHaveLength(2);
      expect(questions[0]).toContain('lesson planning'); // Education-specific question
    });

    it('should build on previous conversation context', async () => {
      const mockContext = {
        conversationMemory: {
          userProfile: {
            industry: 'education',
            workContext: 'education',
            experienceLevel: 'beginner',
          },
          requirements: {
            mustHave: ['easy to use'],
            niceToHave: [],
            dealBreakers: ['too complex'],
          },
        },
        discoveryProgress: {
          phase: 'refinement',
          confidence: 0.6,
          informationGathered: {
            useCase: true,
            industry: true,
            technicalLevel: true,
            budget: false,
            teamSize: false,
          },
        },
      };

      jest.spyOn(questionGenerator, 'generateContextualQuestions')
        .mockImplementation((context) => {
          // Should generate questions based on missing information
          const questions = [];
          if (!context.discoveryProgress?.informationGathered?.budget) {
            questions.push('Do you have a budget in mind, or are you open to both free and paid options?');
          }
          if (!context.discoveryProgress?.informationGathered?.teamSize) {
            questions.push('Will this be for personal use, or do you need something that works for a team?');
          }
          return questions;
        });

      const questions = questionGenerator.generateContextualQuestions(mockContext as any, 2);

      expect(questions).toContain('Do you have a budget in mind, or are you open to both free and paid options?');
      expect(questions).toContain('Will this be for personal use, or do you need something that works for a team?');
      expect(questions).not.toContain('What industry do you work in?'); // Already known
    });

    it('should generate advanced prompts with full context', () => {
      const mockContext = {
        sessionId: 'test-session',
        messages: [
          { role: 'user', content: 'I work in education and need help with grading', timestamp: new Date() },
        ],
        conversationMemory: {
          userProfile: {
            industry: 'education',
            workContext: 'education',
            primaryUseCases: ['grading', 'assessment'],
          },
        },
        discoveryProgress: {
          phase: 'exploration',
          confidence: 0.5,
        },
        questionHistory: {
          askedQuestions: [],
          questionCategories: {},
        },
      };

      const mockIntent = {
        type: 'discovery',
        confidence: 0.8,
        entities: [],
        categories: ['Education'],
        features: ['grading', 'assessment'],
      };

      const mockEntities = [
        {
          id: '1',
          name: 'Gradescope',
          description: 'AI-powered grading platform',
          categories: [{ category: { name: 'Education' } }],
        },
      ];

      jest.spyOn(promptGenerator, 'generateAdvancedChatPrompt')
        .mockImplementation((userMessage, context, intent, entities) => {
          // Should include context about education and grading
          expect(userMessage).toContain('grading');
          expect(context.conversationMemory?.userProfile?.industry).toBe('education');
          expect(entities).toHaveLength(1);
          expect(entities?.[0]?.name).toBe('Gradescope');

          return 'Advanced prompt with full context about education and grading needs';
        });

      const prompt = promptGenerator.generateAdvancedChatPrompt(
        'I work in education and need help with grading',
        mockContext as any,
        mockIntent as any,
        mockEntities as any,
      );

      expect(prompt).toContain('Advanced prompt with full context');
    });
  });

  describe('Progressive Discovery', () => {
    it('should track conversation progress and readiness for recommendations', () => {
      const mockContext = {
        conversationMemory: {
          userProfile: {
            industry: 'education',
            experienceLevel: 'intermediate',
            budget: 'medium',
            teamSize: 'small_team',
          },
          requirements: {
            mustHave: ['easy integration', 'good support'],
            niceToHave: ['mobile app'],
            dealBreakers: ['too expensive'],
          },
        },
        discoveryProgress: {
          phase: 'evaluation',
          confidence: 0.8,
          readinessScore: 0.85,
          informationGathered: {
            useCase: true,
            industry: true,
            technicalLevel: true,
            budget: true,
            teamSize: true,
            timeline: false,
            specificRequirements: true,
            integrationNeeds: true,
          },
        },
      };

      jest.spyOn(enhancedConversationManager, 'getConversationInsights')
        .mockReturnValue({
          readinessForRecommendations: 0.85,
          missingInformation: ['timeline'],
          conversationEfficiency: 0.7,
          suggestedNextSteps: ['provide_recommendations'],
        });

      const insights = enhancedConversationManager.getConversationInsights(mockContext as any);

      expect(insights.readinessForRecommendations).toBeGreaterThan(0.8);
      expect(insights.missingInformation).toEqual(['timeline']);
      expect(insights.suggestedNextSteps).toContain('provide_recommendations');
    });
  });

  describe('Real Conversation Simulation', () => {
    it('should handle a complete conversation flow without repetition', async () => {
      // Simulate a real conversation about AI video generation tools
      const conversationFlow = [
        {
          userMessage: 'I need the best AI video generation tool',
          expectedQuestions: ['What type of videos are you looking to create?'],
          expectedContext: { primaryGoal: 'video generation' },
        },
        {
          userMessage: 'I want to create marketing videos for my business',
          expectedQuestions: ['What\'s your experience level with video editing tools?'],
          expectedContext: { industry: 'marketing', useCase: 'business marketing' },
        },
        {
          userMessage: 'I\'m a beginner, need something easy to use',
          expectedQuestions: ['Do you have a budget in mind for this tool?'],
          expectedContext: { experienceLevel: 'beginner', requirements: ['easy to use'] },
        },
        {
          userMessage: 'I can spend up to $50 per month',
          expectedQuestions: [], // Should be ready for recommendations
          expectedContext: { budget: 'medium', readyForRecommendations: true },
        },
      ];

      // Mock the conversation flow
      let mockContext: any = {
        sessionId: 'test-session',
        messages: [] as any[],
        conversationMemory: {
          userProfile: {},
          requirements: { mustHave: [], niceToHave: [], dealBreakers: [] },
        },
        discoveryProgress: {
          phase: 'initial',
          confidence: 0.1,
          informationGathered: {},
        },
        questionHistory: {
          askedQuestions: [],
          questionCategories: {},
        },
      };

      for (const step of conversationFlow) {
        // Update context based on user message
        mockContext.messages.push({
          role: 'user',
          content: step.userMessage,
          timestamp: new Date(),
        });

        // Mock question generation based on current context
        jest.spyOn(questionGenerator, 'generateContextualQuestions')
          .mockReturnValue(step.expectedQuestions);

        const questions = questionGenerator.generateContextualQuestions(mockContext as any, 1);

        if (step.expectedQuestions.length > 0) {
          expect(questions).toEqual(step.expectedQuestions);
        } else {
          // Should be ready for recommendations
          expect(questions).toHaveLength(0);
        }

        // Update mock context for next iteration
        Object.assign(mockContext.conversationMemory.userProfile, step.expectedContext);
        mockContext.discoveryProgress.confidence += 0.2;
      }
    });
  });
});
