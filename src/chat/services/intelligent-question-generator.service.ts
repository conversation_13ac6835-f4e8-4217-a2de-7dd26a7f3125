import { Injectable, Logger } from '@nestjs/common';
import { 
  ConversationContext, 
  ConversationMemory,
  DiscoveryProgress,
  QuestionHistory
} from '../../common/llm/interfaces/llm.service.interface';

/**
 * Intelligent Question Generator Service
 * 
 * Generates contextual, non-repetitive questions that progressively
 * discover user needs and guide them toward the perfect AI tool.
 */
@Injectable()
export class IntelligentQuestionGeneratorService {
  private readonly logger = new Logger(IntelligentQuestionGeneratorService.name);

  /**
   * Generate intelligent follow-up questions based on conversation context
   */
  generateContextualQuestions(
    context: ConversationContext,
    maxQuestions: number = 2,
  ): string[] {
    const memory = context.conversationMemory;
    const progress = context.discoveryProgress;
    const questionHistory = context.questionHistory;

    if (!memory || !progress || !questionHistory) {
      return this.getFallbackQuestions(context.conversationStage);
    }

    const questions: string[] = [];
    const insights = this.analyzeConversationGaps(context);

    // Generate questions based on current phase and missing information
    switch (progress.phase) {
      case 'initial':
        questions.push(...this.generateInitialDiscoveryQuestions(context, insights));
        break;
      case 'exploration':
        questions.push(...this.generateExplorationQuestions(context, insights));
        break;
      case 'refinement':
        questions.push(...this.generateRefinementQuestions(context, insights));
        break;
      case 'evaluation':
        questions.push(...this.generateEvaluationQuestions(context, insights));
        break;
      case 'decision':
        questions.push(...this.generateDecisionQuestions(context, insights));
        break;
    }

    // Filter out questions we've asked recently or that should be avoided
    const filteredQuestions = this.filterQuestions(questions, questionHistory);

    // Return top questions up to maxQuestions
    return filteredQuestions.slice(0, maxQuestions);
  }

  /**
   * Generate questions for specific information gaps
   */
  generateTargetedQuestions(
    context: ConversationContext,
    targetCategory: string,
  ): string[] {
    const memory = context.conversationMemory;
    const userProfile = memory?.userProfile || {};

    switch (targetCategory) {
      case 'use_case':
        return this.generateUseCaseQuestions(userProfile);
      case 'technical_level':
        return this.generateTechnicalLevelQuestions(userProfile);
      case 'budget':
        return this.generateBudgetQuestions(userProfile);
      case 'team_size':
        return this.generateTeamSizeQuestions(userProfile);
      case 'integration':
        return this.generateIntegrationQuestions(userProfile);
      case 'timeline':
        return this.generateTimelineQuestions(userProfile);
      default:
        return [];
    }
  }

  /**
   * Analyze conversation to identify information gaps and opportunities
   */
  private analyzeConversationGaps(context: ConversationContext): ConversationInsights {
    const memory = context.conversationMemory!;
    const progress = context.discoveryProgress!;
    const questionHistory = context.questionHistory!;

    return {
      missingCriticalInfo: this.identifyMissingCriticalInfo(progress),
      userEngagementPatterns: this.analyzeUserEngagement(questionHistory),
      conversationMomentum: this.assessConversationMomentum(context),
      readinessIndicators: this.assessReadinessForRecommendations(memory, progress),
    };
  }

  private generateInitialDiscoveryQuestions(
    context: ConversationContext,
    insights: ConversationInsights,
  ): string[] {
    const questions: string[] = [];
    const userProfile = context.conversationMemory?.userProfile || {};

    // Focus on understanding the primary use case first
    if (!userProfile.workContext && !this.hasAskedRecently(context, 'work_context')) {
      questions.push("What kind of work or projects are you looking to enhance with AI tools?");
    }

    if (!userProfile.primaryUseCases?.length && !this.hasAskedRecently(context, 'primary_use_case')) {
      questions.push("What's the main challenge or task you're hoping AI can help you solve?");
    }

    if (!userProfile.industry && !this.hasAskedRecently(context, 'industry')) {
      questions.push("What industry or field do you work in?");
    }

    return questions;
  }

  private generateExplorationQuestions(
    context: ConversationContext,
    insights: ConversationInsights,
  ): string[] {
    const questions: string[] = [];
    const userProfile = context.conversationMemory?.userProfile || {};
    const requirements = context.conversationMemory?.requirements || {};

    // Explore specific requirements based on what we know
    if (userProfile.workContext === 'education' && !this.hasAskedRecently(context, 'education_specific')) {
      questions.push("Are you looking for tools to help with lesson planning, student engagement, or assessment?");
    }

    if (userProfile.workContext === 'marketing' && !this.hasAskedRecently(context, 'marketing_specific')) {
      questions.push("Are you focusing on content creation, analytics, customer engagement, or campaign management?");
    }

    if (!userProfile.experienceLevel && !this.hasAskedRecently(context, 'experience_level')) {
      questions.push("How comfortable are you with learning new AI tools - would you prefer something simple to start with?");
    }

    if (!requirements.mustHave.length && !this.hasAskedRecently(context, 'must_have_features')) {
      questions.push("Are there any specific features or capabilities that are absolutely essential for your needs?");
    }

    return questions;
  }

  private generateRefinementQuestions(
    context: ConversationContext,
    insights: ConversationInsights,
  ): string[] {
    const questions: string[] = [];
    const userProfile = context.conversationMemory?.userProfile || {};
    const requirements = context.conversationMemory?.requirements || {};

    // Refine understanding of specific needs
    if (!userProfile.budget && !this.hasAskedRecently(context, 'budget')) {
      questions.push("Do you have a budget in mind, or are you open to both free and paid options?");
    }

    if (!userProfile.teamSize && !this.hasAskedRecently(context, 'team_size')) {
      questions.push("Will this be for personal use, or do you need something that works for a team?");
    }

    if (!requirements.integrationNeeds.length && !this.hasAskedRecently(context, 'integrations')) {
      questions.push("Do you need the tool to integrate with any existing software you're already using?");
    }

    if (!requirements.dealBreakers.length && !this.hasAskedRecently(context, 'deal_breakers')) {
      questions.push("Are there any features or limitations that would be deal-breakers for you?");
    }

    return questions;
  }

  private generateEvaluationQuestions(
    context: ConversationContext,
    insights: ConversationInsights,
  ): string[] {
    const questions: string[] = [];
    const memory = context.conversationMemory!;

    // Help them evaluate options
    if (!memory.insights.evaluationCriteria.length && !this.hasAskedRecently(context, 'evaluation_criteria')) {
      questions.push("When comparing different AI tools, what factors matter most to you - ease of use, features, price, or something else?");
    }

    if (!memory.insights.timeline && !this.hasAskedRecently(context, 'timeline')) {
      questions.push("How soon are you looking to start using a new AI tool?");
    }

    if (!memory.insights.decisionMakers.length && !this.hasAskedRecently(context, 'decision_makers')) {
      questions.push("Are you the only one who needs to approve this decision, or do others need to be involved?");
    }

    return questions;
  }

  private generateDecisionQuestions(
    context: ConversationContext,
    insights: ConversationInsights,
  ): string[] {
    const questions: string[] = [];

    // Help finalize the decision
    if (!this.hasAskedRecently(context, 'final_preferences')) {
      questions.push("Based on everything we've discussed, would you like me to show you the top 3 tools that best match your needs?");
    }

    if (!this.hasAskedRecently(context, 'comparison_focus')) {
      questions.push("Would you like me to focus on comparing features, pricing, or ease of implementation?");
    }

    return questions;
  }

  // Specific question generators for different categories
  private generateUseCaseQuestions(userProfile: ConversationMemory['userProfile']): string[] {
    const questions = [
      "What specific task or challenge are you hoping AI can help you solve?",
      "Are you looking to automate something you're currently doing manually?",
      "What would success look like for you with the right AI tool?",
    ];

    // Customize based on known context
    if (userProfile.industry === 'education') {
      questions.push("Are you looking to enhance teaching, administrative tasks, or student learning?");
    }

    return questions;
  }

  private generateTechnicalLevelQuestions(userProfile: ConversationMemory['userProfile']): string[] {
    return [
      "How comfortable are you with learning new software tools?",
      "Do you prefer tools that work right out of the box, or are you okay with some setup?",
      "Have you used AI tools before, or would this be your first experience?",
    ];
  }

  private generateBudgetQuestions(userProfile: ConversationMemory['userProfile']): string[] {
    const questions = [
      "Are you looking for free options, or do you have a budget for AI tools?",
      "Would you prefer a one-time purchase or are you open to monthly subscriptions?",
    ];

    if (userProfile.teamSize === 'enterprise') {
      questions.push("Are you looking at enterprise pricing, or individual licenses?");
    }

    return questions;
  }

  private generateTeamSizeQuestions(userProfile: ConversationMemory['userProfile']): string[] {
    return [
      "Will this tool be used by just you, or do you need something for your team?",
      "How many people would need access to this tool?",
      "Do you need collaboration features, or is individual use sufficient?",
    ];
  }

  private generateIntegrationQuestions(userProfile: ConversationMemory['userProfile']): string[] {
    return [
      "What other software tools do you currently use that you'd want this to work with?",
      "Do you need API access or direct integrations with specific platforms?",
      "Are you using any particular workflow tools or platforms already?",
    ];
  }

  private generateTimelineQuestions(userProfile: ConversationMemory['userProfile']): string[] {
    return [
      "How soon are you looking to start using a new AI tool?",
      "Is this urgent, or are you planning ahead?",
      "Do you have a specific deadline or project timeline driving this decision?",
    ];
  }

  // Helper methods
  private hasAskedRecently(context: ConversationContext, category: string, hoursAgo: number = 24): boolean {
    const questionHistory = context.questionHistory;
    if (!questionHistory) return false;

    const categoryData = questionHistory.questionCategories[category];
    if (!categoryData) return false;

    const timeSinceLastAsked = Date.now() - categoryData.lastAsked.getTime();
    const threshold = hoursAgo * 60 * 60 * 1000;

    return timeSinceLastAsked < threshold;
  }

  private filterQuestions(questions: string[], questionHistory: QuestionHistory): string[] {
    const recentQuestions = questionHistory.askedQuestions
      .filter(q => Date.now() - q.timestamp.getTime() < 24 * 60 * 60 * 1000) // Last 24 hours
      .map(q => q.question.toLowerCase());

    return questions.filter(question => {
      const questionLower = question.toLowerCase();
      return !recentQuestions.some(recent => 
        this.calculateSimilarity(questionLower, recent) > 0.7
      );
    });
  }

  private calculateSimilarity(str1: string, str2: string): number {
    // Simple similarity calculation based on common words
    const words1 = str1.split(' ');
    const words2 = str2.split(' ');
    const commonWords = words1.filter(word => words2.includes(word));
    return commonWords.length / Math.max(words1.length, words2.length);
  }

  private identifyMissingCriticalInfo(progress: DiscoveryProgress): string[] {
    return Object.entries(progress.informationGathered)
      .filter(([_, gathered]) => !gathered)
      .map(([key, _]) => key);
  }

  private analyzeUserEngagement(questionHistory: QuestionHistory): UserEngagementPattern {
    const recentQuestions = questionHistory.askedQuestions.slice(-5);
    const avgEffectiveness = recentQuestions.reduce((sum, q) => sum + q.effectiveness, 0) / recentQuestions.length || 0.5;
    
    return {
      averageEffectiveness: avgEffectiveness,
      preferredCategories: questionHistory.preferredTopics,
      avoidedCategories: questionHistory.avoidedTopics,
    };
  }

  private assessConversationMomentum(context: ConversationContext): number {
    const messageCount = context.messages.length;
    const recentMessages = context.messages.slice(-3);
    const avgMessageLength = recentMessages.reduce((sum, msg) => sum + msg.content.length, 0) / recentMessages.length || 0;
    
    // Higher momentum if user is providing detailed responses
    return Math.min(avgMessageLength / 100, 1.0);
  }

  private assessReadinessForRecommendations(
    memory: ConversationMemory,
    progress: DiscoveryProgress,
  ): ReadinessIndicators {
    return {
      hasUseCase: !!memory.userProfile.primaryUseCases?.length,
      hasRequirements: memory.requirements.mustHave.length > 0,
      hasConstraints: !!memory.userProfile.budget || !!memory.userProfile.technicalSkills?.length,
      confidenceScore: progress.confidence,
    };
  }

  private getFallbackQuestions(stage: string): string[] {
    const fallbackQuestions = {
      greeting: [
        "What brings you here today? What kind of AI tool are you looking for?",
        "What's the main challenge you're hoping AI can help you solve?",
      ],
      discovery: [
        "Can you tell me more about what you're trying to accomplish?",
        "What industry or type of work is this for?",
      ],
      refinement: [
        "What features are most important to you?",
        "Do you have any budget constraints I should know about?",
      ],
      recommendation: [
        "Would you like me to show you some specific recommendations?",
        "Should I focus on the most popular options or newer alternatives?",
      ],
    };

    return fallbackQuestions[stage] || fallbackQuestions.discovery;
  }
}

// Supporting interfaces
interface ConversationInsights {
  missingCriticalInfo: string[];
  userEngagementPatterns: UserEngagementPattern;
  conversationMomentum: number;
  readinessIndicators: ReadinessIndicators;
}

interface UserEngagementPattern {
  averageEffectiveness: number;
  preferredCategories: string[];
  avoidedCategories: string[];
}

interface ReadinessIndicators {
  hasUseCase: boolean;
  hasRequirements: boolean;
  hasConstraints: boolean;
  confidenceScore: number;
}
