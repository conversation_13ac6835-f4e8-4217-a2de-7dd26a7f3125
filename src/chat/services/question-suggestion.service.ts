import { Injectable, Logger } from '@nestjs/common';
import { ConversationContext } from '../../common/llm/interfaces/llm.service.interface';

/**
 * Simple service to suggest contextual follow-up questions
 * This is a minimal, working improvement that doesn't break existing functionality
 */
@Injectable()
export class QuestionSuggestionService {
  private readonly logger = new Logger(QuestionSuggestionService.name);

  /**
   * Generate contextual follow-up questions based on conversation state
   */
  generateFollowUpQuestions(
    context: ConversationContext,
    userMessage: string,
    maxQuestions: number = 2,
  ): string[] {
    try {
      const questions: string[] = [];
      const messageCount = context.messages?.length || 0;
      const userPrefs = context.userPreferences || {};

      // 🎯 ENHANCED: Check what's already been discussed to avoid repetition
      const previousMessages = this.extractPreviousTopics(context.messages || []);
      const hasDiscussedCoding = this.hasDiscussedTopic(previousMessages, ['code', 'coding', 'programming', 'development']);
      const hasDiscussedIndustry = this.hasDiscussedTopic(previousMessages, ['work', 'industry', 'business', 'company']);
      const hasDiscussedSpecifics = this.hasDiscussedTopic(previousMessages, ['specific', 'particular', 'exactly', 'precisely']);

      // 🎯 ENHANCED: Generate questions based on what user actually said, not generic prompts
      if (userMessage.toLowerCase().includes('code') || userMessage.toLowerCase().includes('coding')) {
        // User mentioned coding - ask specific coding questions
        if (!hasDiscussedSpecifics) {
          questions.push("What programming language are you primarily working with?");
          questions.push("Are you looking for tools to help with debugging, testing, or code generation?");
        }
      } else if (userMessage.toLowerCase().includes('help') && !hasDiscussedSpecifics) {
        // User asked for help but was vague - get specific
        questions.push("What specific challenge are you trying to solve?");
        questions.push("What type of work or project is this for?");
      } else if (messageCount > 3 && !hasDiscussedSpecifics) {
        // Later in conversation but still no specifics
        questions.push("What would be the ideal outcome for you?");
        questions.push("Are there any specific features that are must-haves?");
      }

      // 🎯 ENHANCED: Only ask industry questions if not already discussed
      if (!hasDiscussedIndustry && messageCount <= 4) {
        questions.push("What industry or field are you working in?");
      }

      // 🎯 ENHANCED: Avoid asking the same type of questions repeatedly
      if (questions.length === 0) {
        // Fallback to context-aware questions
        if (messageCount <= 2) {
          questions.push(...this.getInitialQuestions(userMessage, userPrefs));
        } else if (messageCount <= 6) {
          questions.push(...this.getExplorationQuestions(userMessage, userPrefs));
        } else {
          questions.push(...this.getRefinementQuestions(userMessage, userPrefs));
        }
      }

      // Filter and return up to maxQuestions
      return this.filterAndRankQuestions(questions, context, maxQuestions);
    } catch (error) {
      this.logger.error('Error generating follow-up questions', error);
      return [];
    }
  }

  /**
   * Questions for early conversation
   */
  private getInitialQuestions(userMessage: string, userPrefs: any): string[] {
    const questions: string[] = [];
    const message = userMessage.toLowerCase();

    // Ask about work context if not known
    if (!userPrefs.industry && !userPrefs.work_context) {
      if (message.includes('work') || message.includes('business') || message.includes('company')) {
        questions.push("What industry do you work in?");
      } else {
        questions.push("What kind of work or projects are you looking to enhance with AI tools?");
      }
    }

    // Ask about specific use case
    if (message.includes('help') || message.includes('need') || message.includes('looking')) {
      questions.push("What specific task or challenge are you trying to solve?");
    }

    // Ask about experience level
    if (!userPrefs.technical_level) {
      questions.push("How comfortable are you with learning new AI tools?");
    }

    return questions;
  }

  /**
   * Questions for exploration phase
   */
  private getExplorationQuestions(userMessage: string, userPrefs: any): string[] {
    const questions: string[] = [];
    const message = userMessage.toLowerCase();

    // Industry-specific questions
    if (userPrefs.industry === 'education' || userPrefs.work_context === 'education') {
      questions.push("Are you looking for tools to help with lesson planning, student engagement, or assessment?");
    } else if (userPrefs.industry === 'marketing') {
      questions.push("Are you focusing on content creation, analytics, or customer engagement?");
    } else if (userPrefs.industry === 'healthcare') {
      questions.push("Are you looking for tools for patient care, research, or administrative tasks?");
    }

    // Budget questions
    if (!userPrefs.budget) {
      questions.push("Do you have a budget in mind, or are you open to both free and paid options?");
    }

    // Team size questions
    if (!userPrefs.team_size) {
      questions.push("Will this be for personal use, or do you need something that works for a team?");
    }

    // Feature-specific questions
    if (message.includes('video') || message.includes('image') || message.includes('content')) {
      questions.push("What type of content are you primarily working with?");
    }

    return questions;
  }

  /**
   * Questions for refinement phase
   */
  private getRefinementQuestions(userMessage: string, userPrefs: any): string[] {
    const questions: string[] = [];

    // Integration questions
    questions.push("Do you need the tool to integrate with any existing software you're already using?");

    // Timeline questions
    questions.push("How soon are you looking to implement this solution?");

    // Deal breaker questions
    questions.push("Are there any features or limitations that would be deal-breakers for you?");

    // Specific feature questions
    questions.push("What's the most important feature you need this tool to have?");

    return questions;
  }

  /**
   * Filter and rank questions based on relevance
   */
  private filterAndRankQuestions(
    questions: string[],
    context: ConversationContext,
    maxQuestions: number,
  ): string[] {
    // Remove duplicates
    const uniqueQuestions = [...new Set(questions)];

    // Simple ranking: prefer questions that haven't been asked recently
    const recentMessages = context.messages?.slice(-4) || [];
    const recentContent = recentMessages.map(m => m.content.toLowerCase()).join(' ');

    const rankedQuestions = uniqueQuestions
      .map(question => ({
        question,
        relevance: this.calculateRelevance(question, recentContent),
      }))
      .sort((a, b) => b.relevance - a.relevance)
      .slice(0, maxQuestions)
      .map(item => item.question);

    return rankedQuestions;
  }

  /**
   * Calculate relevance score for a question
   */
  private calculateRelevance(question: string, recentContent: string): number {
    let score = 1.0;

    // Lower score if similar question was asked recently
    const questionWords = question.toLowerCase().split(' ');
    const keyWords = questionWords.filter(word => 
      word.length > 3 && !['what', 'how', 'are', 'you', 'the', 'for', 'with'].includes(word)
    );

    for (const word of keyWords) {
      if (recentContent.includes(word)) {
        score -= 0.3;
      }
    }

    return Math.max(score, 0.1);
  }

  /**
   * Check if a question category has been covered recently
   */
  isQuestionCategoryRecent(context: ConversationContext, category: string): boolean {
    const recentMessages = context.messages?.slice(-6) || [];
    const recentContent = recentMessages.map(m => m.content.toLowerCase()).join(' ');

    const categoryKeywords: Record<string, string[]> = {
      industry: ['industry', 'work', 'business', 'company'],
      budget: ['budget', 'cost', 'price', 'free', 'paid'],
      team: ['team', 'personal', 'individual', 'group'],
      technical: ['comfortable', 'experience', 'technical', 'beginner'],
      timeline: ['soon', 'timeline', 'when', 'deadline'],
      integration: ['integrate', 'software', 'existing', 'api'],
    };

    const keywords = categoryKeywords[category] || [];
    return keywords.some((keyword: string) => recentContent.includes(keyword));
  }

  /**
   * Generate questions for specific topics
   */
  generateTopicQuestions(topic: string, context: ConversationContext): string[] {
    const topicQuestions: Record<string, string[]> = {
      budget: [
        "What's your budget range for AI tools?",
        "Are you looking for free options, or are you open to paid solutions?",
        "Do you need enterprise-level features, or would basic plans work?",
      ],
      industry: [
        "What industry do you work in?",
        "What type of business or organization are you with?",
        "What's your primary work focus?",
      ],
      technical_level: [
        "How comfortable are you with learning new technology?",
        "Do you prefer simple, user-friendly tools or are you okay with more complex solutions?",
        "What's your experience level with AI tools?",
      ],
      team_size: [
        "Is this for personal use or for a team?",
        "How many people would be using this tool?",
        "Do you need collaboration features?",
      ],
      use_case: [
        "What specific task are you trying to accomplish?",
        "What's the main challenge you're facing?",
        "What would success look like for you?",
      ],
    };

    return topicQuestions[topic] || [];
  }

  /**
   * Extract topics from previous messages to avoid repetition
   */
  private extractPreviousTopics(messages: any[]): string[] {
    const topics: string[] = [];
    messages.forEach(message => {
      if (message.content && typeof message.content === 'string') {
        topics.push(message.content.toLowerCase());
      }
    });
    return topics;
  }

  /**
   * Check if a topic has already been discussed
   */
  private hasDiscussedTopic(previousMessages: string[], keywords: string[]): boolean {
    const allText = previousMessages.join(' ').toLowerCase();
    return keywords.some(keyword => allText.includes(keyword));
  }
}
