import { Injectable, Logger, Inject } from '@nestjs/common';
import { ConversationManagerService } from './conversation-manager.service';
import { ChatErrorHandlerService } from './chat-error-handler.service';
import { LlmFailoverService } from './llm-failover.service';
import { ChatPerformanceMonitorService } from './chat-performance-monitor.service';
import { ChatCacheService } from './chat-cache.service';
import { EnhancedIntentClassificationService } from './enhanced-intent-classification.service';
import { ConversationFlowManagerService } from './conversation-flow-manager.service';
import { IntelligentResponseGeneratorService } from './intelligent-response-generator.service';
import { AdvancedEntityRankingService, RankingContext } from '../../common/ranking/advanced-entity-ranking.service';
import { PerformanceOptimizationService } from '../../common/performance/performance-optimization.service';
import { EntitiesService } from '../../entities/entities.service';
import { ILlmService } from '../../common/llm/interfaces/llm.service.interface';
import { LlmFactoryService } from '../../common/llm/services/llm-factory.service';
import { SendChatMessageDto } from '../dto/send-chat-message.dto';
import { ChatResponseDto } from '../dto/chat-response.dto';
import { ConversationHistoryResponseDto } from '../dto/conversation-history-response.dto';
import { GetConversationHistoryDto } from '../dto/get-conversation-history.dto';
import { ListEntitiesDto } from '../../entities/dto/list-entities.dto';

/**
 * Main chat service that orchestrates conversation flow, entity discovery, and LLM interactions
 */
@Injectable()
export class ChatService {
  private readonly logger = new Logger(ChatService.name);

  constructor(
    private readonly conversationManager: ConversationManagerService,
    private readonly chatErrorHandler: ChatErrorHandlerService,
    private readonly llmFailover: LlmFailoverService,
    private readonly performanceMonitor: ChatPerformanceMonitorService,
    private readonly cacheService: ChatCacheService,
    private readonly enhancedIntentService: EnhancedIntentClassificationService,
    private readonly conversationFlowManager: ConversationFlowManagerService,
    private readonly intelligentResponseGenerator: IntelligentResponseGeneratorService,
    private readonly advancedRankingService: AdvancedEntityRankingService,
    private readonly performanceOptimizationService: PerformanceOptimizationService,
    private readonly entitiesService: EntitiesService,
    @Inject('ILlmService') private readonly llmService: ILlmService,
    private readonly llmFactoryService: LlmFactoryService,
  ) {}

  /**
   * Process a chat message and generate a response
   */
  async sendMessage(
    userId: string,
    sendChatMessageDto: SendChatMessageDto,
  ): Promise<ChatResponseDto> {
    const startTime = Date.now();
    const requestId = this.performanceMonitor.recordRequestStart(
      sendChatMessageDto.session_id || 'new',
    );

    this.logger.log(
      `Processing chat message for user ${userId}, session: ${sendChatMessageDto.session_id || 'new'}, request: ${requestId}`,
    );

    try {
      // Get or create conversation context with error handling
      let context;
      try {
        context = await this.conversationManager.getOrCreateConversation(
          userId,
          sendChatMessageDto.session_id,
        );
      } catch (error) {
        this.logger.error('Failed to get/create conversation context', error.stack);
        return this.chatErrorHandler.handleConversationStateError(
          error,
          sendChatMessageDto.session_id || `chat_${Date.now()}`,
          userId,
        );
      }

      // Update user preferences if provided
      if (sendChatMessageDto.user_preferences) {
        try {
          await this.conversationManager.updateUserPreferences(
            context.sessionId,
            sendChatMessageDto.user_preferences,
          );
        } catch (error) {
          this.logger.warn('Failed to update user preferences', error.stack);
          // Continue without preferences update
        }
      }

      // Add user message to conversation
      let updatedContext;
      try {
        updatedContext = await this.conversationManager.addMessage(
          context.sessionId,
          {
            role: 'user',
            content: sendChatMessageDto.message,
            metadata: sendChatMessageDto.context,
          },
        );
      } catch (error) {
        this.logger.error('Failed to add user message to conversation', error.stack);
        updatedContext = context; // Use original context as fallback
      }

      // ENHANCED: Step 1 - Enhanced intent classification with filter extraction
      let enhancedIntent;
      try {
        enhancedIntent = await this.enhancedIntentService.classifyIntentWithFilters(
          sendChatMessageDto.message,
          updatedContext,
        );
      } catch (error) {
        this.logger.warn('Enhanced intent classification failed, using basic flow', error.stack);
        enhancedIntent = null;
      }

      // ENHANCED: Step 2 - Update conversation context with accumulated filters
      let enhancedContext = this.ensureEnhancedContext(updatedContext);
      if (enhancedIntent) {
        try {
          enhancedContext = this.enhancedIntentService.updateConversationFilters(
            enhancedContext,
            enhancedIntent,
            updatedContext.messages?.length || 0,
          );
        } catch (error) {
          this.logger.warn('Failed to update conversation filters', error.stack);
        }
      }

      // ENHANCED: Step 3 - Handle filter corrections and conflicts
      let correctionResult;
      if (enhancedIntent) {
        try {
          correctionResult = this.conversationFlowManager.handleFilterCorrection(
            enhancedContext,
            enhancedIntent,
            sendChatMessageDto.message,
          );

          // Apply corrections if needed
          if (correctionResult.corrections.length > 0) {
            this.applyFilterCorrections(enhancedContext, correctionResult.corrections);
          }
        } catch (error) {
          this.logger.warn('Failed to handle filter corrections', error.stack);
        }
      }

      // ENHANCED: Step 4 - Determine next conversation action
      let conversationAction;
      if (enhancedIntent) {
        try {
          conversationAction = this.conversationFlowManager.determineNextAction(
            enhancedContext,
            enhancedIntent,
          );
        } catch (error) {
          this.logger.warn('Failed to determine conversation action', error.stack);
        }
      }

      // ENHANCED: Step 5 - Generate strategic clarifying questions
      let strategicQuestions: any[] = [];
      if (enhancedIntent) {
        try {
          strategicQuestions = this.conversationFlowManager.generateStrategicQuestions(
            enhancedContext,
            enhancedIntent,
          );
        } catch (error) {
          this.logger.warn('Failed to generate strategic questions', error.stack);
        }
      }

      // ENHANCED: Step 6 - Optimize conversation flow
      let conversationOptimization;
      if (enhancedIntent) {
        try {
          conversationOptimization = this.conversationFlowManager.optimizeConversationFlow(
            enhancedContext,
            enhancedIntent,
          );
        } catch (error) {
          this.logger.warn('Failed to optimize conversation flow', error.stack);
        }
      }

      // ENHANCED: Step 7 - Enhanced entity discovery with comprehensive filtering
      let candidateEntities;
      try {
        candidateEntities = await this.discoverRelevantEntities(
          sendChatMessageDto.message,
          enhancedContext,
        );
      } catch (error) {
        this.logger.warn('Entity discovery failed, continuing without entities', error.stack);
        candidateEntities = [];
      }

      // ENHANCED: Step 8 - Generate intelligent response or fallback to LLM
      let chatResponse;
      if (enhancedIntent && conversationAction && this.intelligentResponseGenerator) {
        try {
          const intelligentResponse = this.intelligentResponseGenerator.generateIntelligentResponse(
            enhancedContext,
            enhancedIntent,
            conversationAction,
            candidateEntities,
            strategicQuestions,
            correctionResult,
            conversationOptimization,
          );

          // Convert intelligent response to chat response format
          chatResponse = this.convertIntelligentResponseToChatResponse(intelligentResponse, enhancedContext);
        } catch (error) {
          this.logger.warn('Intelligent response generation failed, falling back to LLM', error.stack);
          chatResponse = null;
        }
      }

      // Fallback to LLM if intelligent response failed
      if (!chatResponse) {
        try {
          chatResponse = await this.llmFailover.getChatResponseWithFailover(
            sendChatMessageDto.message,
            enhancedContext,
            candidateEntities,
          );
        } catch (error) {
          this.logger.error('All LLM providers failed', error.stack);
          return this.chatErrorHandler.handleLlmError(
            error,
            enhancedContext,
            sendChatMessageDto.message,
          );
        }
      }

      // Add assistant message to conversation with error handling
      let finalContext = updatedContext;
      try {
        finalContext = await this.conversationManager.addMessage(
          context.sessionId,
          {
            role: 'assistant',
            content: chatResponse.message,
            metadata: {
              intent: chatResponse.intent,
              llmProvider: chatResponse.metadata.llmProvider,
              responseTime: chatResponse.metadata.responseTime,
            },
          },
          chatResponse.intent,
        );
      } catch (error) {
        this.logger.warn('Failed to add assistant message to conversation', error.stack);
        // Continue with the response even if we can't save it
      }

      // Update discovered entities if any
      if (chatResponse.discoveredEntities && chatResponse.discoveredEntities.length > 0) {
        try {
          const entityIds = chatResponse.discoveredEntities.map((e: any) => e.id);
          await this.conversationManager.addDiscoveredEntities(context.sessionId, entityIds);
        } catch (error) {
          this.logger.warn('Failed to update discovered entities', error.stack);
          // Continue without updating entities
        }
      }

      // Update conversation stage if changed
      if (chatResponse.conversationStage !== finalContext.conversationStage) {
        try {
          await this.conversationManager.updateConversationStage(
            context.sessionId,
            chatResponse.conversationStage,
          );
        } catch (error) {
          this.logger.warn('Failed to update conversation stage', error.stack);
          // Continue without updating stage
        }
      }

      // Build response DTO
      const response: ChatResponseDto = {
        message: chatResponse.message,
        session_id: context.sessionId,
        conversation_stage: chatResponse.conversationStage,
        suggested_actions: chatResponse.suggestedActions?.map((action: any) => ({
          type: action.type,
          label: action.label,
          data: action.data,
        })),
        discovered_entities: chatResponse.discoveredEntities?.map((entity: any) => ({
          id: entity.id,
          name: entity.name,
          relevance_score: entity.relevanceScore,
          reason: entity.reason,
        })),
        follow_up_questions: chatResponse.followUpQuestions,
        should_transition_to_recommendations: chatResponse.shouldTransitionToRecommendations,
        metadata: {
          response_time: Date.now() - startTime,
          llm_provider: chatResponse.metadata.llmProvider,
          tokens_used: chatResponse.metadata.tokensUsed,
        },
        generated_at: new Date(),
      };

      // Record performance metrics
      this.performanceMonitor.recordRequestComplete(
        requestId,
        response.metadata.response_time,
        true,
        response.metadata.llm_provider,
        response.conversation_stage,
      );

      this.logger.log(
        `Chat response generated for session ${context.sessionId} in ${response.metadata.response_time}ms`,
      );

      return response;
    } catch (error) {
      this.logger.error('Critical error processing chat message', error.stack);

      // Record failed request
      this.performanceMonitor.recordRequestComplete(
        requestId,
        Date.now() - startTime,
        false,
        'FALLBACK',
        'error',
        error.message,
      );

      // Return a graceful fallback response instead of throwing
      return this.chatErrorHandler.createCriticalErrorFallback(
        sendChatMessageDto.session_id || `chat_${Date.now()}`,
        error,
      );
    }
  }

  /**
   * Get conversation history for a session
   */
  async getConversationHistory(
    userId: string,
    sessionId: string,
    getHistoryDto: GetConversationHistoryDto,
  ): Promise<ConversationHistoryResponseDto> {
    this.logger.log(`Getting conversation history for session ${sessionId}`);

    try {
      const context = await this.conversationManager.getOrCreateConversation(userId, sessionId);

      // Verify the session belongs to the user
      if (context.userId !== userId) {
        this.chatErrorHandler.handleAuthError(
          new Error('Session does not belong to the user'),
          sessionId,
        );
      }

      const messages = await this.conversationManager.getConversationHistory(
        sessionId,
        getHistoryDto.limit,
      );

      const response: ConversationHistoryResponseDto = {
        session_id: sessionId,
        conversation_stage: context.conversationStage,
        messages: messages.map(msg => ({
          id: msg.id,
          role: msg.role,
          content: msg.content,
          timestamp: msg.timestamp,
          metadata: msg.metadata,
        })),
        total_messages: context.metadata.totalMessages,
        discovered_entities_count: context.discoveredEntities.length,
        started_at: context.metadata.startedAt,
        last_active_at: context.metadata.lastActiveAt,
      };

      return response;
    } catch (error) {
      this.logger.error('Error getting conversation history', error.stack);
      throw error; // Re-throw for controller to handle
    }
  }

  /**
   * End a conversation session
   */
  async endConversation(userId: string, sessionId: string): Promise<void> {
    this.logger.log(`Ending conversation session ${sessionId} for user ${userId}`);

    // Verify the session belongs to the user
    const context = await this.conversationManager.getOrCreateConversation(userId, sessionId);
    if (context.userId !== userId) {
      throw new Error('Session does not belong to the user');
    }

    await this.conversationManager.endConversation(sessionId);
  }

  /**
   * Get all active sessions for a user
   */
  async getUserActiveSessions(userId: string): Promise<string[]> {
    return this.conversationManager.getUserActiveSessions(userId);
  }

  /**
   * Enhanced entity discovery using comprehensive filtering and intent classification
   */
  private async discoverRelevantEntities(
    userMessage: string,
    context: any,
  ): Promise<any[]> {
    try {
      this.logger.debug('Starting enhanced entity discovery', {
        message: userMessage.substring(0, 100),
        sessionId: context.sessionId,
      });

      // Step 1: Enhanced intent classification with filter extraction
      const enhancedIntent = await this.enhancedIntentService.classifyIntentWithFilters(
        userMessage,
        context,
      );

      // Step 2: Update conversation context with accumulated filters
      const enhancedContext = this.enhancedIntentService.updateConversationFilters(
        context,
        enhancedIntent,
        context.messages?.length || 0,
      );

      // Step 3: Build comprehensive filters for entity search
      const searchFilters = this.buildEntitySearchFilters(enhancedContext, enhancedIntent);

      this.logger.debug('Enhanced filters for entity search:', {
        extractedFilters: Object.keys(enhancedIntent.extractedFilters),
        accumulatedFilters: Object.keys(enhancedContext.accumulatedFilters?.filters || {}),
        finalFilters: Object.keys(searchFilters),
      });

      // Step 4: Use comprehensive entity search instead of just vector search
      let entityResults;
      if (Object.keys(searchFilters).length > 1) { // More than just limit
        // Use comprehensive filtering
        entityResults = await this.entitiesService.findAll(searchFilters);
        entityResults = entityResults.data; // Extract data from paginated response
      } else {
        // Fallback to optimized vector search if no filters extracted
        entityResults = await this.performanceOptimizationService.optimizedVectorSearch(
          userMessage,
          async () => {
            return Promise.race([
              this.entitiesService.vectorSearch({
                query: userMessage,
                limit: 10,
              }),
              new Promise<never>((_, reject) =>
                setTimeout(() => reject(new Error('Vector search timeout')), 15000)
              ),
            ]);
          },
        );
      }

      if (entityResults.length === 0) {
        this.logger.debug('No entities found through enhanced search');
        return [];
      }

      // Step 5: Apply advanced ranking for optimal entity ordering
      const filteredResults = this.applyAdvancedRanking(
        entityResults,
        enhancedContext,
        enhancedIntent,
        searchFilters,
      );

      // Step 6: Update enhanced metadata
      if (enhancedContext.enhancedMetadata) {
        enhancedContext.enhancedMetadata.lastEntityDiscovery = {
          timestamp: new Date(),
          filtersUsed: searchFilters,
          entitiesFound: filteredResults.length,
        };
      }

      this.logger.debug(
        `Enhanced entity discovery completed: ${filteredResults.length} entities found`,
      );

      return filteredResults.slice(0, 8); // Increased limit for better LLM analysis
    } catch (error) {
      this.logger.error('Error in enhanced entity discovery', error.stack);

      // Check if this is a critical error that should be handled specially
      if (error.message?.includes('database') || error.message?.includes('connection')) {
        throw error; // Let the caller handle database errors
      }

      return []; // Return empty array for other errors
    }
  }

  /**
   * Build comprehensive search filters from conversation context and intent
   */
  private buildEntitySearchFilters(enhancedContext: any, enhancedIntent: any): ListEntitiesDto {
    const filters: ListEntitiesDto = {
      limit: 15, // Reasonable limit for chat context
      page: 1,
    };

    // Merge accumulated filters from conversation
    if (enhancedContext.accumulatedFilters?.filters) {
      Object.assign(filters, enhancedContext.accumulatedFilters.filters);
    }

    // Merge current message filters (with higher precedence)
    Object.assign(filters, enhancedIntent.extractedFilters);

    // Add search term from user message if not already present
    if (!filters.searchTerm && enhancedIntent.constraints?.use_case) {
      filters.searchTerm = enhancedIntent.constraints.use_case;
    }

    // Ensure we have reasonable defaults
    if (!filters.entityTypeIds || filters.entityTypeIds.length === 0) {
      // If no entity type specified, include the most common ones
      filters.entityTypeIds = ['ai-tool', 'course', 'job', 'event'];
    }

    return filters;
  }

  /**
   * Apply advanced ranking for optimal entity ordering
   */
  private applyAdvancedRanking(
    entities: any[],
    enhancedContext: any,
    enhancedIntent: any,
    appliedFilters: any,
  ): any[] {
    try {
      // Build ranking context from conversation state
      const rankingContext: RankingContext = {
        appliedFilters,
        filterConfidence: enhancedContext.accumulatedFilters?.confidence || {},
        userPreferences: enhancedContext.userPreferences || {},
        userHistory: {
          viewedEntities: enhancedContext.discoveredEntities || [],
          skillProgression: {
            currentLevel: enhancedContext.userPreferences?.technical_level,
          },
        },
        collaborativeSignals: {
          similarUserLikes: 0, // Could be enhanced with user analytics
        },
        currentResults: [],
        intent: enhancedIntent,
      };

      // Apply advanced ranking
      const rankedEntities = this.advancedRankingService.rankEntities(
        entities,
        rankingContext,
      );

      this.logger.debug('Advanced ranking applied to chat entities', {
        originalCount: entities.length,
        rankedCount: rankedEntities.length,
        topScore: rankedEntities[0]?.rankingScore,
        avgScore: rankedEntities.reduce((sum, e) => sum + e.rankingScore, 0) / rankedEntities.length,
      });

      return rankedEntities;
    } catch (error) {
      this.logger.warn('Advanced ranking failed, using basic sorting', error.stack);

      // Fallback to basic sorting
      return entities.sort((a, b) => {
        const aScore = (a.avgRating || 0) + Math.log(Math.max(1, a.reviewCount || 0)) * 0.1;
        const bScore = (b.avgRating || 0) + Math.log(Math.max(1, b.reviewCount || 0)) * 0.1;
        return bScore - aScore;
      });
    }
  }

  /**
   * Ensure we have an EnhancedConversationContext with all required properties
   */
  private ensureEnhancedContext(context: any): any {
    if (context.accumulatedFilters && context.pendingClarifications && context.enhancedMetadata) {
      return context; // Already enhanced
    }

    return {
      ...context,
      accumulatedFilters: context.accumulatedFilters || {
        filters: {},
        confidence: {},
        history: {},
        source: {},
      },
      pendingClarifications: context.pendingClarifications || [],
      enhancedMetadata: context.enhancedMetadata || {
        filtersExtracted: 0,
        clarificationQuestions: 0,
        conversationQuality: 0.5,
        readyForRecommendations: false,
      },
    };
  }

  /**
   * Apply filter corrections to conversation context
   */
  private applyFilterCorrections(context: any, corrections: any[]): void {
    if (!context.accumulatedFilters) {
      context.accumulatedFilters = { filters: {}, confidence: {}, history: {}, source: {} };
    }

    corrections.forEach(correction => {
      context.accumulatedFilters.filters[correction.filterKey] = correction.newValue;
      context.accumulatedFilters.confidence[correction.filterKey] = correction.confidence;
      context.accumulatedFilters.source[correction.filterKey] = 'user_correction';

      this.logger.debug(`Applied filter correction: ${correction.filterKey} = ${correction.newValue}`);
    });
  }

  /**
   * Convert intelligent response to chat response format
   */
  private convertIntelligentResponseToChatResponse(intelligentResponse: any, context: any): any {
    return {
      message: intelligentResponse.message,
      conversationStage: intelligentResponse.conversationStage,
      intent: {
        type: 'discovery',
        confidence: 0.8,
      },
      suggestedActions: intelligentResponse.suggestedActions?.map((action: any) => ({
        type: action.type,
        label: action.label,
        data: { description: action.description, priority: action.priority },
      })) || [],
      discoveredEntities: intelligentResponse.discoveredEntities?.map((entity: any) => ({
        id: entity.id,
        name: entity.name,
        relevanceScore: entity.relevanceScore,
        reason: `Matches ${entity.matchedFilters.join(', ')}`,
      })) || [],
      followUpQuestions: intelligentResponse.strategicQuestions?.map((q: any) => q.question) || [],
      shouldTransitionToRecommendations: intelligentResponse.readyForRecommendations,
      metadata: {
        llmProvider: 'INTELLIGENT_SYSTEM',
        responseTime: 0,
        tokensUsed: 0,
        enhancedFeatures: {
          filtersAccumulated: intelligentResponse.metadata.filtersAccumulated,
          correctionHandled: intelligentResponse.metadata.correctionHandled,
          actionType: intelligentResponse.metadata.actionType,
          conversationGuidance: intelligentResponse.conversationGuidance,
        },
      },
    };
  }

  /**
   * Get performance metrics
   */
  getPerformanceMetrics(): any {
    return this.performanceMonitor.getMetrics();
  }

  /**
   * Get performance health status
   */
  getPerformanceHealth(): any {
    return this.performanceMonitor.getHealthStatus();
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): any {
    return this.cacheService.getCacheStats();
  }

  /**
   * Clear caches (admin operation)
   */
  clearCaches(): void {
    this.cacheService.clearAllCaches();
  }

  /**
   * Get current LLM provider for response metadata
   */
  private async getCurrentLlmProvider(): Promise<string> {
    try {
      // This is a simple way to get the current provider
      // In a more sophisticated setup, you might want to track this differently
      const providers = this.llmFactoryService.getAvailableProviders();
      return providers[0] || 'UNKNOWN';
    } catch (error) {
      this.logger.error('Error getting current LLM provider', error.stack);
      return 'UNKNOWN';
    }
  }
}
