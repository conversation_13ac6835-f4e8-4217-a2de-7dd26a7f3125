import { Test, TestingModule } from '@nestjs/testing';
import { SimpleChatService } from './simple-chat.service';
import { ConversationManagerService } from './conversation-manager.service';
import { ChatErrorHandlerService } from './chat-error-handler.service';
import { LlmFailoverService } from './llm-failover.service';
import { QuestionSuggestionService } from './question-suggestion.service';
import { EntitiesService } from '../../entities/entities.service';
import { LlmFactoryService } from '../../common/llm/services/llm-factory.service';
import { ILlmService } from '../../common/llm/interfaces/llm.service.interface';

describe('SimpleChatService', () => {
  let service: SimpleChatService;
  let mockConversationManager: jest.Mocked<ConversationManagerService>;
  let mockChatErrorHandler: jest.Mocked<ChatErrorHandlerService>;
  let mockLlmFailover: jest.Mocked<LlmFailoverService>;
  let mockQuestionSuggestion: jest.Mocked<QuestionSuggestionService>;
  let mockEntitiesService: jest.Mocked<EntitiesService>;
  let mockLlmService: jest.Mocked<ILlmService>;
  let mockLlmFactoryService: jest.Mocked<LlmFactoryService>;

  beforeEach(async () => {
    // Create mocks
    mockConversationManager = {
      getOrCreateConversation: jest.fn(),
      updateUserPreferences: jest.fn(),
      addMessage: jest.fn(),
      getConversationHistory: jest.fn(),
      endConversation: jest.fn(),
      getUserActiveSessions: jest.fn(),
    } as any;

    mockChatErrorHandler = {
      handleCriticalError: jest.fn(),
    } as any;

    mockLlmFailover = {
      generateChatResponse: jest.fn(),
    } as any;

    mockQuestionSuggestion = {
      generateFollowUpQuestions: jest.fn(),
    } as any;

    mockEntitiesService = {
      findAll: jest.fn(),
    } as any;

    mockLlmService = {
      getProviderName: jest.fn().mockReturnValue('test-provider'),
    } as any;

    mockLlmFactoryService = {} as any;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        SimpleChatService,
        {
          provide: ConversationManagerService,
          useValue: mockConversationManager,
        },
        {
          provide: ChatErrorHandlerService,
          useValue: mockChatErrorHandler,
        },
        {
          provide: LlmFailoverService,
          useValue: mockLlmFailover,
        },
        {
          provide: QuestionSuggestionService,
          useValue: mockQuestionSuggestion,
        },
        {
          provide: EntitiesService,
          useValue: mockEntitiesService,
        },
        {
          provide: 'ILlmService',
          useValue: mockLlmService,
        },
        {
          provide: LlmFactoryService,
          useValue: mockLlmFactoryService,
        },
      ],
    }).compile();

    service = module.get<SimpleChatService>(SimpleChatService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('sendMessage', () => {
    it('should process a chat message successfully with follow-up questions', async () => {
      const userId = 'test-user-123';
      const sendChatMessageDto = {
        message: 'I need help finding AI tools for education',
        session_id: 'test-session',
        user_preferences: {
          technical_level: 'beginner' as const,
          preferred_categories: ['Education'],
        },
      };

      const mockContext = {
        sessionId: 'test-session',
        userId,
        messages: [],
        discoveredEntities: [],
        userPreferences: {},
        conversationStage: 'discovery' as const,
        metadata: {
          startedAt: new Date(),
          lastActiveAt: new Date(),
          totalMessages: 0,
          entitiesShown: [],
        },
      };

      const mockUpdatedContext = {
        ...mockContext,
        messages: [
          { role: 'user', content: sendChatMessageDto.message, timestamp: new Date() },
        ],
      };

      const mockFinalContext = {
        ...mockUpdatedContext,
        messages: [
          ...mockUpdatedContext.messages,
          { role: 'assistant', content: 'I can help you find educational AI tools!', timestamp: new Date() },
        ],
      };

      const mockEntities = [
        { id: '1', name: 'Gradescope', description: 'AI-powered grading platform' },
      ];

      const mockLlmResponse = {
        message: 'I can help you find educational AI tools!',
      };

      const mockFollowUpQuestions = [
        'What specific educational tasks do you need help with?',
        'Are you looking for tools for lesson planning or student assessment?',
      ];

      // Setup mocks
      mockConversationManager.getOrCreateConversation.mockResolvedValue(mockContext);
      mockConversationManager.updateUserPreferences.mockResolvedValue();
      mockConversationManager.addMessage
        .mockResolvedValueOnce(mockUpdatedContext)
        .mockResolvedValueOnce(mockFinalContext);
      mockEntitiesService.findAll.mockResolvedValue({ data: mockEntities });
      mockLlmFailover.generateChatResponse.mockResolvedValue(mockLlmResponse);
      mockQuestionSuggestion.generateFollowUpQuestions.mockReturnValue(mockFollowUpQuestions);

      const result = await service.sendMessage(userId, sendChatMessageDto);

      expect(result).toBeDefined();
      expect(result.message).toBe('I can help you find educational AI tools!');
      expect(result.session_id).toBe('test-session');
      expect(result.discovered_entities).toHaveLength(1);
      expect(result.follow_up_questions).toEqual(mockFollowUpQuestions);
      expect(result.should_transition_to_recommendations).toBe(false);

      // Verify service calls
      expect(mockConversationManager.getOrCreateConversation).toHaveBeenCalledWith(userId, 'test-session');
      expect(mockConversationManager.updateUserPreferences).toHaveBeenCalledWith('test-session', sendChatMessageDto.user_preferences);
      expect(mockQuestionSuggestion.generateFollowUpQuestions).toHaveBeenCalledWith(mockFinalContext, sendChatMessageDto.message, 2);
    });

    it('should handle entity discovery failure gracefully', async () => {
      const userId = 'test-user-123';
      const sendChatMessageDto = {
        message: 'I need help',
        session_id: 'test-session',
      };

      const mockContext = {
        sessionId: 'test-session',
        userId,
        messages: [],
        discoveredEntities: [],
        userPreferences: {},
        conversationStage: 'discovery' as const,
        metadata: {
          startedAt: new Date(),
          lastActiveAt: new Date(),
          totalMessages: 0,
          entitiesShown: [],
        },
      };

      // Setup mocks - entity service fails
      mockConversationManager.getOrCreateConversation.mockResolvedValue(mockContext);
      mockConversationManager.addMessage.mockResolvedValue(mockContext);
      mockEntitiesService.findAll.mockRejectedValue(new Error('Entity service error'));
      mockLlmFailover.generateChatResponse.mockResolvedValue({ message: 'How can I help?' });
      mockQuestionSuggestion.generateFollowUpQuestions.mockReturnValue(['What are you looking for?']);

      const result = await service.sendMessage(userId, sendChatMessageDto);

      expect(result).toBeDefined();
      expect(result.discovered_entities).toEqual([]);
      expect(result.follow_up_questions).toEqual(['What are you looking for?']);
    });

    it('should handle LLM failure with fallback response', async () => {
      const userId = 'test-user-123';
      const sendChatMessageDto = {
        message: 'I need help',
        session_id: 'test-session',
      };

      const mockContext = {
        sessionId: 'test-session',
        userId,
        messages: [],
        discoveredEntities: [],
        userPreferences: {},
        conversationStage: 'discovery' as const,
        metadata: {
          startedAt: new Date(),
          lastActiveAt: new Date(),
          totalMessages: 0,
          entitiesShown: [],
        },
      };

      // Setup mocks - LLM fails
      mockConversationManager.getOrCreateConversation.mockResolvedValue(mockContext);
      mockConversationManager.addMessage.mockResolvedValue(mockContext);
      mockEntitiesService.findAll.mockResolvedValue({ data: [] });
      mockLlmFailover.generateChatResponse.mockRejectedValue(new Error('LLM error'));
      mockQuestionSuggestion.generateFollowUpQuestions.mockReturnValue(['What are you looking for?']);

      const result = await service.sendMessage(userId, sendChatMessageDto);

      expect(result).toBeDefined();
      expect(result.message).toContain('What specific task are you trying to accomplish?');
      expect(result.follow_up_questions).toEqual(['What are you looking for?']);
    });

    it('should handle critical errors with error handler', async () => {
      const userId = 'test-user-123';
      const sendChatMessageDto = {
        message: 'I need help',
        session_id: 'test-session',
      };

      const mockErrorResponse = {
        message: 'Sorry, something went wrong. Please try again.',
        session_id: 'test-session',
        discovered_entities: [],
        conversation_stage: 'discovery' as const,
        follow_up_questions: [],
        should_transition_to_recommendations: false,
        metadata: {
          response_time: 0,
          llm_provider: 'test-provider',
        },
        generated_at: new Date(),
      };

      // Setup mocks - conversation manager fails
      mockConversationManager.getOrCreateConversation.mockRejectedValue(new Error('Critical error'));
      mockChatErrorHandler.handleCriticalError.mockResolvedValue(mockErrorResponse);

      const result = await service.sendMessage(userId, sendChatMessageDto);

      expect(result).toEqual(mockErrorResponse);
      expect(mockChatErrorHandler.handleCriticalError).toHaveBeenCalledWith(
        expect.any(Error),
        'test-session',
        userId,
      );
    });
  });

  describe('getConversationHistory', () => {
    it('should get conversation history successfully', async () => {
      const userId = 'test-user-123';
      const dto = { limit: 20 };
      const mockHistory = {
        session_id: 'test-session',
        messages: [],
        metadata: {},
      };

      mockConversationManager.getConversationHistory.mockResolvedValue(mockHistory);

      const result = await service.getConversationHistory(userId, dto);

      expect(result).toEqual(mockHistory);
      expect(mockConversationManager.getConversationHistory).toHaveBeenCalledWith(userId, dto);
    });
  });

  describe('endConversation', () => {
    it('should end conversation successfully', async () => {
      const userId = 'test-user-123';
      const sessionId = 'test-session';

      mockConversationManager.endConversation.mockResolvedValue(undefined);

      await service.endConversation(userId, sessionId);

      expect(mockConversationManager.endConversation).toHaveBeenCalledWith(userId, sessionId);
    });
  });

  describe('getUserActiveSessions', () => {
    it('should get user active sessions successfully', async () => {
      const userId = 'test-user-123';
      const mockSessions = ['session-1', 'session-2'];

      mockConversationManager.getUserActiveSessions.mockResolvedValue(mockSessions);

      const result = await service.getUserActiveSessions(userId);

      expect(result).toEqual(mockSessions);
      expect(mockConversationManager.getUserActiveSessions).toHaveBeenCalledWith(userId);
    });
  });
});
