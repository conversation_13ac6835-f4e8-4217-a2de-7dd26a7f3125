import { Injectable, ConflictException, NotFoundException, InternalServerErrorException, ForbiddenException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service'; // Adjust path if needed
import { UpdateProfileDto } from './dto/update-profile.dto';
// Remove PrismaClient import if User type comes from generated prisma client
// import { PrismaClient, User } from '@prisma/client';
import { User, UserNotificationSettings, UserStatus, UserRole } from 'generated/prisma'; // Import User type from generated prisma client location
import { Prisma } from 'generated/prisma'; // Import Prisma namespace for types like UserUpdateInput
import { UpdateNotificationSettingsDto } from './dto/update-notification-settings.dto';
import { ValidationService } from '../common/validation.service';
import { SupabaseService } from '../supabase/supabase.service'; // Assuming SupabaseService is imported
// import { UserStatus } from '../../generated/prisma'; // Already imported above

// Define a type for the user data returned by findAllUsers
export interface AdminUserListItem {
  id: string;
  username: string | null;
  displayName: string | null;
  email: string;
  role: UserRole;
  status: UserStatus;
  createdAt: Date;
  updatedAt: Date;
  lastLogin: Date | null;
}

// Define allowed sortable fields for admin user list
type UserSortableFields = 'createdAt' | 'username' | 'email' | 'status' | 'role' | 'lastLogin' | 'updatedAt' | 'displayName';

@Injectable()
export class UserService {
  constructor(
    private readonly prismaService: PrismaService,
    private readonly supabaseService: SupabaseService,
    private readonly validationService: ValidationService,
  ) {} // Inject PrismaService, SupabaseService, and ValidationService

  async findProfileById(userId: string): Promise<User | null> {
    return this.prismaService.user.findUnique({
      where: { id: userId },
    });
  }

  async getUserUpvotedIds(userId: string): Promise<string[]> {
    const upvotes = await this.prismaService.userUpvote.findMany({
      where: { userId: userId },
      select: { entityId: true },
    });

    return upvotes.map(upvote => upvote.entityId);
  }

  async updateProfile(userId: string, dto: UpdateProfileDto): Promise<User> {
    // Validate the profile update
    await this.validationService.validateProfileUpdate(userId, dto);

    // 1. If username is being changed, check for uniqueness first (redundant with validation but kept for safety)
    if (dto.username) {
      const existingUserWithUsername = await this.prismaService.user.findFirst({
        where: {
          username: dto.username,
          NOT: { id: userId }, // Exclude the current user from the check
        },
      });
      if (existingUserWithUsername) {
        throw new ConflictException('Username already taken. Please choose another.');
      }
    }

    // Prepare data for update, only including fields present in the DTO
    // Use Prisma.UserUpdateInput for better type safety
    const dataToUpdate: Prisma.UserUpdateInput = {};
    if (dto.display_name !== undefined) dataToUpdate.displayName = dto.display_name; // Corrected field name
    if (dto.username !== undefined) dataToUpdate.username = dto.username;
    if (dto.bio !== undefined) dataToUpdate.bio = dto.bio;
    if (dto.profile_picture_url !== undefined) dataToUpdate.profilePictureUrl = dto.profile_picture_url; // Corrected field name
    if (dto.social_links !== undefined) dataToUpdate.socialLinks = dto.social_links; // Corrected field name, Prisma handles JSON
    if (dto.technical_level !== undefined) dataToUpdate.technicalLevel = dto.technical_level; // Corrected field name

    // Debug log to verify the DTO values being received
    console.log('Received DTO:', JSON.stringify(dto, null, 2));

    if (Object.keys(dataToUpdate).length === 0) {
      // Fetch and return current profile if DTO is effectively empty
      const currentUser = await this.findProfileById(userId);
      if (!currentUser) throw new NotFoundException('User not found during update.'); // Should not happen if guard works
      return currentUser;
      // Alternatively, throw BadRequestException:
      // throw new BadRequestException('No valid fields provided for update.');
    }

    // 2. Update our public.users table
    try {
      // Debug log to verify the data being sent to Prisma
      console.log('Profile update data:', JSON.stringify(dataToUpdate, null, 2));

      const updatedProfile = await this.prismaService.user.update({
        where: { id: userId },
        data: dataToUpdate,
      });
      return updatedProfile;
    } catch (error) {
        // Log the error for debugging
        console.error("Error updating user profile:", error);
        // Handle potential Prisma errors (e.g., record not found P2025)
        if (error instanceof Prisma.PrismaClientKnownRequestError && error.code === 'P2025') {
            throw new NotFoundException(`User with ID ${userId} not found.`);
        }
        // Rethrow unexpected errors
        throw error;
    }
  }

  async findNotificationSettingsByUserId(userId: string): Promise<UserNotificationSettings | null> {
    return this.prismaService.userNotificationSettings.findUnique({
      where: { userId },
    });
  }

  async updateNotificationSettings(userId: string, dto: UpdateNotificationSettingsDto): Promise<UserNotificationSettings> {
    const dataToUpdate: Prisma.UserNotificationSettingsUpdateInput = {};
    if (dto.emailNewsletter !== undefined) dataToUpdate.emailNewsletter = dto.emailNewsletter;
    if (dto.emailNewEntityInFollowedCategory !== undefined) dataToUpdate.emailOnNewEntityInFollowed = dto.emailNewEntityInFollowedCategory;
    if (dto.emailNewEntityInFollowedTag !== undefined) dataToUpdate.emailOnNewEntityInFollowed = dto.emailNewEntityInFollowedTag;
    if (dto.emailNewReviewOnSavedEntity !== undefined) dataToUpdate.emailOnNewReview = dto.emailNewReviewOnSavedEntity;
    if (dto.emailUpdatesOnSavedEntity !== undefined) dataToUpdate.emailOnNewReview = dto.emailUpdatesOnSavedEntity;

    return this.prismaService.userNotificationSettings.upsert({
      where: { userId },
      update: dataToUpdate,
      create: {
        userId: userId,
        emailNewsletter: dto.emailNewsletter,
        emailOnNewEntityInFollowed: dto.emailNewEntityInFollowedCategory || dto.emailNewEntityInFollowedTag,
        emailOnNewReview: dto.emailNewReviewOnSavedEntity,
        emailOnReviewResponse: true,
        emailOnNewFollower: true,
      },
    });
  }

  async softDeleteUser(userId: string, authUserId: string): Promise<void> {
    const anonymizedUsername = `deleted_user_${Date.now()}`;
    try {
      await this.prismaService.user.update({
        where: { id: userId },
        data: {
          status: UserStatus.DELETED,
          username: anonymizedUsername,
          email: `${anonymizedUsername}@example.com`, 
          displayName: 'Deleted User',
          profilePictureUrl: null,
          bio: null, 
          socialLinks: Prisma.JsonNull, 
        },
      });
      console.log(`Soft deleted user in public.users table with ID: ${userId}`);
    } catch (error) {
      console.error(`Error soft deleting user in public.users table with ID: ${userId}`, error);
      throw new InternalServerErrorException('Failed to update user profile during deletion process.');
    }

    try {
      const supabaseAdmin = this.supabaseService.getAdminClient(); 
      const { error: supabaseDeleteError } = await supabaseAdmin.auth.admin.deleteUser(authUserId);
      if (supabaseDeleteError) {
        console.error(`CRITICAL: Failed to delete Supabase auth user with ID: ${authUserId} after soft deleting profile.`, supabaseDeleteError);
        throw new InternalServerErrorException(`User profile was soft-deleted, but failed to delete authentication record. Please contact support. Error: ${supabaseDeleteError.message}`);
      }
      console.log(`Successfully deleted Supabase auth user with ID: ${authUserId}`);
    } catch (error) {
      console.error(`Exception during Supabase auth user deletion for ID: ${authUserId}`, error);
      throw new InternalServerErrorException('An unexpected error occurred while deleting the authentication record.');
    }
  }

  // Admin: Find all users with pagination and optional filtering/sorting
  async findAllUsers(options: {
    page?: number;
    limit?: number;
    filterByStatus?: UserStatus;
    sortBy?: UserSortableFields; // Use the defined type for sortBy
    sortOrder?: 'asc' | 'desc';
  }): Promise<{ users: AdminUserListItem[]; total: number; page: number; limit: number }> { // Adjusted return type
    const { page = 1, limit = 10, filterByStatus, sortBy = 'createdAt', sortOrder = 'desc' } = options;
    const skip = (page - 1) * limit;

    const where: Prisma.UserWhereInput = {};
    if (filterByStatus) {
      where.status = filterByStatus;
    }

    const orderBy: Prisma.UserOrderByWithRelationInput = {};
    if (sortBy) {
      orderBy[sortBy] = sortOrder; // Now type-safe due to UserSortableFields
    }

    const users: AdminUserListItem[] = await this.prismaService.user.findMany({
      where,
      skip,
      take: limit,
      orderBy,
      select: {
        id: true,
        username: true,
        displayName: true,
        email: true,
        role: true,
        status: true,
        createdAt: true,
        updatedAt: true,
        lastLogin: true,
      }
    });

    const total = await this.prismaService.user.count({
      where,
    });

    return { users, total, page, limit };
  }

  // Admin: Update a user's status
  async updateUserStatus(adminPerformingUpdate: User, targetUserId: string, newStatus: UserStatus): Promise<User> {
    if (adminPerformingUpdate.id === targetUserId) {
      throw new ForbiddenException('Admins cannot change their own status using this endpoint.');
    }

    if (newStatus === UserStatus.DELETED) {
      throw new BadRequestException('Cannot set status to DELETED using this endpoint. Please use the account deletion process.');
    }

    // Fetch the user to ensure they exist before attempting update
    const targetUser = await this.prismaService.user.findUnique({
      where: { id: targetUserId },
    });

    if (!targetUser) {
      throw new NotFoundException(`User with ID ${targetUserId} not found.`);
    }
    
    // Optional: Add logic for allowed status transitions if needed
    // e.g., if targetUser.status === UserStatus.DELETED && newStatus === UserStatus.ACTIVE (reactivation)

    try {
      return this.prismaService.user.update({
        where: { id: targetUserId },
        data: { status: newStatus },
      });
    } catch (error) {
      console.error(`Error updating status for user ${targetUserId}:`, error);
      // Handle potential Prisma errors (e.g., if the status enum value is somehow still invalid despite checks)
      throw new InternalServerErrorException('Failed to update user status.');
    }
  }

  // Admin: Update a user's role
  async updateUserRole(adminPerformingUpdate: User, targetUserId: string, newRole: UserRole): Promise<User> {
    if (adminPerformingUpdate.id === targetUserId) {
      throw new ForbiddenException('Admins cannot change their own role using this endpoint.');
    }

    // Fetch the target user
    const targetUser = await this.prismaService.user.findUnique({
      where: { id: targetUserId },
    });

    if (!targetUser) {
      throw new NotFoundException(`User with ID ${targetUserId} not found.`);
    }

    // Critical check: Prevent removing the last admin role
    if (targetUser.role === UserRole.ADMIN && newRole !== UserRole.ADMIN) {
      const adminCount = await this.prismaService.user.count({
        where: { role: UserRole.ADMIN },
      });
      if (adminCount <= 1) {
        throw new ForbiddenException('Cannot remove the last admin role. At least one admin must exist.');
      }
    }

    try {
      return this.prismaService.user.update({
        where: { id: targetUserId },
        data: { role: newRole },
      });
    } catch (error) {
      console.error(`Error updating role for user ${targetUserId}:`, error);
      throw new InternalServerErrorException('Failed to update user role.');
    }
  }
}
