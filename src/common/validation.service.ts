import { Injectable, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { EntityStatus } from '../../generated/prisma';

@Injectable()
export class ValidationService {
  constructor(private readonly prismaService: PrismaService) {}

  /**
   * Validate tool request business rules
   */
  async validateToolRequest(userId: string, toolName: string): Promise<void> {
    // Check if user has reached daily limit (5 requests per day)
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    const todayRequestsCount = await this.prismaService.toolRequest.count({
      where: {
        userId,
        createdAt: {
          gte: today,
          lt: tomorrow,
        },
      },
    });

    if (todayRequestsCount >= 5) {
      throw new BadRequestException('You have reached the daily limit of 5 tool requests. Please try again tomorrow.');
    }

    // Check if user has already requested this tool (case-insensitive)
    const existingRequest = await this.prismaService.toolRequest.findFirst({
      where: {
        userId,
        toolName: {
          equals: toolName,
          mode: 'insensitive',
        },
        status: {
          in: ['PENDING', 'UNDER_REVIEW', 'APPROVED'],
        },
      },
    });

    if (existingRequest) {
      throw new BadRequestException('You have already requested this tool. Please wait for the existing request to be processed.');
    }
  }

  /**
   * Validate user preferences
   */
  validateUserPreferences(preferences: any): void {
    // Validate items per page
    if (preferences.itemsPerPage !== undefined) {
      const validValues = [10, 20, 50, 100];
      if (!validValues.includes(preferences.itemsPerPage)) {
        throw new BadRequestException(`Items per page must be one of: ${validValues.join(', ')}`);
      }
    }

    // Validate categories arrays
    if (preferences.preferredCategories && preferences.preferredCategories.length > 20) {
      throw new BadRequestException('You can select a maximum of 20 preferred categories.');
    }

    if (preferences.blockedCategories && preferences.blockedCategories.length > 50) {
      throw new BadRequestException('You can block a maximum of 50 categories.');
    }

    // Validate language code
    if (preferences.contentLanguage && !/^[a-z]{2}(-[A-Z]{2})?$/.test(preferences.contentLanguage)) {
      throw new BadRequestException('Content language must be a valid language code (e.g., "en", "en-US").');
    }
  }

  /**
   * Validate profile update
   */
  async validateProfileUpdate(userId: string, profileData: any): Promise<void> {
    // Validate username uniqueness if provided
    if (profileData.username) {
      // Check username format
      if (!/^[a-zA-Z0-9_]{3,30}$/.test(profileData.username)) {
        throw new BadRequestException('Username must be 3-30 characters long and contain only letters, numbers, and underscores.');
      }

      // Check if username is already taken by another user
      const existingUser = await this.prismaService.user.findFirst({
        where: {
          username: {
            equals: profileData.username,
            mode: 'insensitive',
          },
          id: {
            not: userId,
          },
        },
      });

      if (existingUser) {
        throw new BadRequestException('This username is already taken. Please choose a different one.');
      }
    }

    // Validate display name length
    if (profileData.displayName && profileData.displayName.length > 100) {
      throw new BadRequestException('Display name cannot exceed 100 characters.');
    }

    // Validate bio length
    if (profileData.bio && profileData.bio.length > 500) {
      throw new BadRequestException('Bio cannot exceed 500 characters.');
    }

    // Validate social links
    if (profileData.socialLinks) {
      const validDomains = {
        website: null, // Any valid URL
        twitter: ['twitter.com', 'x.com'],
        linkedin: ['linkedin.com'],
        github: ['github.com'],
      };

      for (const [platform, url] of Object.entries(profileData.socialLinks)) {
        if (url && typeof url === 'string') {
          try {
            const urlObj = new URL(url);
            const allowedDomains = validDomains[platform as keyof typeof validDomains];
            
            if (allowedDomains && !allowedDomains.some(domain => urlObj.hostname.includes(domain))) {
              throw new BadRequestException(`${platform} URL must be from ${allowedDomains.join(' or ')}`);
            }
          } catch {
            throw new BadRequestException(`Invalid URL format for ${platform}`);
          }
        }
      }
    }
  }

  /**
   * Validate entity submission rate limits
   */
  async validateEntitySubmissionRate(userId: string): Promise<void> {
    // Check if user has reached daily limit (3 submissions per day for regular users)
    const user = await this.prismaService.user.findUnique({
      where: { id: userId },
      select: { role: true },
    });

    if (!user) {
      throw new BadRequestException('User not found.');
    }

    // Admin users have unlimited submissions for scraping purposes
    if (user.role === 'ADMIN') {
      return; // No rate limiting for admins
    }

    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    const todaySubmissionsCount = await this.prismaService.userSubmittedTool.count({
      where: {
        userId,
        submittedAt: {
          gte: today,
          lt: tomorrow,
        },
      },
    });

    const dailyLimit = user.role === 'MODERATOR' ? 20 : 3;

    if (todaySubmissionsCount >= dailyLimit) {
      throw new BadRequestException(`You have reached the daily limit of ${dailyLimit} entity submissions. Please try again tomorrow.`);
    }
  }

  /**
   * Validate review submission
   */
  async validateReviewSubmission(userId: string, entityId: string): Promise<void> {
    // Check if user has already reviewed this entity
    const existingReview = await this.prismaService.review.findUnique({
      where: {
        entityId_userId: {
          entityId,
          userId,
        },
      },
    });

    if (existingReview) {
      throw new BadRequestException('You have already reviewed this entity. You can update your existing review instead.');
    }

    // Check if user is trying to review their own submission
    const entity = await this.prismaService.entity.findUnique({
      where: { id: entityId },
      select: { submitterId: true },
    });

    if (entity?.submitterId === userId) {
      throw new BadRequestException('You cannot review an entity that you submitted.');
    }
  }

  /**
   * Validate bookmark action
   */
  async validateBookmarkAction(userId: string, entityId: string): Promise<void> {
    // Check if entity exists and is published
    const entity = await this.prismaService.entity.findUnique({
      where: { id: entityId },
      select: { status: true },
    });

    if (!entity) {
      throw new BadRequestException('Entity not found.');
    }

    if (entity.status !== EntityStatus.ACTIVE) {
      throw new BadRequestException('You can only bookmark active entities.');
    }
  }
}
