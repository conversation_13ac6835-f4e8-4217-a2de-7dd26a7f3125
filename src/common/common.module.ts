import { Module, Global } from '@nestjs/common';
import { ActivityLoggerService } from './activity-logger.service';
import { StatisticsCalculatorService } from './statistics-calculator.service';
import { ValidationService } from './validation.service';
import { AdminThrottlerGuard } from './guards/admin-throttler.guard';
import { PrismaModule } from '../prisma/prisma.module';

@Global()
@Module({
  imports: [PrismaModule],
  providers: [ActivityLoggerService, StatisticsCalculatorService, ValidationService, AdminThrottlerGuard],
  exports: [ActivityLoggerService, StatisticsCalculatorService, ValidationService, AdminThrottlerGuard],
})
export class CommonModule {}
