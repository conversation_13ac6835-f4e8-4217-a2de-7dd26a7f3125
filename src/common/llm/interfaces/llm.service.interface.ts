export interface LlmRecommendation {
  recommendedEntityIds: string[];
  explanation: string;
}

// Chat-specific interfaces
export interface ChatMessage {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  metadata?: Record<string, any>;
}

export interface UserIntent {
  type: 'discovery' | 'comparison' | 'specific_tool' | 'general_question' | 'refinement';
  confidence: number;
  entities?: string[]; // Entity IDs or names mentioned
  categories?: string[]; // Categories of interest
  features?: string[]; // Specific features mentioned
  constraints?: {
    budget?: 'free' | 'low' | 'medium' | 'high';
    technical_level?: 'beginner' | 'intermediate' | 'advanced';
    use_case?: string;
  };
}

/**
 * Enhanced User Intent with comprehensive filter extraction capabilities
 * Extends the basic UserIntent to include detailed filter criteria extracted from natural language
 */
export interface EnhancedUserIntent extends UserIntent {
  // Extracted filter criteria from natural language (using ListEntitiesDto structure)
  extractedFilters: {
    // Core filters
    entityTypeIds?: string[];
    searchTerm?: string;

    // Tool-specific filters
    technical_levels?: string[];
    learning_curves?: string[];
    has_api?: boolean;
    has_free_tier?: boolean;
    open_source?: boolean;
    platforms?: string[];
    frameworks?: string[];
    use_cases_search?: string;
    key_features_search?: string;

    // Course-specific filters
    skill_levels?: string[];
    certificate_available?: boolean;
    instructor_name?: string;
    duration_text?: string;

    // Job-specific filters
    employment_types?: string[];
    experience_levels?: string[];
    location_types?: string[];
    salary_min?: number;
    salary_max?: number;
    company_name?: string;

    // Event-specific filters
    event_types?: string[];
    is_online?: boolean;
    location?: string;
    start_date_from?: string;
    start_date_to?: string;

    // Hardware-specific filters
    hardware_types?: string[];
    manufacturers?: string[];
    price_min?: number;
    price_max?: number;
    memory_search?: string;

    // And other entity-specific filters...
    [key: string]: any;
  };

  // Confidence scores for each extracted filter (0-1)
  filterConfidence: Record<string, number>;

  // Missing information needed for better recommendations
  missingCriteria: {
    entityTypes?: boolean;
    technicalLevel?: boolean;
    budget?: boolean;
    useCase?: boolean;
    platform?: boolean;
    specificRequirements?: boolean;
  };

  // Suggested clarifying questions to gather missing information
  clarifyingQuestions: SmartFollowUpQuestion[];
}

/**
 * Smart follow-up question with context and purpose
 */
export interface SmartFollowUpQuestion {
  question: string;
  purpose: 'entity_type' | 'technical_level' | 'budget' | 'use_case' | 'platform' | 'refinement' | 'requirements';
  expectedFilterKeys: string[]; // Which filters this question will help populate
  priority: number; // Higher = more important to ask (1-10)
  suggestedAnswers?: string[]; // Optional predefined answers
  followUpContext?: string; // Additional context for the LLM
}

export interface ConversationContext {
  sessionId: string;
  userId: string;
  messages: ChatMessage[];
  currentIntent?: UserIntent;
  discoveredEntities: string[]; // Entity IDs discovered so far
  userPreferences: {
    budget?: 'free' | 'low' | 'medium' | 'high';
    technical_level?: 'beginner' | 'intermediate' | 'advanced';
    preferred_categories?: string[];
    excluded_categories?: string[];
  };
  conversationStage: 'greeting' | 'discovery' | 'refinement' | 'recommendation' | 'comparison';
  metadata: {
    startedAt: Date;
    lastActiveAt: Date;
    totalMessages: number;
    entitiesShown: string[];
  };
}

/**
 * Enhanced Conversation Context with comprehensive filter tracking
 * Extends the basic ConversationContext to include accumulated filters and conversation state
 */
export interface EnhancedConversationContext extends ConversationContext {
  // Enhanced intent tracking
  currentIntent?: EnhancedUserIntent;

  // Accumulated filters from conversation history
  accumulatedFilters: {
    // All filters gathered throughout the conversation
    filters: Record<string, any>;

    // Confidence in each filter (higher = more certain)
    confidence: Record<string, number>;

    // Conversation turn when each filter was established
    history: Record<string, number>;

    // Source of each filter ('user_explicit', 'extracted', 'inferred', 'clarified')
    source: Record<string, 'user_explicit' | 'extracted' | 'inferred' | 'clarified'>;
  };

  // Filters that need clarification or are pending user input
  pendingClarifications: {
    filterKey: string;
    question: string;
    priority: number;
    askedAt?: Date;
  }[];

  // Enhanced metadata
  enhancedMetadata: {
    // Number of filters successfully extracted
    filtersExtracted: number;

    // Number of clarifying questions asked
    clarificationQuestions: number;

    // Quality score of the conversation (0-1)
    conversationQuality: number;

    // Whether the user seems ready for recommendations
    readyForRecommendations: boolean;

    // Last entity discovery attempt
    lastEntityDiscovery?: {
      timestamp: Date;
      filtersUsed: Record<string, any>;
      entitiesFound: number;
    };
  };
}

export interface ChatResponse {
  message: string;
  intent: UserIntent;
  suggestedActions?: Array<{
    type: 'ask_question' | 'show_entities' | 'refine_search' | 'get_recommendations';
    label: string;
    data?: any;
  }>;
  discoveredEntities?: Array<{
    id: string;
    name: string;
    relevanceScore: number;
    reason: string;
  }>;
  followUpQuestions?: string[];
  shouldTransitionToRecommendations: boolean;
  conversationStage: ConversationContext['conversationStage'];
  metadata: {
    responseTime: number;
    llmProvider: string;
    tokensUsed?: number;
  };
}

export interface CandidateEntity {
  id: string;
  name: string;
  shortDescription: string | null;
  description: string | null;
  entityType: {
    name: string;
    slug: string;
  };
  categories: Array<{
    category: {
      name: string;
      slug: string;
    };
  }>;
  tags: Array<{
    tag: {
      name: string;
      slug: string;
    };
  }>;
  features: Array<{
    feature: {
      name: string;
      slug: string;
    };
  }>;
  // Additional details that might be useful for LLM context
  websiteUrl?: string | null;
  logoUrl?: string | null;
  avgRating?: number;
  reviewCount?: number;
}

export interface ILlmService {
  /**
   * Get AI-powered recommendations based on a problem description and candidate entities
   * @param problemDescription - The user's problem description
   * @param candidateEntities - Array of entities found through vector search
   * @returns Promise containing recommended entity IDs and explanation
   */
  getRecommendation(
    problemDescription: string,
    candidateEntities: CandidateEntity[],
  ): Promise<LlmRecommendation>;

  /**
   * Generate a conversational chat response based on user message and conversation context
   * @param userMessage - The user's current message
   * @param context - Full conversation context including history and discovered entities
   * @param candidateEntities - Optional array of entities to consider in the response
   * @returns Promise containing chat response with intent analysis and suggestions
   */
  getChatResponse(
    userMessage: string,
    context: ConversationContext,
    candidateEntities?: CandidateEntity[],
  ): Promise<ChatResponse>;

  /**
   * Classify user intent from their message and conversation context
   * @param userMessage - The user's current message
   * @param context - Conversation context for better intent understanding
   * @returns Promise containing classified intent with confidence score
   */
  classifyIntent(
    userMessage: string,
    context: ConversationContext,
  ): Promise<UserIntent>;

  /**
   * Generate intelligent follow-up questions to guide the conversation
   * @param context - Current conversation context
   * @returns Promise containing array of suggested follow-up questions
   */
  generateFollowUpQuestions(
    context: ConversationContext,
  ): Promise<string[]>;

  /**
   * Determine if the conversation should transition to the recommendations endpoint
   * @param context - Current conversation context
   * @returns Promise containing boolean decision and reasoning
   */
  shouldTransitionToRecommendations(
    context: ConversationContext,
  ): Promise<{ shouldTransition: boolean; reason: string }>;
}
