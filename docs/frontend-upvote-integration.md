# Frontend Upvote Integration Guide

## Overview

The backend now includes a complete upvote functionality that allows authenticated users to upvote and remove their upvote from entities. This document provides everything the frontend team needs to integrate this feature.

## API Endpoints

### Base URL
```
http://localhost:3000 (development)
https://your-production-domain.com (production)
```

### Authentication Required
All upvote endpoints require JWT authentication via the `Authorization` header:
```
Authorization: Bearer {jwt_token}
```

### 1. Add Upvote
**Endpoint:** `POST /entities/{entityId}/upvote`

**Description:** Adds an upvote from the authenticated user to the specified entity.

**Parameters:**
- `entityId` (path, required): UUID of the entity to upvote

**Request Example:**
```javascript
const response = await fetch(`/entities/${entityId}/upvote`, {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${userToken}`,
    'Content-Type': 'application/json'
  }
});
```

**Response Codes:**
- `201 Created`: Upvote successfully added
- `200 OK`: User already upvoted this entity (idempotent operation)
- `400 Bad Request`: Invalid entity ID format
- `401 Unauthorized`: Missing or invalid JWT token
- `404 Not Found`: Entity not found
- `500 Internal Server Error`: Server error

**Success Response Body (201/200):**
```json
{
  "userId": "123e4567-e89b-12d3-a456-************",
  "entityId": "987fcdeb-51a2-43d1-9f4e-123456789abc",
  "createdAt": "2025-06-22T12:00:00.000Z"
}
```

### 2. Remove Upvote
**Endpoint:** `DELETE /entities/{entityId}/upvote`

**Description:** Removes the authenticated user's upvote from the specified entity.

**Parameters:**
- `entityId` (path, required): UUID of the entity to remove upvote from

**Request Example:**
```javascript
const response = await fetch(`/entities/${entityId}/upvote`, {
  method: 'DELETE',
  headers: {
    'Authorization': `Bearer ${userToken}`
  }
});
```

**Response Codes:**
- `204 No Content`: Upvote successfully removed (or didn't exist - idempotent)
- `400 Bad Request`: Invalid entity ID format
- `401 Unauthorized`: Missing or invalid JWT token
- `500 Internal Server Error`: Server error

**Success Response:** No body (204 status)

## Entity Data Changes

### Updated Entity Response
The entity objects now include an `upvoteCount` field:

```json
{
  "id": "987fcdeb-51a2-43d1-9f4e-123456789abc",
  "name": "ChatGPT",
  "slug": "chatgpt",
  "description": "AI-powered conversational assistant",
  "upvoteCount": 42,
  // ... other entity fields
}
```

### New Field: `upvoteCount`
- **Type:** `number`
- **Description:** Total number of upvotes this entity has received
- **Default:** `0`
- **Updates:** Automatically maintained by database triggers

## Frontend Implementation Examples

### React Hook Example
```typescript
import { useState, useCallback } from 'react';

interface UseUpvoteProps {
  entityId: string;
  initialUpvoteCount: number;
  userToken: string;
}

interface UseUpvoteReturn {
  upvoteCount: number;
  isUpvoted: boolean;
  isLoading: boolean;
  toggleUpvote: () => Promise<void>;
}

export const useUpvote = ({ 
  entityId, 
  initialUpvoteCount, 
  userToken 
}: UseUpvoteProps): UseUpvoteReturn => {
  const [upvoteCount, setUpvoteCount] = useState(initialUpvoteCount);
  const [isUpvoted, setIsUpvoted] = useState(false); // You'll need to track this
  const [isLoading, setIsLoading] = useState(false);

  const toggleUpvote = useCallback(async () => {
    if (isLoading) return;
    
    setIsLoading(true);
    
    try {
      const method = isUpvoted ? 'DELETE' : 'POST';
      const response = await fetch(`/api/entities/${entityId}/upvote`, {
        method,
        headers: {
          'Authorization': `Bearer ${userToken}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        if (isUpvoted) {
          // Removed upvote
          setUpvoteCount(prev => prev - 1);
          setIsUpvoted(false);
        } else {
          // Added upvote
          setUpvoteCount(prev => prev + 1);
          setIsUpvoted(true);
        }
      } else {
        // Handle error
        console.error('Upvote operation failed:', response.status);
      }
    } catch (error) {
      console.error('Upvote error:', error);
    } finally {
      setIsLoading(false);
    }
  }, [entityId, isUpvoted, isLoading, userToken]);

  return {
    upvoteCount,
    isUpvoted,
    isLoading,
    toggleUpvote
  };
};
```

### React Component Example
```tsx
import React from 'react';
import { useUpvote } from './hooks/useUpvote';

interface UpvoteButtonProps {
  entityId: string;
  initialUpvoteCount: number;
  userToken: string;
  className?: string;
}

export const UpvoteButton: React.FC<UpvoteButtonProps> = ({
  entityId,
  initialUpvoteCount,
  userToken,
  className = ''
}) => {
  const { upvoteCount, isUpvoted, isLoading, toggleUpvote } = useUpvote({
    entityId,
    initialUpvoteCount,
    userToken
  });

  return (
    <button
      onClick={toggleUpvote}
      disabled={isLoading}
      className={`upvote-button ${isUpvoted ? 'upvoted' : ''} ${className}`}
      aria-label={isUpvoted ? 'Remove upvote' : 'Add upvote'}
    >
      <span className="upvote-icon">
        {isUpvoted ? '❤️' : '🤍'}
      </span>
      <span className="upvote-count">
        {upvoteCount}
      </span>
      {isLoading && <span className="loading-spinner">⏳</span>}
    </button>
  );
};
```

### Vue.js Composition API Example
```typescript
import { ref, computed } from 'vue';

export function useUpvote(entityId: string, initialCount: number, userToken: string) {
  const upvoteCount = ref(initialCount);
  const isUpvoted = ref(false); // Track this based on user data
  const isLoading = ref(false);

  const toggleUpvote = async () => {
    if (isLoading.value) return;
    
    isLoading.value = true;
    
    try {
      const method = isUpvoted.value ? 'DELETE' : 'POST';
      const response = await fetch(`/api/entities/${entityId}/upvote`, {
        method,
        headers: {
          'Authorization': `Bearer ${userToken}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        if (isUpvoted.value) {
          upvoteCount.value--;
          isUpvoted.value = false;
        } else {
          upvoteCount.value++;
          isUpvoted.value = true;
        }
      }
    } catch (error) {
      console.error('Upvote error:', error);
    } finally {
      isLoading.value = false;
    }
  };

  return {
    upvoteCount: computed(() => upvoteCount.value),
    isUpvoted: computed(() => isUpvoted.value),
    isLoading: computed(() => isLoading.value),
    toggleUpvote
  };
}
```

## User Upvote State Tracking

### Important Note
The backend doesn't currently provide an endpoint to check if a user has upvoted a specific entity. You have two options:

### Option 1: Track Client-Side (Recommended for MVP)
```typescript
// Store user upvotes in localStorage or state management
const userUpvotes = new Set<string>(); // Set of entity IDs

// After successful upvote
userUpvotes.add(entityId);
localStorage.setItem('userUpvotes', JSON.stringify([...userUpvotes]));

// Check if upvoted
const isUpvoted = userUpvotes.has(entityId);
```

### Option 2: Request Backend Enhancement
Consider requesting an endpoint like:
```
GET /users/me/upvotes
GET /entities/{entityId}/upvoted-by-me
```

## Error Handling

### Common Error Scenarios
```typescript
const handleUpvoteError = (response: Response) => {
  switch (response.status) {
    case 400:
      return 'Invalid entity ID';
    case 401:
      return 'Please log in to upvote';
    case 404:
      return 'Entity not found';
    case 500:
      return 'Server error. Please try again.';
    default:
      return 'An unexpected error occurred';
  }
};
```

### Retry Logic
```typescript
const upvoteWithRetry = async (entityId: string, maxRetries = 3) => {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      const response = await fetch(`/api/entities/${entityId}/upvote`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${userToken}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (response.ok) return response;
      if (response.status < 500) throw new Error('Client error');
      
    } catch (error) {
      if (attempt === maxRetries) throw error;
      await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
    }
  }
};
```

## UI/UX Recommendations

### Visual States
1. **Default State**: Empty heart or thumbs up icon
2. **Upvoted State**: Filled heart or colored thumbs up
3. **Loading State**: Spinner or disabled appearance
4. **Error State**: Brief error message or red indicator

### Accessibility
```tsx
<button
  onClick={toggleUpvote}
  disabled={isLoading}
  aria-label={`${isUpvoted ? 'Remove' : 'Add'} upvote for ${entityName}`}
  aria-pressed={isUpvoted}
>
  {/* Button content */}
</button>
```

### Animation Examples
```css
.upvote-button {
  transition: all 0.2s ease;
}

.upvote-button:hover {
  transform: scale(1.05);
}

.upvote-button.upvoted {
  color: #e74c3c;
  animation: heartbeat 0.3s ease;
}

@keyframes heartbeat {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}
```

## Testing

### Unit Test Example (Jest + React Testing Library)
```typescript
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { UpvoteButton } from './UpvoteButton';

// Mock fetch
global.fetch = jest.fn();

describe('UpvoteButton', () => {
  beforeEach(() => {
    (fetch as jest.Mock).mockClear();
  });

  it('should toggle upvote state on click', async () => {
    (fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      status: 201,
      json: async () => ({ userId: '123', entityId: '456', createdAt: new Date() })
    });

    render(
      <UpvoteButton
        entityId="test-entity"
        initialUpvoteCount={5}
        userToken="test-token"
      />
    );

    const button = screen.getByRole('button');
    expect(screen.getByText('5')).toBeInTheDocument();

    fireEvent.click(button);

    await waitFor(() => {
      expect(screen.getByText('6')).toBeInTheDocument();
    });

    expect(fetch).toHaveBeenCalledWith('/api/entities/test-entity/upvote', {
      method: 'POST',
      headers: {
        'Authorization': 'Bearer test-token',
        'Content-Type': 'application/json'
      }
    });
  });
});
```

## Performance Considerations

### Optimistic Updates
```typescript
const toggleUpvote = async () => {
  // Optimistic update
  const previousState = { upvoteCount, isUpvoted };
  setUpvoteCount(prev => isUpvoted ? prev - 1 : prev + 1);
  setIsUpvoted(!isUpvoted);

  try {
    const response = await fetch(/* ... */);
    if (!response.ok) {
      // Revert on error
      setUpvoteCount(previousState.upvoteCount);
      setIsUpvoted(previousState.isUpvoted);
    }
  } catch (error) {
    // Revert on error
    setUpvoteCount(previousState.upvoteCount);
    setIsUpvoted(previousState.isUpvoted);
  }
};
```

### Debouncing
```typescript
import { debounce } from 'lodash';

const debouncedToggleUpvote = debounce(toggleUpvote, 300);
```

## Integration Checklist

- [ ] Add upvote buttons to entity cards/pages
- [ ] Implement user upvote state tracking
- [ ] Add error handling and user feedback
- [ ] Include loading states and animations
- [ ] Add accessibility attributes
- [ ] Write unit tests for upvote components
- [ ] Test with different user authentication states
- [ ] Verify upvote counts update correctly
- [ ] Test error scenarios (network failures, etc.)
- [ ] Implement optimistic updates for better UX

## Support

For questions or issues with the upvote API:
1. Check the Swagger documentation at `/api-docs`
2. Review the backend logs for error details
3. Test endpoints directly using curl or Postman
4. Refer to the comprehensive backend documentation in `docs/upvote-functionality.md`
