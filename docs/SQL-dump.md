-- WARNING: This schema is for context only and is not meant to be run.
-- Table order and constraints may not be valid for execution.

CREATE TABLE public._prisma_migrations (
  id character varying NOT NULL,
  checksum character varying NOT NULL,
  finished_at timestamp with time zone,
  migration_name character varying NOT NULL,
  logs text,
  rolled_back_at timestamp with time zone,
  started_at timestamp with time zone NOT NULL DEFAULT now(),
  applied_steps_count integer NOT NULL DEFAULT 0,
  CONSTRAINT _prisma_migrations_pkey PRIMARY KEY (id)
);
CREATE TABLE public.app_settings (
  key text NOT NULL,
  value text NOT NULL,
  description text,
  created_at timestamp without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp without time zone NOT NULL,
  CONSTRAINT app_settings_pkey PRIMARY KEY (key)
);
CREATE TABLE public.badge_types (
  id uuid NOT NULL,
  name text NOT NULL,
  description text,
  icon_url text,
  scope USER-DEFINED NOT NULL,
  created_at timestamp without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp without time zone NOT NULL,
  criteria jsonb,
  CONSTRAINT badge_types_pkey PRIMARY KEY (id)
);
CREATE TABLE public.categories (
  id uuid NOT NULL,
  name text NOT NULL,
  description text,
  slug text NOT NULL,
  icon_url text,
  created_at timestamp without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp without time zone NOT NULL,
  parent_id uuid,
  CONSTRAINT categories_pkey PRIMARY KEY (id),
  CONSTRAINT categories_parent_id_fkey FOREIGN KEY (parent_id) REFERENCES public.categories(id)
);
CREATE TABLE public.entities (
  id uuid NOT NULL,
  entity_type_id uuid NOT NULL,
  name text NOT NULL,
  short_description text,
  description text,
  logo_url text,
  website_url text,
  documentation_url text,
  contact_url text,
  privacy_policy_url text,
  founded_year integer,
  social_links jsonb,
  status USER-DEFINED NOT NULL DEFAULT 'PENDING'::"EntityStatus",
  avg_rating double precision NOT NULL DEFAULT 0,
  review_count integer NOT NULL DEFAULT 0,
  created_at timestamp without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp without time zone NOT NULL,
  legacy_id text,
  submitter_id uuid NOT NULL,
  affiliateStatus USER-DEFINED DEFAULT 'NONE'::"AffiliateStatus",
  location_summary text,
  meta_description text,
  meta_title text,
  ref_link text,
  scraped_review_count integer,
  scraped_review_sentiment_label text,
  scraped_review_sentiment_score double precision,
  vector_embedding USER-DEFINED,
  ftsDocument tsvector,
  employee_count_range USER-DEFINED,
  funding_stage USER-DEFINED,
  slug text NOT NULL,
  CONSTRAINT entities_pkey PRIMARY KEY (id),
  CONSTRAINT entities_entity_type_id_fkey FOREIGN KEY (entity_type_id) REFERENCES public.entity_types(id),
  CONSTRAINT entities_submitter_id_fkey FOREIGN KEY (submitter_id) REFERENCES public.users(id)
);
CREATE TABLE public.entity_badges (
  id uuid NOT NULL,
  entity_id uuid NOT NULL,
  badge_type_id uuid NOT NULL,
  granted_at timestamp without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
  notes text,
  expires_at timestamp without time zone,
  granted_by uuid,
  CONSTRAINT entity_badges_pkey PRIMARY KEY (id),
  CONSTRAINT entity_badges_granted_by_fkey FOREIGN KEY (granted_by) REFERENCES public.users(id),
  CONSTRAINT entity_badges_entity_id_fkey FOREIGN KEY (entity_id) REFERENCES public.entities(id),
  CONSTRAINT entity_badges_badge_type_id_fkey FOREIGN KEY (badge_type_id) REFERENCES public.badge_types(id)
);
CREATE TABLE public.entity_categories (
  entity_id uuid NOT NULL,
  category_id uuid NOT NULL,
  assigned_at timestamp without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
  assigned_by uuid NOT NULL,
  CONSTRAINT entity_categories_pkey PRIMARY KEY (entity_id, category_id),
  CONSTRAINT entity_categories_entity_id_fkey FOREIGN KEY (entity_id) REFERENCES public.entities(id),
  CONSTRAINT entity_categories_category_id_fkey FOREIGN KEY (category_id) REFERENCES public.categories(id)
);
CREATE TABLE public.entity_details_agency (
  entity_id uuid NOT NULL,
  services_offered jsonb,
  industry_focus jsonb,
  target_client_size jsonb,
  target_audience jsonb,
  location_summary text,
  portfolio_url text,
  pricing_info text,
  id uuid NOT NULL,
  CONSTRAINT entity_details_agency_pkey PRIMARY KEY (id),
  CONSTRAINT entity_details_agency_entity_id_fkey FOREIGN KEY (entity_id) REFERENCES public.entities(id)
);
CREATE TABLE public.entity_details_book (
  entity_id uuid NOT NULL,
  created_at timestamp without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp without time zone NOT NULL,
  isbn text,
  page_count integer,
  publisher text,
  purchase_url text,
  summary text,
  author text,
  format text,
  id uuid NOT NULL,
  publication_date timestamp without time zone,
  CONSTRAINT entity_details_book_pkey PRIMARY KEY (id),
  CONSTRAINT entity_details_book_entity_id_fkey FOREIGN KEY (entity_id) REFERENCES public.entities(id)
);
CREATE TABLE public.entity_details_bounty (
  entity_id uuid NOT NULL,
  created_at timestamp without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp without time zone NOT NULL,
  amount text,
  deadline timestamp without time zone,
  id uuid NOT NULL,
  platform text,
  required_skills ARRAY,
  status text,
  task_description text,
  url text,
  CONSTRAINT entity_details_bounty_pkey PRIMARY KEY (id),
  CONSTRAINT entity_details_bounty_entity_id_fkey FOREIGN KEY (entity_id) REFERENCES public.entities(id)
);
CREATE TABLE public.entity_details_community (
  entity_id uuid NOT NULL,
  platform text,
  member_count integer,
  focus_topics jsonb,
  rules_url text,
  invite_url text,
  main_channel_url text,
  id uuid NOT NULL,
  CONSTRAINT entity_details_community_pkey PRIMARY KEY (id),
  CONSTRAINT entity_details_community_entity_id_fkey FOREIGN KEY (entity_id) REFERENCES public.entities(id)
);
CREATE TABLE public.entity_details_content_creator (
  entity_id uuid NOT NULL,
  creator_name text,
  primary_platform text,
  focus_areas jsonb,
  follower_count integer,
  example_content_url text,
  id uuid NOT NULL,
  CONSTRAINT entity_details_content_creator_pkey PRIMARY KEY (id),
  CONSTRAINT entity_details_content_creator_entity_id_fkey FOREIGN KEY (entity_id) REFERENCES public.entities(id)
);
CREATE TABLE public.entity_details_course (
  entity_id uuid NOT NULL,
  instructor_name text,
  duration_text text,
  skill_level USER-DEFINED,
  prerequisites text,
  syllabus_url text,
  enrollment_count integer,
  certificate_available boolean DEFAULT false,
  id uuid NOT NULL,
  CONSTRAINT entity_details_course_pkey PRIMARY KEY (id),
  CONSTRAINT entity_details_course_entity_id_fkey FOREIGN KEY (entity_id) REFERENCES public.entities(id)
);
CREATE TABLE public.entity_details_dataset (
  entity_id uuid NOT NULL,
  created_at timestamp without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp without time zone NOT NULL,
  access_notes text,
  description text,
  license text,
  size_in_bytes bigint,
  source_url text,
  collection_method text,
  id uuid NOT NULL,
  update_frequency text,
  format text,
  CONSTRAINT entity_details_dataset_pkey PRIMARY KEY (id),
  CONSTRAINT entity_details_dataset_entity_id_fkey FOREIGN KEY (entity_id) REFERENCES public.entities(id)
);
CREATE TABLE public.entity_details_event (
  entity_id uuid NOT NULL,
  created_at timestamp without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp without time zone NOT NULL,
  end_date timestamp without time zone,
  location text,
  price text,
  registration_url text,
  start_date timestamp without time zone,
  event_type text,
  id uuid NOT NULL,
  is_online boolean,
  key_speakers ARRAY,
  target_audience ARRAY,
  topics ARRAY,
  CONSTRAINT entity_details_event_pkey PRIMARY KEY (id),
  CONSTRAINT entity_details_event_entity_id_fkey FOREIGN KEY (entity_id) REFERENCES public.entities(id)
);
CREATE TABLE public.entity_details_grant (
  entity_id uuid NOT NULL,
  created_at timestamp without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp without time zone NOT NULL,
  application_url text,
  amount text,
  deadline timestamp without time zone,
  eligibility text,
  focus_areas ARRAY,
  funder_name text,
  grant_type text,
  id uuid NOT NULL,
  location text,
  CONSTRAINT entity_details_grant_pkey PRIMARY KEY (id),
  CONSTRAINT entity_details_grant_entity_id_fkey FOREIGN KEY (entity_id) REFERENCES public.entities(id)
);
CREATE TABLE public.entity_details_hardware (
  entity_id uuid NOT NULL,
  created_at timestamp without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp without time zone NOT NULL,
  availability text,
  gpu text,
  id uuid NOT NULL,
  memory text,
  power_consumption text,
  price text,
  processor text,
  storage text,
  use_cases ARRAY,
  CONSTRAINT entity_details_hardware_pkey PRIMARY KEY (id),
  CONSTRAINT entity_details_hardware_entity_id_fkey FOREIGN KEY (entity_id) REFERENCES public.entities(id)
);
CREATE TABLE public.entity_details_investor (
  entity_id uuid NOT NULL,
  created_at timestamp without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp without time zone NOT NULL,
  contact_email text,
  application_url text,
  focus_areas ARRAY,
  id uuid NOT NULL,
  investment_stages ARRAY,
  investor_type text,
  location_summary text,
  notable_investments ARRAY,
  portfolio_size integer,
  CONSTRAINT entity_details_investor_pkey PRIMARY KEY (id),
  CONSTRAINT entity_details_investor_entity_id_fkey FOREIGN KEY (entity_id) REFERENCES public.entities(id)
);
CREATE TABLE public.entity_details_job (
  entity_id uuid NOT NULL,
  created_at timestamp without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp without time zone NOT NULL,
  application_url text,
  company_name text,
  experience_level text,
  id uuid NOT NULL,
  is_remote boolean,
  job_type text,
  key_responsibilities ARRAY,
  location text,
  required_skills ARRAY,
  salary_max double precision,
  salary_min double precision,
  CONSTRAINT entity_details_job_pkey PRIMARY KEY (id),
  CONSTRAINT entity_details_job_entity_id_fkey FOREIGN KEY (entity_id) REFERENCES public.entities(id)
);
CREATE TABLE public.entity_details_model (
  entity_id uuid NOT NULL,
  created_at timestamp without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp without time zone NOT NULL,
  license text,
  model_architecture text,
  training_dataset text,
  deployment_options ARRAY,
  frameworks ARRAY,
  id uuid NOT NULL,
  input_data_types ARRAY,
  libraries ARRAY,
  output_data_types ARRAY,
  performance_metrics jsonb,
  target_audience ARRAY,
  use_cases ARRAY,
  CONSTRAINT entity_details_model_pkey PRIMARY KEY (id),
  CONSTRAINT entity_details_model_entity_id_fkey FOREIGN KEY (entity_id) REFERENCES public.entities(id)
);
CREATE TABLE public.entity_details_news (
  entity_id uuid NOT NULL,
  created_at timestamp without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp without time zone NOT NULL,
  article_url text,
  author text,
  publication_date timestamp without time zone,
  source_name text,
  summary text,
  id uuid NOT NULL,
  tags ARRAY,
  CONSTRAINT entity_details_news_pkey PRIMARY KEY (id),
  CONSTRAINT entity_details_news_entity_id_fkey FOREIGN KEY (entity_id) REFERENCES public.entities(id)
);
CREATE TABLE public.entity_details_newsletter (
  entity_id uuid NOT NULL,
  frequency text,
  main_topics jsonb,
  archive_url text,
  subscribe_url text,
  author_name text,
  subscriber_count integer DEFAULT 0,
  id uuid NOT NULL,
  CONSTRAINT entity_details_newsletter_pkey PRIMARY KEY (id),
  CONSTRAINT entity_details_newsletter_entity_id_fkey FOREIGN KEY (entity_id) REFERENCES public.entities(id)
);
CREATE TABLE public.entity_details_platform (
  entity_id uuid NOT NULL,
  created_at timestamp without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp without time zone NOT NULL,
  documentation_url text,
  platform_type text,
  community_url text,
  has_free_tier boolean,
  has_live_chat boolean,
  price_range USER-DEFINED,
  pricing_details text,
  pricing_url text,
  support_email text,
  pricing_model USER-DEFINED,
  api_access boolean,
  customization_level text,
  demo_available boolean,
  deployment_options ARRAY,
  has_api boolean,
  id uuid NOT NULL,
  mobile_support boolean,
  open_source boolean,
  support_channels ARRAY,
  supported_os ARRAY,
  target_audience ARRAY,
  trial_available boolean,
  integrations ARRAY,
  key_services ARRAY,
  use_cases ARRAY,
  CONSTRAINT entity_details_platform_pkey PRIMARY KEY (id),
  CONSTRAINT entity_details_platform_entity_id_fkey FOREIGN KEY (entity_id) REFERENCES public.entities(id)
);
CREATE TABLE public.entity_details_podcast (
  entity_id uuid NOT NULL,
  created_at timestamp without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp without time zone NOT NULL,
  frequency text,
  apple_podcasts_url text,
  average_length text,
  google_podcasts_url text,
  host text,
  id uuid NOT NULL,
  main_topics ARRAY,
  spotify_url text,
  youtube_url text,
  CONSTRAINT entity_details_podcast_pkey PRIMARY KEY (id),
  CONSTRAINT entity_details_podcast_entity_id_fkey FOREIGN KEY (entity_id) REFERENCES public.entities(id)
);
CREATE TABLE public.entity_details_project_reference (
  entity_id uuid NOT NULL,
  created_at timestamp without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp without time zone NOT NULL,
  forks integer,
  id uuid NOT NULL,
  key_technologies ARRAY,
  license text,
  repository_url text,
  stars integer,
  status text,
  use_cases ARRAY,
  contributors integer,
  CONSTRAINT entity_details_project_reference_pkey PRIMARY KEY (id),
  CONSTRAINT entity_details_project_reference_entity_id_fkey FOREIGN KEY (entity_id) REFERENCES public.entities(id)
);
CREATE TABLE public.entity_details_research_paper (
  entity_id uuid NOT NULL,
  created_at timestamp without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp without time zone NOT NULL,
  abstract text,
  citation_count integer,
  doi text,
  journal_or_conference text,
  publication_date timestamp without time zone,
  id uuid NOT NULL,
  pdf_url text,
  authors ARRAY,
  CONSTRAINT entity_details_research_paper_pkey PRIMARY KEY (id),
  CONSTRAINT entity_details_research_paper_entity_id_fkey FOREIGN KEY (entity_id) REFERENCES public.entities(id)
);
CREATE TABLE public.entity_details_service_provider (
  entity_id uuid NOT NULL,
  created_at timestamp without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp without time zone NOT NULL,
  company_size_focus text,
  id uuid NOT NULL,
  industry_specializations ARRAY,
  location_summary text,
  portfolio_url text,
  pricing_info text,
  services_offered ARRAY,
  target_audience ARRAY,
  CONSTRAINT entity_details_service_provider_pkey PRIMARY KEY (id),
  CONSTRAINT entity_details_service_provider_entity_id_fkey FOREIGN KEY (entity_id) REFERENCES public.entities(id)
);
CREATE TABLE public.entity_details_software (
  entity_id uuid NOT NULL,
  created_at timestamp without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp without time zone NOT NULL,
  current_version text,
  license_type text,
  community_url text,
  has_free_tier boolean,
  has_live_chat boolean,
  price_range USER-DEFINED,
  pricing_details text,
  pricing_model USER-DEFINED,
  pricing_url text,
  support_email text,
  api_access boolean,
  customization_level text,
  demo_available boolean,
  deployment_options ARRAY,
  frameworks ARRAY,
  has_api boolean,
  id uuid NOT NULL,
  key_features ARRAY,
  libraries ARRAY,
  mobile_support boolean,
  open_source boolean,
  support_channels ARRAY,
  supported_os ARRAY,
  target_audience ARRAY,
  trial_available boolean,
  integrations ARRAY,
  platform_compatibility ARRAY,
  programming_languages ARRAY,
  use_cases ARRAY,
  CONSTRAINT entity_details_software_pkey PRIMARY KEY (id),
  CONSTRAINT entity_details_software_entity_id_fkey FOREIGN KEY (entity_id) REFERENCES public.entities(id)
);
CREATE TABLE public.entity_details_tool (
  entity_id uuid NOT NULL,
  learning_curve USER-DEFINED,
  target_audience jsonb,
  key_features jsonb,
  use_cases jsonb,
  pricing_model USER-DEFINED,
  price_range USER-DEFINED,
  pricing_details text,
  pricing_url text,
  has_free_tier boolean,
  integrations jsonb,
  api_access boolean,
  community_url text,
  customization_level text,
  demo_available boolean,
  deployment_options jsonb,
  frameworks jsonb,
  has_live_chat boolean,
  libraries jsonb,
  mobile_support boolean,
  open_source boolean,
  programming_languages jsonb,
  support_channels jsonb,
  support_email text,
  supported_os jsonb,
  trial_available boolean,
  has_api boolean,
  id uuid NOT NULL,
  platforms jsonb,
  technical_level USER-DEFINED,
  CONSTRAINT entity_details_tool_pkey PRIMARY KEY (id),
  CONSTRAINT entity_details_tool_entity_id_fkey FOREIGN KEY (entity_id) REFERENCES public.entities(id)
);
CREATE TABLE public.entity_features (
  entity_id uuid NOT NULL,
  feature_id uuid NOT NULL,
  assigned_by uuid NOT NULL,
  created_at timestamp without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
  id uuid NOT NULL,
  CONSTRAINT entity_features_pkey PRIMARY KEY (id),
  CONSTRAINT entity_features_feature_id_fkey FOREIGN KEY (feature_id) REFERENCES public.features(id),
  CONSTRAINT entity_features_entity_id_fkey FOREIGN KEY (entity_id) REFERENCES public.entities(id)
);
CREATE TABLE public.entity_tags (
  entity_id uuid NOT NULL,
  tag_id uuid NOT NULL,
  assigned_by uuid NOT NULL,
  created_at timestamp without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
  id uuid NOT NULL,
  CONSTRAINT entity_tags_pkey PRIMARY KEY (id),
  CONSTRAINT entity_tags_tag_id_fkey FOREIGN KEY (tag_id) REFERENCES public.tags(id),
  CONSTRAINT entity_tags_entity_id_fkey FOREIGN KEY (entity_id) REFERENCES public.entities(id)
);
CREATE TABLE public.entity_types (
  id uuid NOT NULL,
  name text NOT NULL,
  description text,
  slug text NOT NULL,
  icon_url text,
  created_at timestamp without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp without time zone NOT NULL,
  CONSTRAINT entity_types_pkey PRIMARY KEY (id)
);
CREATE TABLE public.features (
  id uuid NOT NULL,
  name text NOT NULL,
  slug text NOT NULL,
  description text,
  created_at timestamp without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp without time zone NOT NULL,
  icon_url text,
  CONSTRAINT features_pkey PRIMARY KEY (id)
);
CREATE TABLE public.profile_activities (
  id uuid NOT NULL,
  user_id uuid NOT NULL,
  type USER-DEFINED NOT NULL,
  description text NOT NULL,
  entity_id uuid,
  entity_name text,
  entity_slug text,
  created_at timestamp without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
  CONSTRAINT profile_activities_pkey PRIMARY KEY (id),
  CONSTRAINT profile_activities_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id),
  CONSTRAINT profile_activities_entity_id_fkey FOREIGN KEY (entity_id) REFERENCES public.entities(id)
);
CREATE TABLE public.review_votes (
  review_id uuid NOT NULL,
  user_id uuid NOT NULL,
  created_at timestamp without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
  id uuid NOT NULL,
  is_upvote boolean NOT NULL,
  CONSTRAINT review_votes_pkey PRIMARY KEY (id),
  CONSTRAINT review_votes_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id),
  CONSTRAINT review_votes_review_id_fkey FOREIGN KEY (review_id) REFERENCES public.reviews(id)
);
CREATE TABLE public.reviews (
  id uuid NOT NULL,
  entity_id uuid NOT NULL,
  user_id uuid NOT NULL,
  rating integer NOT NULL,
  title text,
  status USER-DEFINED NOT NULL DEFAULT 'PENDING'::"ReviewStatus",
  moderation_notes text,
  created_at timestamp without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp without time zone NOT NULL,
  content text,
  downvotes integer NOT NULL DEFAULT 0,
  moderator_id uuid,
  upvotes integer NOT NULL DEFAULT 0,
  CONSTRAINT reviews_pkey PRIMARY KEY (id),
  CONSTRAINT reviews_moderator_id_fkey FOREIGN KEY (moderator_id) REFERENCES public.users(id),
  CONSTRAINT reviews_entity_id_fkey FOREIGN KEY (entity_id) REFERENCES public.entities(id),
  CONSTRAINT reviews_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id)
);
CREATE TABLE public.tags (
  id uuid NOT NULL,
  name text NOT NULL,
  description text,
  slug text NOT NULL,
  created_at timestamp without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp without time zone NOT NULL,
  CONSTRAINT tags_pkey PRIMARY KEY (id)
);
CREATE TABLE public.tool_requests (
  id uuid NOT NULL,
  user_id uuid NOT NULL,
  tool_name text NOT NULL,
  description text NOT NULL,
  reason text NOT NULL,
  category_suggestion text,
  website_url text,
  priority USER-DEFINED NOT NULL DEFAULT 'MEDIUM'::"ToolRequestPriority",
  status USER-DEFINED NOT NULL DEFAULT 'PENDING'::"ToolRequestStatus",
  admin_notes text,
  votes integer NOT NULL DEFAULT 0,
  created_at timestamp without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp without time zone NOT NULL,
  CONSTRAINT tool_requests_pkey PRIMARY KEY (id),
  CONSTRAINT tool_requests_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id)
);
CREATE TABLE public.user_activity_logs (
  id uuid NOT NULL,
  user_id uuid NOT NULL,
  action_type USER-DEFINED NOT NULL,
  entity_id uuid,
  category_id uuid,
  tag_id uuid,
  review_id uuid,
  target_user_id uuid,
  details jsonb,
  created_at timestamp without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
  CONSTRAINT user_activity_logs_pkey PRIMARY KEY (id),
  CONSTRAINT user_activity_logs_target_user_id_fkey FOREIGN KEY (target_user_id) REFERENCES public.users(id),
  CONSTRAINT user_activity_logs_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id),
  CONSTRAINT user_activity_logs_tag_id_fkey FOREIGN KEY (tag_id) REFERENCES public.tags(id),
  CONSTRAINT user_activity_logs_review_id_fkey FOREIGN KEY (review_id) REFERENCES public.reviews(id),
  CONSTRAINT user_activity_logs_entity_id_fkey FOREIGN KEY (entity_id) REFERENCES public.entities(id),
  CONSTRAINT user_activity_logs_category_id_fkey FOREIGN KEY (category_id) REFERENCES public.categories(id)
);
CREATE TABLE public.user_badges (
  id uuid NOT NULL,
  user_id uuid NOT NULL,
  badge_type_id uuid NOT NULL,
  granted_at timestamp without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
  notes text,
  granted_by uuid,
  CONSTRAINT user_badges_pkey PRIMARY KEY (id),
  CONSTRAINT user_badges_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id),
  CONSTRAINT user_badges_granted_by_fkey FOREIGN KEY (granted_by) REFERENCES public.users(id),
  CONSTRAINT user_badges_badge_type_id_fkey FOREIGN KEY (badge_type_id) REFERENCES public.badge_types(id)
);
CREATE TABLE public.user_followed_categories (
  user_id uuid NOT NULL,
  category_id uuid NOT NULL,
  followed_at timestamp without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
  CONSTRAINT user_followed_categories_pkey PRIMARY KEY (user_id, category_id),
  CONSTRAINT user_followed_categories_category_id_fkey FOREIGN KEY (category_id) REFERENCES public.categories(id),
  CONSTRAINT user_followed_categories_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id)
);
CREATE TABLE public.user_followed_tags (
  user_id uuid NOT NULL,
  tag_id uuid NOT NULL,
  created_at timestamp without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
  id uuid NOT NULL,
  CONSTRAINT user_followed_tags_pkey PRIMARY KEY (id),
  CONSTRAINT user_followed_tags_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id),
  CONSTRAINT user_followed_tags_tag_id_fkey FOREIGN KEY (tag_id) REFERENCES public.tags(id)
);
CREATE TABLE public.user_notification_settings (
  user_id uuid NOT NULL,
  email_newsletter boolean NOT NULL DEFAULT true,
  created_at timestamp without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp without time zone NOT NULL,
  email_marketing boolean NOT NULL DEFAULT true,
  email_on_new_entity_in_followed boolean NOT NULL DEFAULT true,
  email_on_new_follower boolean NOT NULL DEFAULT true,
  email_on_new_review boolean NOT NULL DEFAULT true,
  email_on_review_response boolean NOT NULL DEFAULT true,
  id uuid NOT NULL,
  CONSTRAINT user_notification_settings_pkey PRIMARY KEY (id),
  CONSTRAINT user_notification_settings_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id)
);
CREATE TABLE public.user_preferences (
  id uuid NOT NULL,
  user_id uuid NOT NULL,
  email_notifications boolean NOT NULL DEFAULT true,
  marketing_emails boolean NOT NULL DEFAULT false,
  weekly_digest boolean NOT NULL DEFAULT true,
  new_tool_alerts boolean NOT NULL DEFAULT true,
  profile_visibility USER-DEFINED NOT NULL DEFAULT 'PUBLIC'::"ProfileVisibility",
  show_bookmarks boolean NOT NULL DEFAULT true,
  show_reviews boolean NOT NULL DEFAULT true,
  show_activity boolean NOT NULL DEFAULT true,
  theme USER-DEFINED NOT NULL DEFAULT 'LIGHT'::"Theme",
  items_per_page integer NOT NULL DEFAULT 20,
  default_view USER-DEFINED NOT NULL DEFAULT 'GRID'::"DefaultView",
  preferred_categories ARRAY,
  blocked_categories ARRAY,
  content_language text NOT NULL DEFAULT 'en'::text,
  created_at timestamp without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp without time zone NOT NULL,
  CONSTRAINT user_preferences_pkey PRIMARY KEY (id),
  CONSTRAINT user_preferences_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id)
);
CREATE TABLE public.user_saved_entities (
  user_id uuid NOT NULL,
  entity_id uuid NOT NULL,
  created_at timestamp without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
  id uuid NOT NULL,
  CONSTRAINT user_saved_entities_pkey PRIMARY KEY (id),
  CONSTRAINT user_saved_entities_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id),
  CONSTRAINT user_saved_entities_entity_id_fkey FOREIGN KEY (entity_id) REFERENCES public.entities(id)
);
CREATE TABLE public.user_submitted_tools (
  id uuid NOT NULL,
  user_id uuid NOT NULL,
  entity_id uuid NOT NULL,
  submission_status USER-DEFINED NOT NULL DEFAULT 'PENDING'::"SubmissionStatus",
  submitted_at timestamp without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
  reviewed_at timestamp without time zone,
  reviewer_id uuid,
  reviewer_notes text,
  changes_requested text,
  CONSTRAINT user_submitted_tools_pkey PRIMARY KEY (id),
  CONSTRAINT user_submitted_tools_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id),
  CONSTRAINT user_submitted_tools_reviewer_id_fkey FOREIGN KEY (reviewer_id) REFERENCES public.users(id),
  CONSTRAINT user_submitted_tools_entity_id_fkey FOREIGN KEY (entity_id) REFERENCES public.entities(id)
);
CREATE TABLE public.users (
  id uuid NOT NULL,
  auth_user_id uuid NOT NULL,
  username text,
  display_name text,
  email text NOT NULL,
  role USER-DEFINED NOT NULL DEFAULT 'USER'::"UserRole",
  status USER-DEFINED NOT NULL DEFAULT 'ACTIVE'::"UserStatus",
  technical_level USER-DEFINED,
  profile_picture_url text,
  bio text,
  social_links jsonb,
  created_at timestamp without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp without time zone NOT NULL,
  last_login timestamp without time zone,
  bookmarks_count integer NOT NULL DEFAULT 0,
  reputation_score integer NOT NULL DEFAULT 0,
  requests_fulfilled integer NOT NULL DEFAULT 0,
  requests_made integer NOT NULL DEFAULT 0,
  reviews_count integer NOT NULL DEFAULT 0,
  tools_approved integer NOT NULL DEFAULT 0,
  tools_submitted integer NOT NULL DEFAULT 0,
  CONSTRAINT users_pkey PRIMARY KEY (id)
);