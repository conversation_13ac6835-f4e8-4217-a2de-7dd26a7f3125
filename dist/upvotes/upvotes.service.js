"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpvotesService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const prisma_1 = require("generated/prisma");
const logger_service_1 = require("../common/logger/logger.service");
let UpvotesService = class UpvotesService {
    constructor(prisma, logger) {
        this.prisma = prisma;
        this.logger = logger;
    }
    async addUpvote(userId, entityId) {
        const entity = await this.prisma.entity.findUnique({
            where: { id: entityId },
            select: { id: true, name: true },
        });
        if (!entity) {
            throw new common_1.NotFoundException(`Entity with ID "${entityId}" not found.`);
        }
        try {
            const result = await this.prisma.$transaction(async (tx) => {
                const upvote = await tx.userUpvote.create({
                    data: {
                        userId: userId,
                        entityId: entityId,
                    },
                });
                await tx.entity.update({
                    where: { id: entityId },
                    data: {
                        upvoteCount: {
                            increment: 1,
                        },
                    },
                });
                return upvote;
            });
            this.logger.log('Upvote successfully added', {
                operation: 'addUpvote',
                userId,
                entityId,
            });
            return result;
        }
        catch (error) {
            if (error instanceof prisma_1.Prisma.PrismaClientKnownRequestError) {
                switch (error.code) {
                    case 'P2002':
                        this.logger.log('User already upvoted this entity - treating as success', {
                            operation: 'addUpvote',
                            userId,
                            entityId,
                            reason: 'already_upvoted',
                        });
                        const existingUpvote = await this.prisma.userUpvote.findUnique({
                            where: {
                                userId_entityId: {
                                    userId: userId,
                                    entityId: entityId,
                                },
                            },
                        });
                        if (existingUpvote) {
                            return existingUpvote;
                        }
                        throw new common_1.InternalServerErrorException('Could not add the upvote. Please try again later.');
                    case 'P2003':
                        throw new common_1.NotFoundException(`Invalid user ID or entity ID provided.`);
                    default:
                        this.logger.logError(error, {
                            operation: 'addUpvote',
                            userId,
                            entityId,
                            errorCode: error.code,
                            type: 'prisma_unknown_error',
                        });
                        throw new common_1.InternalServerErrorException('Could not add the upvote. Please try again later.');
                }
            }
            throw new common_1.InternalServerErrorException('Could not add the upvote. Please try again later.');
        }
    }
    async removeUpvote(userId, entityId) {
        try {
            await this.prisma.$transaction(async (tx) => {
                await tx.userUpvote.delete({
                    where: {
                        userId_entityId: {
                            userId: userId,
                            entityId: entityId,
                        },
                    },
                });
                await tx.entity.update({
                    where: { id: entityId },
                    data: {
                        upvoteCount: {
                            decrement: 1,
                        },
                    },
                });
            });
            this.logger.log('Upvote successfully removed', {
                operation: 'removeUpvote',
                userId,
                entityId,
            });
        }
        catch (error) {
            if (error instanceof prisma_1.Prisma.PrismaClientKnownRequestError) {
                if (error.code === 'P2025') {
                    this.logger.log('Upvote not found during delete - treating as success', {
                        operation: 'removeUpvote',
                        userId,
                        entityId,
                        reason: 'upvote_not_found',
                    });
                    return;
                }
            }
            this.logger.logError(error, {
                operation: 'removeUpvote',
                userId,
                entityId,
                type: 'upvote_delete_error',
            });
            throw new common_1.InternalServerErrorException('Could not remove the upvote. Please try again later.');
        }
    }
};
exports.UpvotesService = UpvotesService;
exports.UpvotesService = UpvotesService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        logger_service_1.AppLoggerService])
], UpvotesService);
//# sourceMappingURL=upvotes.service.js.map