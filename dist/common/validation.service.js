"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ValidationService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const prisma_1 = require("../../generated/prisma");
let ValidationService = class ValidationService {
    constructor(prismaService) {
        this.prismaService = prismaService;
    }
    async validateToolRequest(userId, toolName) {
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1);
        const todayRequestsCount = await this.prismaService.toolRequest.count({
            where: {
                userId,
                createdAt: {
                    gte: today,
                    lt: tomorrow,
                },
            },
        });
        if (todayRequestsCount >= 5) {
            throw new common_1.BadRequestException('You have reached the daily limit of 5 tool requests. Please try again tomorrow.');
        }
        const existingRequest = await this.prismaService.toolRequest.findFirst({
            where: {
                userId,
                toolName: {
                    equals: toolName,
                    mode: 'insensitive',
                },
                status: {
                    in: ['PENDING', 'UNDER_REVIEW', 'APPROVED'],
                },
            },
        });
        if (existingRequest) {
            throw new common_1.BadRequestException('You have already requested this tool. Please wait for the existing request to be processed.');
        }
    }
    validateUserPreferences(preferences) {
        if (preferences.itemsPerPage !== undefined) {
            const validValues = [10, 20, 50, 100];
            if (!validValues.includes(preferences.itemsPerPage)) {
                throw new common_1.BadRequestException(`Items per page must be one of: ${validValues.join(', ')}`);
            }
        }
        if (preferences.preferredCategories && preferences.preferredCategories.length > 20) {
            throw new common_1.BadRequestException('You can select a maximum of 20 preferred categories.');
        }
        if (preferences.blockedCategories && preferences.blockedCategories.length > 50) {
            throw new common_1.BadRequestException('You can block a maximum of 50 categories.');
        }
        if (preferences.contentLanguage && !/^[a-z]{2}(-[A-Z]{2})?$/.test(preferences.contentLanguage)) {
            throw new common_1.BadRequestException('Content language must be a valid language code (e.g., "en", "en-US").');
        }
    }
    async validateProfileUpdate(userId, profileData) {
        if (profileData.username) {
            if (!/^[a-zA-Z0-9_]{3,30}$/.test(profileData.username)) {
                throw new common_1.BadRequestException('Username must be 3-30 characters long and contain only letters, numbers, and underscores.');
            }
            const existingUser = await this.prismaService.user.findFirst({
                where: {
                    username: {
                        equals: profileData.username,
                        mode: 'insensitive',
                    },
                    id: {
                        not: userId,
                    },
                },
            });
            if (existingUser) {
                throw new common_1.BadRequestException('This username is already taken. Please choose a different one.');
            }
        }
        if (profileData.displayName && profileData.displayName.length > 100) {
            throw new common_1.BadRequestException('Display name cannot exceed 100 characters.');
        }
        if (profileData.bio && profileData.bio.length > 500) {
            throw new common_1.BadRequestException('Bio cannot exceed 500 characters.');
        }
        if (profileData.socialLinks) {
            const validDomains = {
                website: null,
                twitter: ['twitter.com', 'x.com'],
                linkedin: ['linkedin.com'],
                github: ['github.com'],
            };
            for (const [platform, url] of Object.entries(profileData.socialLinks)) {
                if (url && typeof url === 'string') {
                    try {
                        const urlObj = new URL(url);
                        const allowedDomains = validDomains[platform];
                        if (allowedDomains && !allowedDomains.some(domain => urlObj.hostname.includes(domain))) {
                            throw new common_1.BadRequestException(`${platform} URL must be from ${allowedDomains.join(' or ')}`);
                        }
                    }
                    catch {
                        throw new common_1.BadRequestException(`Invalid URL format for ${platform}`);
                    }
                }
            }
        }
    }
    async validateEntitySubmissionRate(userId) {
        const user = await this.prismaService.user.findUnique({
            where: { id: userId },
            select: { role: true },
        });
        if (!user) {
            throw new common_1.BadRequestException('User not found.');
        }
        if (user.role === 'ADMIN') {
            return;
        }
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1);
        const todaySubmissionsCount = await this.prismaService.userSubmittedTool.count({
            where: {
                userId,
                submittedAt: {
                    gte: today,
                    lt: tomorrow,
                },
            },
        });
        const dailyLimit = user.role === 'MODERATOR' ? 20 : 3;
        if (todaySubmissionsCount >= dailyLimit) {
            throw new common_1.BadRequestException(`You have reached the daily limit of ${dailyLimit} entity submissions. Please try again tomorrow.`);
        }
    }
    async validateReviewSubmission(userId, entityId) {
        const existingReview = await this.prismaService.review.findUnique({
            where: {
                entityId_userId: {
                    entityId,
                    userId,
                },
            },
        });
        if (existingReview) {
            throw new common_1.BadRequestException('You have already reviewed this entity. You can update your existing review instead.');
        }
        const entity = await this.prismaService.entity.findUnique({
            where: { id: entityId },
            select: { submitterId: true },
        });
        if (entity?.submitterId === userId) {
            throw new common_1.BadRequestException('You cannot review an entity that you submitted.');
        }
    }
    async validateBookmarkAction(userId, entityId) {
        const entity = await this.prismaService.entity.findUnique({
            where: { id: entityId },
            select: { status: true },
        });
        if (!entity) {
            throw new common_1.BadRequestException('Entity not found.');
        }
        if (entity.status !== prisma_1.EntityStatus.ACTIVE) {
            throw new common_1.BadRequestException('You can only bookmark active entities.');
        }
    }
};
exports.ValidationService = ValidationService;
exports.ValidationService = ValidationService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], ValidationService);
//# sourceMappingURL=validation.service.js.map