import { ConfigService } from '@nestjs/config';
import { ILlmService, LlmRecommendation, CandidateEntity, ChatResponse, ConversationContext, UserIntent } from '../interfaces/llm.service.interface';
export declare class AnthropicLlmService implements ILlmService {
    private readonly configService;
    private readonly logger;
    private readonly apiKey;
    private readonly apiUrl;
    constructor(configService: ConfigService);
    getRecommendation(problemDescription: string, candidateEntities: CandidateEntity[]): Promise<LlmRecommendation>;
    private callAnthropicAPI;
    private buildRecommendationPrompt;
    private parseAnthropicResponse;
    private getFallbackRecommendation;
    getChatResponse(userMessage: string, context: ConversationContext, candidateEntities?: CandidateEntity[]): Promise<ChatResponse>;
    classifyIntent(userMessage: string, context: ConversationContext): Promise<UserIntent>;
    generateFollowUpQuestions(context: ConversationContext): Promise<string[]>;
    shouldTransitionToRecommendations(context: ConversationContext): Promise<{
        shouldTransition: boolean;
        reason: string;
    }>;
    private buildChatPrompt;
    private buildIntentClassificationPrompt;
    private buildFollowUpPrompt;
    private buildTransitionPrompt;
    private parseChatResponse;
    private parseIntentResponse;
    private parseFollowUpResponse;
    private parseTransitionResponse;
    private formatEntitiesForChat;
    private formatUserProfile;
    private getFallbackChatResponse;
    private getFallbackIntent;
    private getFallbackFollowUpQuestions;
}
