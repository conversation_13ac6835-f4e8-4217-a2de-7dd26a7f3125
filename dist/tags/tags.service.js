"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var TagsService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.TagsService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const prisma_1 = require("generated/prisma");
const slug_utils_1 = require("../utils/slug.utils");
const common_2 = require("@nestjs/common");
let TagsService = TagsService_1 = class TagsService {
    constructor(prisma) {
        this.prisma = prisma;
        this.logger = new common_2.Logger(TagsService_1.name);
    }
    async create(createTagDto) {
        const slug = createTagDto.slug || (0, slug_utils_1.generateSlug)(createTagDto.name);
        try {
            const tag = await this.prisma.tag.create({
                data: {
                    name: createTagDto.name,
                    slug: slug,
                    description: createTagDto.description,
                },
            });
            return tag;
        }
        catch (error) {
            this.logger.error(`Caught error during TagsService.create. Error type: ${error?.constructor?.name}, Name: ${error?.name}, Message: ${error?.message}`);
            if (error && typeof error === 'object') {
                if ('code' in error && typeof error.code === 'string' && error.code.startsWith('P')) {
                    this.logger.warn(`Re-throwing PrismaClientKnownRequestError from TagsService (Code: ${error.code}) for global filter. Target: ${error.meta?.target}`);
                    throw error;
                }
                else if (error.name === 'PrismaClientValidationError') {
                    this.logger.warn(`Re-throwing PrismaClientValidationError from TagsService for global filter.`);
                    throw error;
                }
            }
            this.logger.error(`Unexpected error in TagsService.create for tag '${createTagDto.name}'. Not identified as Prisma error for re-throw. Original error: ${error?.message}`, error?.stack);
            throw new common_1.InternalServerErrorException('Could not create tag due to an unexpected server issue.');
        }
    }
    async findAll(page = 1, limit = 10) {
        const skip = (page - 1) * limit;
        try {
            const [tags, totalCount] = await this.prisma.$transaction([
                this.prisma.tag.findMany({
                    skip,
                    take: limit,
                    orderBy: {
                        name: 'asc',
                    },
                }),
                this.prisma.tag.count(),
            ]);
            return {
                data: tags,
                count: totalCount,
                totalPages: Math.ceil(totalCount / limit),
                currentPage: page,
            };
        }
        catch (error) {
            console.error('Error fetching all tags:', error);
            throw new common_1.InternalServerErrorException('Could not fetch tags.');
        }
    }
    async findAllPublic() {
        try {
            return await this.prisma.tag.findMany({
                orderBy: {
                    name: 'asc',
                },
            });
        }
        catch (error) {
            this.logger.error('Error fetching all public tags:', error.stack);
            throw new common_1.InternalServerErrorException('Could not fetch public tags.');
        }
    }
    async findOne(id) {
        const tag = await this.prisma.tag.findUnique({
            where: { id },
        });
        if (!tag) {
            throw new common_1.NotFoundException(`Tag with ID "${id}" not found`);
        }
        return tag;
    }
    async update(id, updateTagDto) {
        await this.findOne(id);
        const dataToUpdate = {};
        if (updateTagDto.name !== undefined)
            dataToUpdate.name = updateTagDto.name;
        if (updateTagDto.description !== undefined)
            dataToUpdate.description = updateTagDto.description;
        if (updateTagDto.slug) {
            dataToUpdate.slug = (0, slug_utils_1.generateSlug)(updateTagDto.slug);
        }
        else if (updateTagDto.name) {
            dataToUpdate.slug = (0, slug_utils_1.generateSlug)(updateTagDto.name);
        }
        try {
            return await this.prisma.tag.update({
                where: { id },
                data: dataToUpdate,
            });
        }
        catch (error) {
            if (error instanceof prisma_1.Prisma.PrismaClientKnownRequestError) {
                if (error.code === 'P2002') {
                    throw new common_1.ConflictException(`Tag with this name or slug already exists.`);
                }
                if (error.code === 'P2025') {
                    throw new common_1.NotFoundException(`Tag with ID "${id}" not found to update.`);
                }
            }
            console.error(`Error updating tag ${id}:`, error);
            throw new common_1.InternalServerErrorException('Could not update tag.');
        }
    }
    async remove(id) {
        await this.findOne(id);
        try {
            return await this.prisma.tag.delete({
                where: { id },
            });
        }
        catch (error) {
            if (error instanceof prisma_1.Prisma.PrismaClientKnownRequestError) {
                if (error.code === 'P2025') {
                    throw new common_1.NotFoundException(`Tag with ID "${id}" not found to delete.`);
                }
            }
            console.error(`Error removing tag ${id}:`, error);
            throw new common_1.InternalServerErrorException('Could not delete tag.');
        }
    }
};
exports.TagsService = TagsService;
exports.TagsService = TagsService = TagsService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], TagsService);
//# sourceMappingURL=tags.service.js.map