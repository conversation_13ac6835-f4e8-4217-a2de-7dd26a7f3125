"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChatController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const throttler_1 = require("@nestjs/throttler");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const get_req_user_decorator_1 = require("../auth/decorators/get-req-user.decorator");
const chat_service_1 = require("./services/chat.service");
const question_suggestion_service_1 = require("./services/question-suggestion.service");
const send_chat_message_dto_1 = require("./dto/send-chat-message.dto");
const chat_response_dto_1 = require("./dto/chat-response.dto");
const conversation_history_response_dto_1 = require("./dto/conversation-history-response.dto");
const get_conversation_history_dto_1 = require("./dto/get-conversation-history.dto");
let ChatController = class ChatController {
    constructor(chatService, questionSuggestion) {
        this.chatService = chatService;
        this.questionSuggestion = questionSuggestion;
    }
    async sendMessage(sendChatMessageDto, reqUser) {
        try {
            if (!sendChatMessageDto.message?.trim()) {
                throw new common_1.BadRequestException('Message cannot be empty');
            }
            return await this.chatService.sendMessage(reqUser.authData.sub, sendChatMessageDto);
        }
        catch (error) {
            if (error instanceof common_1.BadRequestException) {
                throw error;
            }
            console.error('Chat service error:', error);
            throw new common_1.InternalServerErrorException('Unable to process chat message at this time');
        }
    }
    async getConversationHistory(sessionId, getHistoryDto, reqUser) {
        try {
            if (!sessionId?.trim()) {
                throw new common_1.BadRequestException('Session ID is required');
            }
            return await this.chatService.getConversationHistory(reqUser.authData.sub, sessionId, getHistoryDto);
        }
        catch (error) {
            if (error.message?.includes('does not belong to the user')) {
                throw new common_1.ForbiddenException('Access denied to this conversation');
            }
            if (error.message?.includes('not found')) {
                throw new common_1.NotFoundException('Conversation not found');
            }
            if (error instanceof common_1.BadRequestException ||
                error instanceof common_1.ForbiddenException ||
                error instanceof common_1.NotFoundException) {
                throw error;
            }
            console.error('Error getting conversation history:', error);
            throw new common_1.InternalServerErrorException('Unable to retrieve conversation history');
        }
    }
    async endConversation(sessionId, reqUser) {
        return this.chatService.endConversation(reqUser.authData.sub, sessionId);
    }
    async getUserActiveSessions(reqUser) {
        const sessions = await this.chatService.getUserActiveSessions(reqUser.authData.sub);
        return {
            sessions,
            total: sessions.length,
        };
    }
    async getPerformanceMetrics(reqUser) {
        const metrics = this.chatService.getPerformanceMetrics();
        const health = this.chatService.getPerformanceHealth();
        const cacheStats = this.chatService.getCacheStats();
        return {
            ...metrics,
            health,
            cache: cacheStats,
            timestamp: new Date().toISOString(),
        };
    }
    async clearCaches(reqUser) {
        this.chatService.clearCaches();
    }
    async getQuestionSuggestions(user, sessionId, message) {
        try {
            const mockContext = {
                sessionId,
                userId: user.authData.sub,
                messages: message ? [{
                        id: `msg_${Date.now()}`,
                        role: 'user',
                        content: message,
                        timestamp: new Date()
                    }] : [],
                discoveredEntities: [],
                userPreferences: {},
                conversationStage: 'discovery',
                metadata: {
                    startedAt: new Date(),
                    lastActiveAt: new Date(),
                    totalMessages: message ? 1 : 0,
                    entitiesShown: [],
                },
            };
            const questions = this.questionSuggestion.generateFollowUpQuestions(mockContext, message || 'Hello', 3);
            return {
                questions,
                session_id: sessionId,
            };
        }
        catch (error) {
            throw new common_1.InternalServerErrorException('Failed to generate question suggestions');
        }
    }
};
exports.ChatController = ChatController;
__decorate([
    (0, common_1.Post)(),
    (0, throttler_1.Throttle)({ default: { limit: 20, ttl: 60000 } }),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({
        summary: 'Send a message to the AI chatbot',
        description: `
    Send a message to the conversational AI chatbot that helps users discover the perfect AI tools.
    
    This endpoint:
    1. Processes the user's message in the context of their conversation
    2. Uses vector search to find relevant AI tools
    3. Applies the configured LLM provider to generate intelligent responses
    4. Maintains conversation state and context across messages
    5. Provides personalized recommendations based on user preferences
    
    The chatbot can:
    - Help users explore different AI tool categories
    - Ask clarifying questions to understand specific needs
    - Provide detailed explanations about AI tools and their use cases
    - Guide users through the discovery process
    - Transition to formal recommendations when appropriate
    
    Rate limiting: 20 requests per minute per user to ensure optimal performance.
    `,
    }),
    (0, swagger_1.ApiBody)({
        type: send_chat_message_dto_1.SendChatMessageDto,
        description: 'Chat message and optional conversation context',
        examples: {
            'New Conversation': {
                summary: 'Starting a new conversation',
                value: {
                    message: 'Hi! I need help finding an AI tool for automated code documentation.',
                    user_preferences: {
                        budget: 'medium',
                        technical_level: 'intermediate',
                    },
                },
            },
            'Continue Conversation': {
                summary: 'Continuing an existing conversation',
                value: {
                    message: 'I work primarily with Python and JavaScript. Do you have any specific recommendations?',
                    session_id: 'chat_123e4567-e89b-12d3-a456-426614174000',
                    user_preferences: {
                        preferred_categories: ['Developer Tools'],
                    },
                },
            },
            'Refinement': {
                summary: 'Refining requirements',
                value: {
                    message: 'I prefer free tools, and I need something that integrates with VS Code.',
                    session_id: 'chat_123e4567-e89b-12d3-a456-426614174000',
                    user_preferences: {
                        budget: 'free',
                        technical_level: 'intermediate',
                    },
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Chat response generated successfully',
        type: chat_response_dto_1.ChatResponseDto,
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: 'Invalid request data or malformed message',
    }),
    (0, swagger_1.ApiResponse)({
        status: 401,
        description: 'Authentication required - valid JWT token must be provided',
    }),
    (0, swagger_1.ApiResponse)({
        status: 429,
        description: 'Rate limit exceeded - too many chat requests',
    }),
    (0, swagger_1.ApiResponse)({
        status: 500,
        description: 'Internal server error during chat processing',
    }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, get_req_user_decorator_1.GetReqUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [send_chat_message_dto_1.SendChatMessageDto, Object]),
    __metadata("design:returntype", Promise)
], ChatController.prototype, "sendMessage", null);
__decorate([
    (0, common_1.Get)(':sessionId/history'),
    (0, throttler_1.Throttle)({ default: { limit: 30, ttl: 60000 } }),
    (0, swagger_1.ApiOperation)({
        summary: 'Get conversation history',
        description: `
    Retrieve the conversation history for a specific chat session.
    
    This endpoint returns:
    - All messages in the conversation (up to the specified limit)
    - Current conversation stage and metadata
    - Information about discovered AI tools
    - Conversation timestamps and statistics
    
    Use this to:
    - Resume conversations after page refreshes
    - Display conversation history to users
    - Analyze conversation patterns and effectiveness
    - Debug conversation flow issues
    `,
    }),
    (0, swagger_1.ApiParam)({
        name: 'sessionId',
        description: 'Unique session identifier for the conversation',
        example: 'chat_123e4567-e89b-12d3-a456-426614174000',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'limit',
        description: 'Maximum number of messages to return (1-100)',
        example: 20,
        required: false,
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Conversation history retrieved successfully',
        type: conversation_history_response_dto_1.ConversationHistoryResponseDto,
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: 'Invalid session ID or limit parameter',
    }),
    (0, swagger_1.ApiResponse)({
        status: 401,
        description: 'Authentication required',
    }),
    (0, swagger_1.ApiResponse)({
        status: 403,
        description: 'Session does not belong to the authenticated user',
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Conversation session not found',
    }),
    (0, swagger_1.ApiResponse)({
        status: 429,
        description: 'Rate limit exceeded',
    }),
    __param(0, (0, common_1.Param)('sessionId')),
    __param(1, (0, common_1.Query)()),
    __param(2, (0, get_req_user_decorator_1.GetReqUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, get_conversation_history_dto_1.GetConversationHistoryDto, Object]),
    __metadata("design:returntype", Promise)
], ChatController.prototype, "getConversationHistory", null);
__decorate([
    (0, common_1.Delete)(':sessionId'),
    (0, throttler_1.Throttle)({ default: { limit: 10, ttl: 60000 } }),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    (0, swagger_1.ApiOperation)({
        summary: 'End a conversation session',
        description: `
    Permanently end a conversation session and clean up associated data.
    
    This action:
    - Removes the conversation from active memory/storage
    - Clears all conversation state and history
    - Cannot be undone - the conversation will be permanently lost
    
    Use this when:
    - Users explicitly want to end a conversation
    - Cleaning up old or abandoned sessions
    - Implementing privacy features (data deletion)
    - Managing storage/memory usage
    
    Note: This is a destructive operation and cannot be reversed.
    `,
    }),
    (0, swagger_1.ApiParam)({
        name: 'sessionId',
        description: 'Unique session identifier for the conversation to end',
        example: 'chat_123e4567-e89b-12d3-a456-426614174000',
    }),
    (0, swagger_1.ApiResponse)({
        status: 204,
        description: 'Conversation session ended successfully',
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: 'Invalid session ID format',
    }),
    (0, swagger_1.ApiResponse)({
        status: 401,
        description: 'Authentication required',
    }),
    (0, swagger_1.ApiResponse)({
        status: 403,
        description: 'Session does not belong to the authenticated user',
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Conversation session not found',
    }),
    (0, swagger_1.ApiResponse)({
        status: 429,
        description: 'Rate limit exceeded',
    }),
    __param(0, (0, common_1.Param)('sessionId')),
    __param(1, (0, get_req_user_decorator_1.GetReqUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], ChatController.prototype, "endConversation", null);
__decorate([
    (0, common_1.Get)('sessions'),
    (0, throttler_1.Throttle)({ default: { limit: 10, ttl: 60000 } }),
    (0, swagger_1.ApiOperation)({
        summary: 'Get user active chat sessions',
        description: `
    Retrieve all active chat sessions for the authenticated user.
    
    This endpoint returns:
    - List of session IDs for active conversations
    - Useful for displaying ongoing conversations to users
    - Helps with session management and cleanup
    
    Use this to:
    - Show users their active conversations
    - Implement conversation switching in the UI
    - Monitor user engagement with the chatbot
    - Manage session limits per user
    `,
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Active sessions retrieved successfully',
        schema: {
            type: 'object',
            properties: {
                sessions: {
                    type: 'array',
                    items: { type: 'string' },
                    example: [
                        'chat_123e4567-e89b-12d3-a456-426614174000',
                        'chat_987fcdeb-51a2-43d7-8f9e-123456789abc',
                    ],
                },
                total: {
                    type: 'number',
                    example: 2,
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 401,
        description: 'Authentication required',
    }),
    (0, swagger_1.ApiResponse)({
        status: 429,
        description: 'Rate limit exceeded',
    }),
    __param(0, (0, get_req_user_decorator_1.GetReqUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ChatController.prototype, "getUserActiveSessions", null);
__decorate([
    (0, common_1.Get)('admin/metrics'),
    (0, throttler_1.Throttle)({ default: { limit: 5, ttl: 60000 } }),
    (0, swagger_1.ApiOperation)({
        summary: 'Get chat performance metrics (Admin only)',
        description: `
    Retrieve comprehensive performance metrics for the chat system.

    This endpoint provides:
    - Request statistics (total, success rate, error rate)
    - Response time metrics (average, percentiles)
    - Session statistics (active, peak concurrent)
    - LLM provider usage distribution
    - Conversation stage distribution
    - Error type breakdown
    - Performance thresholds and health status

    **Admin Access Required**: This endpoint should be restricted to administrators only.
    `,
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Performance metrics retrieved successfully',
        schema: {
            type: 'object',
            properties: {
                requests: {
                    type: 'object',
                    properties: {
                        total: { type: 'number' },
                        successful: { type: 'number' },
                        failed: { type: 'number' },
                        successRate: { type: 'number' },
                    },
                },
                performance: {
                    type: 'object',
                    properties: {
                        averageResponseTime: { type: 'number' },
                        medianResponseTime: { type: 'number' },
                        p95ResponseTime: { type: 'number' },
                        p99ResponseTime: { type: 'number' },
                    },
                },
                sessions: {
                    type: 'object',
                    properties: {
                        currentActive: { type: 'number' },
                        peakConcurrent: { type: 'number' },
                    },
                },
                health: {
                    type: 'object',
                    properties: {
                        status: { type: 'string', enum: ['healthy', 'warning', 'critical'] },
                        issues: { type: 'array', items: { type: 'string' } },
                        recommendations: { type: 'array', items: { type: 'string' } },
                    },
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 401,
        description: 'Authentication required',
    }),
    (0, swagger_1.ApiResponse)({
        status: 403,
        description: 'Admin access required',
    }),
    (0, swagger_1.ApiResponse)({
        status: 429,
        description: 'Rate limit exceeded',
    }),
    __param(0, (0, get_req_user_decorator_1.GetReqUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ChatController.prototype, "getPerformanceMetrics", null);
__decorate([
    (0, common_1.Post)('admin/cache/clear'),
    (0, throttler_1.Throttle)({ default: { limit: 2, ttl: 60000 } }),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    (0, swagger_1.ApiOperation)({
        summary: 'Clear chat caches (Admin only)',
        description: `
    Clear all chat-related caches to force fresh data retrieval.

    This operation:
    - Clears intent classification cache
    - Clears follow-up questions cache
    - Clears entity search cache
    - Clears response cache

    Use this when:
    - Deploying new AI models or prompts
    - Debugging cache-related issues
    - Forcing fresh responses for testing

    **Admin Access Required**: This is a destructive operation that affects all users.
    `,
    }),
    (0, swagger_1.ApiResponse)({
        status: 204,
        description: 'Caches cleared successfully',
    }),
    (0, swagger_1.ApiResponse)({
        status: 401,
        description: 'Authentication required',
    }),
    (0, swagger_1.ApiResponse)({
        status: 403,
        description: 'Admin access required',
    }),
    (0, swagger_1.ApiResponse)({
        status: 429,
        description: 'Rate limit exceeded',
    }),
    __param(0, (0, get_req_user_decorator_1.GetReqUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ChatController.prototype, "clearCaches", null);
__decorate([
    (0, common_1.Get)('suggestions/:sessionId'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get contextual question suggestions',
        description: 'Get contextual follow-up questions for a conversation session',
    }),
    (0, swagger_1.ApiParam)({
        name: 'sessionId',
        description: 'Chat session ID',
        example: 'chat_123e4567-e89b-12d3-a456-426614174000',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'message',
        description: 'Current user message for context',
        required: false,
        example: 'I need help finding AI tools',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Question suggestions generated successfully',
        schema: {
            type: 'object',
            properties: {
                questions: {
                    type: 'array',
                    items: { type: 'string' },
                    example: [
                        'What industry do you work in?',
                        'What specific task are you trying to accomplish?',
                    ],
                },
                session_id: {
                    type: 'string',
                    example: 'chat_123e4567-e89b-12d3-a456-426614174000',
                },
            },
        },
    }),
    __param(0, (0, get_req_user_decorator_1.GetReqUser)()),
    __param(1, (0, common_1.Param)('sessionId')),
    __param(2, (0, common_1.Query)('message')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, String]),
    __metadata("design:returntype", Promise)
], ChatController.prototype, "getQuestionSuggestions", null);
exports.ChatController = ChatController = __decorate([
    (0, swagger_1.ApiTags)('Chat'),
    (0, common_1.Controller)('chat'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [chat_service_1.ChatService,
        question_suggestion_service_1.QuestionSuggestionService])
], ChatController);
//# sourceMappingURL=chat.controller.js.map