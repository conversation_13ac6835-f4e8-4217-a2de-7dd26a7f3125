"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var QuestionSuggestionService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.QuestionSuggestionService = void 0;
const common_1 = require("@nestjs/common");
let QuestionSuggestionService = QuestionSuggestionService_1 = class QuestionSuggestionService {
    constructor() {
        this.logger = new common_1.Logger(QuestionSuggestionService_1.name);
    }
    generateFollowUpQuestions(context, userMessage, maxQuestions = 2) {
        try {
            const questions = [];
            const messageCount = context.messages?.length || 0;
            const userPrefs = context.userPreferences || {};
            const previousMessages = this.extractPreviousTopics(context.messages || []);
            const hasDiscussedCoding = this.hasDiscussedTopic(previousMessages, ['code', 'coding', 'programming', 'development']);
            const hasDiscussedIndustry = this.hasDiscussedTopic(previousMessages, ['work', 'industry', 'business', 'company']);
            const hasDiscussedSpecifics = this.hasDiscussedTopic(previousMessages, ['specific', 'particular', 'exactly', 'precisely']);
            if (userMessage.toLowerCase().includes('code') || userMessage.toLowerCase().includes('coding')) {
                if (!hasDiscussedSpecifics) {
                    questions.push("What programming language are you primarily working with?");
                    questions.push("Are you looking for tools to help with debugging, testing, or code generation?");
                }
            }
            else if (userMessage.toLowerCase().includes('help') && !hasDiscussedSpecifics) {
                questions.push("What specific challenge are you trying to solve?");
                questions.push("What type of work or project is this for?");
            }
            else if (messageCount > 3 && !hasDiscussedSpecifics) {
                questions.push("What would be the ideal outcome for you?");
                questions.push("Are there any specific features that are must-haves?");
            }
            if (!hasDiscussedIndustry && messageCount <= 4) {
                questions.push("What industry or field are you working in?");
            }
            if (questions.length === 0) {
                if (messageCount <= 2) {
                    questions.push(...this.getInitialQuestions(userMessage, userPrefs));
                }
                else if (messageCount <= 6) {
                    questions.push(...this.getExplorationQuestions(userMessage, userPrefs));
                }
                else {
                    questions.push(...this.getRefinementQuestions(userMessage, userPrefs));
                }
            }
            return this.filterAndRankQuestions(questions, context, maxQuestions);
        }
        catch (error) {
            this.logger.error('Error generating follow-up questions', error);
            return [];
        }
    }
    getInitialQuestions(userMessage, userPrefs) {
        const questions = [];
        const message = userMessage.toLowerCase();
        if (!userPrefs.industry && !userPrefs.work_context) {
            if (message.includes('work') || message.includes('business') || message.includes('company')) {
                questions.push("What industry do you work in?");
            }
            else {
                questions.push("What kind of work or projects are you looking to enhance with AI tools?");
            }
        }
        if (message.includes('help') || message.includes('need') || message.includes('looking')) {
            questions.push("What specific task or challenge are you trying to solve?");
        }
        if (!userPrefs.technical_level) {
            questions.push("How comfortable are you with learning new AI tools?");
        }
        return questions;
    }
    getExplorationQuestions(userMessage, userPrefs) {
        const questions = [];
        const message = userMessage.toLowerCase();
        if (userPrefs.industry === 'education' || userPrefs.work_context === 'education') {
            questions.push("Are you looking for tools to help with lesson planning, student engagement, or assessment?");
        }
        else if (userPrefs.industry === 'marketing') {
            questions.push("Are you focusing on content creation, analytics, or customer engagement?");
        }
        else if (userPrefs.industry === 'healthcare') {
            questions.push("Are you looking for tools for patient care, research, or administrative tasks?");
        }
        if (!userPrefs.budget) {
            questions.push("Do you have a budget in mind, or are you open to both free and paid options?");
        }
        if (!userPrefs.team_size) {
            questions.push("Will this be for personal use, or do you need something that works for a team?");
        }
        if (message.includes('video') || message.includes('image') || message.includes('content')) {
            questions.push("What type of content are you primarily working with?");
        }
        return questions;
    }
    getRefinementQuestions(userMessage, userPrefs) {
        const questions = [];
        questions.push("Do you need the tool to integrate with any existing software you're already using?");
        questions.push("How soon are you looking to implement this solution?");
        questions.push("Are there any features or limitations that would be deal-breakers for you?");
        questions.push("What's the most important feature you need this tool to have?");
        return questions;
    }
    filterAndRankQuestions(questions, context, maxQuestions) {
        const uniqueQuestions = [...new Set(questions)];
        const recentMessages = context.messages?.slice(-4) || [];
        const recentContent = recentMessages.map(m => m.content.toLowerCase()).join(' ');
        const rankedQuestions = uniqueQuestions
            .map(question => ({
            question,
            relevance: this.calculateRelevance(question, recentContent),
        }))
            .sort((a, b) => b.relevance - a.relevance)
            .slice(0, maxQuestions)
            .map(item => item.question);
        return rankedQuestions;
    }
    calculateRelevance(question, recentContent) {
        let score = 1.0;
        const questionWords = question.toLowerCase().split(' ');
        const keyWords = questionWords.filter(word => word.length > 3 && !['what', 'how', 'are', 'you', 'the', 'for', 'with'].includes(word));
        for (const word of keyWords) {
            if (recentContent.includes(word)) {
                score -= 0.3;
            }
        }
        return Math.max(score, 0.1);
    }
    isQuestionCategoryRecent(context, category) {
        const recentMessages = context.messages?.slice(-6) || [];
        const recentContent = recentMessages.map(m => m.content.toLowerCase()).join(' ');
        const categoryKeywords = {
            industry: ['industry', 'work', 'business', 'company'],
            budget: ['budget', 'cost', 'price', 'free', 'paid'],
            team: ['team', 'personal', 'individual', 'group'],
            technical: ['comfortable', 'experience', 'technical', 'beginner'],
            timeline: ['soon', 'timeline', 'when', 'deadline'],
            integration: ['integrate', 'software', 'existing', 'api'],
        };
        const keywords = categoryKeywords[category] || [];
        return keywords.some((keyword) => recentContent.includes(keyword));
    }
    generateTopicQuestions(topic, context) {
        const topicQuestions = {
            budget: [
                "What's your budget range for AI tools?",
                "Are you looking for free options, or are you open to paid solutions?",
                "Do you need enterprise-level features, or would basic plans work?",
            ],
            industry: [
                "What industry do you work in?",
                "What type of business or organization are you with?",
                "What's your primary work focus?",
            ],
            technical_level: [
                "How comfortable are you with learning new technology?",
                "Do you prefer simple, user-friendly tools or are you okay with more complex solutions?",
                "What's your experience level with AI tools?",
            ],
            team_size: [
                "Is this for personal use or for a team?",
                "How many people would be using this tool?",
                "Do you need collaboration features?",
            ],
            use_case: [
                "What specific task are you trying to accomplish?",
                "What's the main challenge you're facing?",
                "What would success look like for you?",
            ],
        };
        return topicQuestions[topic] || [];
    }
    extractPreviousTopics(messages) {
        const topics = [];
        messages.forEach(message => {
            if (message.content && typeof message.content === 'string') {
                topics.push(message.content.toLowerCase());
            }
        });
        return topics;
    }
    hasDiscussedTopic(previousMessages, keywords) {
        const allText = previousMessages.join(' ').toLowerCase();
        return keywords.some(keyword => allText.includes(keyword));
    }
};
exports.QuestionSuggestionService = QuestionSuggestionService;
exports.QuestionSuggestionService = QuestionSuggestionService = QuestionSuggestionService_1 = __decorate([
    (0, common_1.Injectable)()
], QuestionSuggestionService);
//# sourceMappingURL=question-suggestion.service.js.map