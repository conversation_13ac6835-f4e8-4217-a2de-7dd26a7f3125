"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var ConversationManagerService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConversationManagerService = void 0;
const common_1 = require("@nestjs/common");
const uuid_1 = require("uuid");
let ConversationManagerService = ConversationManagerService_1 = class ConversationManagerService {
    constructor(conversationStateService) {
        this.conversationStateService = conversationStateService;
        this.logger = new common_1.Logger(ConversationManagerService_1.name);
    }
    async initializeConversation(userId, sessionId) {
        const finalSessionId = sessionId || this.generateSessionId();
        const context = {
            sessionId: finalSessionId,
            userId,
            messages: [],
            discoveredEntities: [],
            userPreferences: {},
            conversationStage: 'greeting',
            metadata: {
                startedAt: new Date(),
                lastActiveAt: new Date(),
                totalMessages: 0,
                entitiesShown: [],
            },
        };
        await this.conversationStateService.setConversationContext(finalSessionId, context);
        this.logger.log(`Initialized new conversation session ${finalSessionId} for user ${userId}`);
        return context;
    }
    async getOrCreateConversation(userId, sessionId) {
        if (sessionId) {
            const existingContext = await this.conversationStateService.getConversationContext(sessionId);
            if (existingContext) {
                if (existingContext.userId !== userId) {
                    this.logger.warn(`Session ${sessionId} belongs to different user. Creating new session.`);
                    return this.initializeConversation(userId);
                }
                this.logger.debug(`Retrieved existing conversation session ${sessionId}`);
                return existingContext;
            }
        }
        return this.initializeConversation(userId, sessionId);
    }
    async addMessage(sessionId, message, intent) {
        const context = await this.conversationStateService.getConversationContext(sessionId);
        if (!context) {
            throw new Error(`Conversation session ${sessionId} not found`);
        }
        const chatMessage = {
            id: (0, uuid_1.v4)(),
            timestamp: new Date(),
            ...message,
        };
        context.messages.push(chatMessage);
        context.metadata.lastActiveAt = new Date();
        context.metadata.totalMessages = context.messages.length;
        if (intent) {
            context.currentIntent = intent;
        }
        this.autoUpdateConversationStage(context, intent);
        await this.conversationStateService.setConversationContext(sessionId, context);
        this.logger.debug(`Added ${message.role} message to session ${sessionId}. Total messages: ${context.messages.length}`);
        return context;
    }
    async updateUserPreferences(sessionId, preferences) {
        const context = await this.conversationStateService.getConversationContext(sessionId);
        if (!context) {
            throw new Error(`Conversation session ${sessionId} not found`);
        }
        context.userPreferences = {
            ...context.userPreferences,
            ...preferences,
        };
        context.metadata.lastActiveAt = new Date();
        await this.conversationStateService.setConversationContext(sessionId, context);
        this.logger.debug(`Updated user preferences for session ${sessionId}`);
        return context;
    }
    async addDiscoveredEntities(sessionId, entityIds) {
        const context = await this.conversationStateService.getConversationContext(sessionId);
        if (!context) {
            throw new Error(`Conversation session ${sessionId} not found`);
        }
        const newEntities = entityIds.filter(id => !context.discoveredEntities.includes(id));
        context.discoveredEntities.push(...newEntities);
        context.metadata.entitiesShown.push(...newEntities);
        context.metadata.lastActiveAt = new Date();
        await this.conversationStateService.setConversationContext(sessionId, context);
        this.logger.debug(`Added ${newEntities.length} new entities to session ${sessionId}. Total: ${context.discoveredEntities.length}`);
        return context;
    }
    async updateConversationStage(sessionId, stage) {
        const context = await this.conversationStateService.getConversationContext(sessionId);
        if (!context) {
            throw new Error(`Conversation session ${sessionId} not found`);
        }
        const previousStage = context.conversationStage;
        context.conversationStage = stage;
        context.metadata.lastActiveAt = new Date();
        await this.conversationStateService.setConversationContext(sessionId, context);
        this.logger.debug(`Updated conversation stage for session ${sessionId}: ${previousStage} -> ${stage}`);
        return context;
    }
    async getConversationHistory(sessionId, limit = 10) {
        const context = await this.conversationStateService.getConversationContext(sessionId);
        if (!context) {
            return [];
        }
        return context.messages.slice(-limit);
    }
    async endConversation(sessionId) {
        await this.conversationStateService.deleteConversationContext(sessionId);
        this.logger.log(`Ended conversation session ${sessionId}`);
    }
    async getUserActiveSessions(userId) {
        return this.conversationStateService.getUserActiveSessions(userId);
    }
    generateSessionId() {
        return `chat_${(0, uuid_1.v4)()}`;
    }
    autoUpdateConversationStage(context, intent) {
        const messageCount = context.messages.length;
        const currentStage = context.conversationStage;
        if (currentStage === 'greeting' && messageCount >= 2) {
            context.conversationStage = 'discovery';
        }
        else if (currentStage === 'discovery' && intent?.type === 'refinement') {
            context.conversationStage = 'refinement';
        }
        else if ((currentStage === 'discovery' || currentStage === 'refinement') &&
            context.discoveredEntities.length >= 3) {
            context.conversationStage = 'recommendation';
        }
        else if (intent?.type === 'comparison') {
            context.conversationStage = 'comparison';
        }
    }
};
exports.ConversationManagerService = ConversationManagerService;
exports.ConversationManagerService = ConversationManagerService = ConversationManagerService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, common_1.Inject)('IConversationStateService')),
    __metadata("design:paramtypes", [Object])
], ConversationManagerService);
//# sourceMappingURL=conversation-manager.service.js.map