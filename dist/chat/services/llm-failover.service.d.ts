import { LlmFactoryService } from '../../common/llm/services/llm-factory.service';
import { ConversationContext, ChatResponse, UserIntent, CandidateEntity } from '../../common/llm/interfaces/llm.service.interface';
export declare class LlmFailoverService {
    private readonly llmFactoryService;
    private readonly logger;
    private readonly maxRetries;
    private readonly retryDelayMs;
    private providerHealth;
    constructor(llmFactoryService: LlmFactoryService);
    getChatResponseWithFailover(userMessage: string, context: ConversationContext, candidateEntities?: CandidateEntity[]): Promise<ChatResponse>;
    classifyIntentWithFailover(userMessage: string, context: ConversationContext): Promise<UserIntent>;
    generateFollowUpQuestionsWithFailover(context: ConversationContext): Promise<string[]>;
    shouldTransitionToRecommendationsWithFailover(context: ConversationContext): Promise<{
        shouldTransition: boolean;
        reason: string;
    }>;
    getProviderHealthStatus(): Record<string, any>;
    resetProviderHealth(provider: string): void;
    private initializeProviderHealth;
    private getHealthyProviders;
    private shouldRetryProvider;
    private markProviderHealthy;
    private markProviderUnhealthy;
    private executeWithTimeout;
    private getFallbackFollowUpQuestions;
    private getFallbackTransitionDecision;
}
