"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var ChatService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChatService = void 0;
const common_1 = require("@nestjs/common");
const conversation_manager_service_1 = require("./conversation-manager.service");
const enhanced_conversation_manager_service_1 = require("./enhanced-conversation-manager.service");
const chat_error_handler_service_1 = require("./chat-error-handler.service");
const llm_failover_service_1 = require("./llm-failover.service");
const chat_performance_monitor_service_1 = require("./chat-performance-monitor.service");
const chat_cache_service_1 = require("./chat-cache.service");
const enhanced_intent_classification_service_1 = require("./enhanced-intent-classification.service");
const conversation_flow_manager_service_1 = require("./conversation-flow-manager.service");
const intelligent_response_generator_service_1 = require("./intelligent-response-generator.service");
const intelligent_question_generator_service_1 = require("./intelligent-question-generator.service");
const advanced_prompt_generator_service_1 = require("./advanced-prompt-generator.service");
const advanced_entity_ranking_service_1 = require("../../common/ranking/advanced-entity-ranking.service");
const performance_optimization_service_1 = require("../../common/performance/performance-optimization.service");
const entities_service_1 = require("../../entities/entities.service");
const llm_factory_service_1 = require("../../common/llm/services/llm-factory.service");
let ChatService = ChatService_1 = class ChatService {
    constructor(conversationManager, enhancedConversationManager, chatErrorHandler, llmFailover, performanceMonitor, cacheService, enhancedIntentService, conversationFlowManager, intelligentResponseGenerator, intelligentQuestionGenerator, advancedPromptGenerator, advancedRankingService, performanceOptimizationService, entitiesService, llmService, llmFactoryService) {
        this.conversationManager = conversationManager;
        this.enhancedConversationManager = enhancedConversationManager;
        this.chatErrorHandler = chatErrorHandler;
        this.llmFailover = llmFailover;
        this.performanceMonitor = performanceMonitor;
        this.cacheService = cacheService;
        this.enhancedIntentService = enhancedIntentService;
        this.conversationFlowManager = conversationFlowManager;
        this.intelligentResponseGenerator = intelligentResponseGenerator;
        this.intelligentQuestionGenerator = intelligentQuestionGenerator;
        this.advancedPromptGenerator = advancedPromptGenerator;
        this.advancedRankingService = advancedRankingService;
        this.performanceOptimizationService = performanceOptimizationService;
        this.entitiesService = entitiesService;
        this.llmService = llmService;
        this.llmFactoryService = llmFactoryService;
        this.logger = new common_1.Logger(ChatService_1.name);
    }
    async sendMessage(userId, sendChatMessageDto) {
        const startTime = Date.now();
        const requestId = this.performanceMonitor.recordRequestStart(sendChatMessageDto.session_id || 'new');
        this.logger.log(`Processing chat message for user ${userId}, session: ${sendChatMessageDto.session_id || 'new'}, request: ${requestId}`);
        try {
            let context;
            try {
                context = await this.enhancedConversationManager.getOrCreateEnhancedConversation(userId, sendChatMessageDto.session_id);
            }
            catch (error) {
                this.logger.error('Failed to get/create enhanced conversation context', error.stack);
                return this.chatErrorHandler.handleConversationStateError(error, sendChatMessageDto.session_id || `chat_${Date.now()}`, userId);
            }
            if (sendChatMessageDto.user_preferences) {
                try {
                    await this.conversationManager.updateUserPreferences(context.sessionId, sendChatMessageDto.user_preferences);
                }
                catch (error) {
                    this.logger.warn('Failed to update user preferences', error.stack);
                }
            }
            let updatedContext;
            try {
                updatedContext = await this.conversationManager.addMessage(context.sessionId, {
                    role: 'user',
                    content: sendChatMessageDto.message,
                    metadata: sendChatMessageDto.context,
                });
            }
            catch (error) {
                this.logger.error('Failed to add user message to conversation', error.stack);
                updatedContext = context;
            }
            let enhancedIntent;
            try {
                enhancedIntent = await this.enhancedIntentService.classifyIntentWithFilters(sendChatMessageDto.message, updatedContext);
            }
            catch (error) {
                this.logger.warn('Enhanced intent classification failed, using basic flow', error.stack);
                enhancedIntent = null;
            }
            let enhancedContext = this.ensureEnhancedContext(updatedContext);
            if (enhancedIntent) {
                try {
                    enhancedContext = this.enhancedIntentService.updateConversationFilters(enhancedContext, enhancedIntent, updatedContext.messages?.length || 0);
                }
                catch (error) {
                    this.logger.warn('Failed to update conversation filters', error.stack);
                }
            }
            let correctionResult;
            if (enhancedIntent) {
                try {
                    correctionResult = this.conversationFlowManager.handleFilterCorrection(enhancedContext, enhancedIntent, sendChatMessageDto.message);
                    if (correctionResult.corrections.length > 0) {
                        this.applyFilterCorrections(enhancedContext, correctionResult.corrections);
                    }
                }
                catch (error) {
                    this.logger.warn('Failed to handle filter corrections', error.stack);
                }
            }
            let conversationAction;
            if (enhancedIntent) {
                try {
                    conversationAction = this.conversationFlowManager.determineNextAction(enhancedContext, enhancedIntent);
                }
                catch (error) {
                    this.logger.warn('Failed to determine conversation action', error.stack);
                }
            }
            let strategicQuestions = [];
            if (enhancedIntent) {
                try {
                    strategicQuestions = this.conversationFlowManager.generateStrategicQuestions(enhancedContext, enhancedIntent);
                }
                catch (error) {
                    this.logger.warn('Failed to generate strategic questions', error.stack);
                }
            }
            let conversationOptimization;
            if (enhancedIntent) {
                try {
                    conversationOptimization = this.conversationFlowManager.optimizeConversationFlow(enhancedContext, enhancedIntent);
                }
                catch (error) {
                    this.logger.warn('Failed to optimize conversation flow', error.stack);
                }
            }
            let candidateEntities;
            try {
                candidateEntities = await this.discoverRelevantEntities(sendChatMessageDto.message, enhancedContext);
            }
            catch (error) {
                this.logger.warn('Entity discovery failed, continuing without entities', error.stack);
                candidateEntities = [];
            }
            let chatResponse;
            if (enhancedIntent && conversationAction && this.intelligentResponseGenerator) {
                try {
                    const intelligentResponse = this.intelligentResponseGenerator.generateIntelligentResponse(enhancedContext, enhancedIntent, conversationAction, candidateEntities, strategicQuestions, correctionResult, conversationOptimization);
                    chatResponse = this.convertIntelligentResponseToChatResponse(intelligentResponse, enhancedContext);
                }
                catch (error) {
                    this.logger.warn('Intelligent response generation failed, falling back to LLM', error.stack);
                    chatResponse = null;
                }
            }
            if (!chatResponse) {
                try {
                    chatResponse = await this.llmFailover.getChatResponseWithFailover(sendChatMessageDto.message, enhancedContext, candidateEntities);
                }
                catch (error) {
                    this.logger.error('All LLM providers failed', error.stack);
                    return this.chatErrorHandler.handleLlmError(error, enhancedContext, sendChatMessageDto.message);
                }
            }
            let finalContext = updatedContext;
            try {
                finalContext = await this.conversationManager.addMessage(context.sessionId, {
                    role: 'assistant',
                    content: chatResponse.message,
                    metadata: {
                        intent: chatResponse.intent,
                        llmProvider: chatResponse.metadata.llmProvider,
                        responseTime: chatResponse.metadata.responseTime,
                    },
                }, chatResponse.intent);
            }
            catch (error) {
                this.logger.warn('Failed to add assistant message to conversation', error.stack);
            }
            if (chatResponse.discoveredEntities && chatResponse.discoveredEntities.length > 0) {
                try {
                    const entityIds = chatResponse.discoveredEntities.map((e) => e.id);
                    await this.conversationManager.addDiscoveredEntities(context.sessionId, entityIds);
                }
                catch (error) {
                    this.logger.warn('Failed to update discovered entities', error.stack);
                }
            }
            if (chatResponse.conversationStage !== finalContext.conversationStage) {
                try {
                    await this.conversationManager.updateConversationStage(context.sessionId, chatResponse.conversationStage);
                }
                catch (error) {
                    this.logger.warn('Failed to update conversation stage', error.stack);
                }
            }
            const response = {
                message: chatResponse.message,
                session_id: context.sessionId,
                conversation_stage: chatResponse.conversationStage,
                suggested_actions: chatResponse.suggestedActions?.map((action) => ({
                    type: action.type,
                    label: action.label,
                    data: action.data,
                })),
                discovered_entities: chatResponse.discoveredEntities?.map((entity) => ({
                    id: entity.id,
                    name: entity.name,
                    relevance_score: entity.relevanceScore,
                    reason: entity.reason,
                })),
                follow_up_questions: chatResponse.followUpQuestions,
                should_transition_to_recommendations: chatResponse.shouldTransitionToRecommendations,
                metadata: {
                    response_time: Date.now() - startTime,
                    llm_provider: chatResponse.metadata.llmProvider,
                    tokens_used: chatResponse.metadata.tokensUsed,
                },
                generated_at: new Date(),
            };
            this.performanceMonitor.recordRequestComplete(requestId, response.metadata.response_time, true, response.metadata.llm_provider, response.conversation_stage);
            this.logger.log(`Chat response generated for session ${context.sessionId} in ${response.metadata.response_time}ms`);
            return response;
        }
        catch (error) {
            this.logger.error('Critical error processing chat message', error.stack);
            this.performanceMonitor.recordRequestComplete(requestId, Date.now() - startTime, false, 'FALLBACK', 'error', error.message);
            return this.chatErrorHandler.createCriticalErrorFallback(sendChatMessageDto.session_id || `chat_${Date.now()}`, error);
        }
    }
    async sendEnhancedMessage(userId, sendChatMessageDto) {
        const startTime = Date.now();
        const requestId = this.performanceMonitor.recordRequestStart(sendChatMessageDto.session_id || 'new');
        this.logger.log(`Processing enhanced chat message for user ${userId}, session: ${sendChatMessageDto.session_id || 'new'}`);
        try {
            const context = await this.enhancedConversationManager.getOrCreateEnhancedConversation(userId, sendChatMessageDto.session_id);
            const extractedInfo = this.extractUserInformation(sendChatMessageDto.message);
            const updatedContext = await this.enhancedConversationManager.addMessageWithEnhancedTracking(context.sessionId, {
                role: 'user',
                content: sendChatMessageDto.message,
                metadata: sendChatMessageDto.context,
            }, undefined, extractedInfo);
            const enhancedIntent = await this.enhancedIntentService.classifyIntentWithFilters(sendChatMessageDto.message, updatedContext);
            const intelligentQuestions = this.intelligentQuestionGenerator.generateContextualQuestions(updatedContext, 2);
            const candidateEntities = await this.discoverRelevantEntities(sendChatMessageDto.message, updatedContext);
            const advancedPrompt = this.advancedPromptGenerator.generateAdvancedChatPrompt(sendChatMessageDto.message, updatedContext, enhancedIntent, candidateEntities, intelligentQuestions);
            let chatResponse;
            try {
                const llmService = this.llmFactoryService.getLlmServiceByProvider('OPENAI');
                chatResponse = await llmService.getChatResponse(sendChatMessageDto.message, updatedContext, candidateEntities);
            }
            catch (error) {
                this.logger.warn('Primary LLM failed, using failover', error.message);
                chatResponse = await this.llmFailover.getChatResponseWithFailover(sendChatMessageDto.message, updatedContext, candidateEntities);
            }
            if (intelligentQuestions.length > 0) {
                intelligentQuestions.forEach(question => {
                    this.enhancedConversationManager.trackQuestionAsked(updatedContext, question, 'contextual');
                });
            }
            const finalContext = await this.enhancedConversationManager.addMessageWithEnhancedTracking(context.sessionId, {
                role: 'assistant',
                content: chatResponse.message,
                metadata: {
                    intent: chatResponse.intent,
                    llmProvider: chatResponse.metadata.llmProvider,
                    responseTime: chatResponse.metadata.responseTime,
                },
            }, chatResponse.intent);
            const insights = this.enhancedConversationManager.getConversationInsights(finalContext);
            const response = {
                message: chatResponse.message,
                session_id: context.sessionId,
                conversation_stage: chatResponse.conversationStage,
                suggested_actions: chatResponse.suggestedActions?.map((action) => ({
                    type: action.type,
                    label: action.label,
                    data: action.data,
                })),
                discovered_entities: chatResponse.discoveredEntities?.map((entity) => ({
                    id: entity.id,
                    name: entity.name,
                    relevance_score: entity.relevanceScore,
                    reason: entity.reason,
                })),
                follow_up_questions: intelligentQuestions,
                should_transition_to_recommendations: insights.readinessForRecommendations > 0.8,
                metadata: {
                    response_time: Date.now() - startTime,
                    llm_provider: chatResponse.metadata.llmProvider,
                    tokens_used: chatResponse.metadata.tokensUsed,
                },
                generated_at: new Date(),
            };
            this.performanceMonitor.recordRequestComplete(requestId, response.metadata.response_time, true, response.metadata.llm_provider, response.conversation_stage);
            this.logger.log(`Enhanced chat response generated for session ${context.sessionId} in ${response.metadata.response_time}ms`);
            return response;
        }
        catch (error) {
            this.logger.error('Critical error processing enhanced chat message', error.stack);
            this.performanceMonitor.recordRequestComplete(requestId, Date.now() - startTime, false, 'FALLBACK', 'error', error.message);
            return this.chatErrorHandler.createCriticalErrorFallback(sendChatMessageDto.session_id || `chat_${Date.now()}`, error);
        }
    }
    async getConversationHistory(userId, sessionId, getHistoryDto) {
        this.logger.log(`Getting conversation history for session ${sessionId}`);
        try {
            const context = await this.conversationManager.getOrCreateConversation(userId, sessionId);
            if (context.userId !== userId) {
                this.chatErrorHandler.handleAuthError(new Error('Session does not belong to the user'), sessionId);
            }
            const messages = await this.conversationManager.getConversationHistory(sessionId, getHistoryDto.limit);
            const response = {
                session_id: sessionId,
                conversation_stage: context.conversationStage,
                messages: messages.map(msg => ({
                    id: msg.id,
                    role: msg.role,
                    content: msg.content,
                    timestamp: msg.timestamp,
                    metadata: msg.metadata,
                })),
                total_messages: context.metadata.totalMessages,
                discovered_entities_count: context.discoveredEntities.length,
                started_at: context.metadata.startedAt,
                last_active_at: context.metadata.lastActiveAt,
            };
            return response;
        }
        catch (error) {
            this.logger.error('Error getting conversation history', error.stack);
            throw error;
        }
    }
    async endConversation(userId, sessionId) {
        this.logger.log(`Ending conversation session ${sessionId} for user ${userId}`);
        const context = await this.conversationManager.getOrCreateConversation(userId, sessionId);
        if (context.userId !== userId) {
            throw new Error('Session does not belong to the user');
        }
        await this.conversationManager.endConversation(sessionId);
    }
    async getUserActiveSessions(userId) {
        return this.conversationManager.getUserActiveSessions(userId);
    }
    extractUserInformation(message) {
        const messageContent = message.toLowerCase();
        const extractedInfo = {
            userProfile: {},
            requirements: { mustHave: [], niceToHave: [], dealBreakers: [] },
            insights: {},
        };
        if (messageContent.includes('education') || messageContent.includes('teaching') || messageContent.includes('school')) {
            extractedInfo.userProfile.industry = 'education';
            extractedInfo.userProfile.workContext = 'education';
        }
        if (messageContent.includes('marketing') || messageContent.includes('advertising')) {
            extractedInfo.userProfile.industry = 'marketing';
            extractedInfo.userProfile.workContext = 'marketing';
        }
        if (messageContent.includes('development') || messageContent.includes('coding') || messageContent.includes('programming')) {
            extractedInfo.userProfile.industry = 'technology';
            extractedInfo.userProfile.workContext = 'development';
        }
        if (messageContent.includes('beginner') || messageContent.includes('new to') || messageContent.includes('just starting')) {
            extractedInfo.userProfile.experienceLevel = 'beginner';
        }
        if (messageContent.includes('advanced') || messageContent.includes('expert') || messageContent.includes('experienced')) {
            extractedInfo.userProfile.experienceLevel = 'advanced';
        }
        if (messageContent.includes('free') || messageContent.includes('no cost') || messageContent.includes('budget')) {
            extractedInfo.userProfile.budget = 'free';
        }
        if (messageContent.includes('enterprise') || messageContent.includes('company') || messageContent.includes('business')) {
            extractedInfo.userProfile.budget = 'enterprise';
        }
        if (messageContent.includes('team') || messageContent.includes('colleagues') || messageContent.includes('group')) {
            extractedInfo.userProfile.teamSize = 'small_team';
        }
        if (messageContent.includes('personal') || messageContent.includes('myself') || messageContent.includes('individual')) {
            extractedInfo.userProfile.teamSize = 'individual';
        }
        if (messageContent.includes('must have') || messageContent.includes('need') || messageContent.includes('require')) {
            const requirementKeywords = ['api', 'integration', 'mobile', 'web', 'offline', 'collaboration'];
            requirementKeywords.forEach(keyword => {
                if (messageContent.includes(keyword)) {
                    extractedInfo.requirements.mustHave.push(keyword);
                }
            });
        }
        if (messageContent.includes('urgent') || messageContent.includes('asap') || messageContent.includes('immediately')) {
            extractedInfo.insights.urgency = 'high';
        }
        if (messageContent.includes('planning') || messageContent.includes('future') || messageContent.includes('eventually')) {
            extractedInfo.insights.urgency = 'low';
        }
        return extractedInfo;
    }
    async discoverRelevantEntities(userMessage, context) {
        try {
            this.logger.debug('Starting enhanced entity discovery', {
                message: userMessage.substring(0, 100),
                sessionId: context.sessionId,
            });
            const enhancedIntent = await this.enhancedIntentService.classifyIntentWithFilters(userMessage, context);
            const enhancedContext = this.enhancedIntentService.updateConversationFilters(context, enhancedIntent, context.messages?.length || 0);
            const searchFilters = this.buildEntitySearchFilters(enhancedContext, enhancedIntent);
            this.logger.debug('Enhanced filters for entity search:', {
                extractedFilters: Object.keys(enhancedIntent.extractedFilters),
                accumulatedFilters: Object.keys(enhancedContext.accumulatedFilters?.filters || {}),
                finalFilters: Object.keys(searchFilters),
            });
            let entityResults;
            if (Object.keys(searchFilters).length > 1) {
                entityResults = await this.entitiesService.findAll(searchFilters);
                entityResults = entityResults.data;
            }
            else {
                entityResults = await this.performanceOptimizationService.optimizedVectorSearch(userMessage, async () => {
                    return Promise.race([
                        this.entitiesService.vectorSearch({
                            query: userMessage,
                            limit: 10,
                        }),
                        new Promise((_, reject) => setTimeout(() => reject(new Error('Vector search timeout')), 15000)),
                    ]);
                });
            }
            if (entityResults.length === 0) {
                this.logger.debug('No entities found through enhanced search');
                return [];
            }
            const filteredResults = this.applyAdvancedRanking(entityResults, enhancedContext, enhancedIntent, searchFilters);
            if (enhancedContext.enhancedMetadata) {
                enhancedContext.enhancedMetadata.lastEntityDiscovery = {
                    timestamp: new Date(),
                    filtersUsed: searchFilters,
                    entitiesFound: filteredResults.length,
                };
            }
            this.logger.debug(`Enhanced entity discovery completed: ${filteredResults.length} entities found`);
            return filteredResults.slice(0, 8);
        }
        catch (error) {
            this.logger.error('Error in enhanced entity discovery', error.stack);
            if (error.message?.includes('database') || error.message?.includes('connection')) {
                throw error;
            }
            return [];
        }
    }
    buildEntitySearchFilters(enhancedContext, enhancedIntent) {
        const filters = {
            limit: 15,
            page: 1,
        };
        if (enhancedContext.accumulatedFilters?.filters) {
            Object.assign(filters, enhancedContext.accumulatedFilters.filters);
        }
        Object.assign(filters, enhancedIntent.extractedFilters);
        if (!filters.searchTerm && enhancedIntent.constraints?.use_case) {
            filters.searchTerm = enhancedIntent.constraints.use_case;
        }
        if (!filters.entityTypeIds || filters.entityTypeIds.length === 0) {
            filters.entityTypeIds = ['ai-tool', 'course', 'job', 'event'];
        }
        return filters;
    }
    applyAdvancedRanking(entities, enhancedContext, enhancedIntent, appliedFilters) {
        try {
            const rankingContext = {
                appliedFilters,
                filterConfidence: enhancedContext.accumulatedFilters?.confidence || {},
                userPreferences: enhancedContext.userPreferences || {},
                userHistory: {
                    viewedEntities: enhancedContext.discoveredEntities || [],
                    skillProgression: {
                        currentLevel: enhancedContext.userPreferences?.technical_level,
                    },
                },
                collaborativeSignals: {
                    similarUserLikes: 0,
                },
                currentResults: [],
                intent: enhancedIntent,
            };
            const rankedEntities = this.advancedRankingService.rankEntities(entities, rankingContext);
            this.logger.debug('Advanced ranking applied to chat entities', {
                originalCount: entities.length,
                rankedCount: rankedEntities.length,
                topScore: rankedEntities[0]?.rankingScore,
                avgScore: rankedEntities.reduce((sum, e) => sum + e.rankingScore, 0) / rankedEntities.length,
            });
            return rankedEntities;
        }
        catch (error) {
            this.logger.warn('Advanced ranking failed, using basic sorting', error.stack);
            return entities.sort((a, b) => {
                const aScore = (a.avgRating || 0) + Math.log(Math.max(1, a.reviewCount || 0)) * 0.1;
                const bScore = (b.avgRating || 0) + Math.log(Math.max(1, b.reviewCount || 0)) * 0.1;
                return bScore - aScore;
            });
        }
    }
    ensureEnhancedContext(context) {
        if (context.accumulatedFilters && context.pendingClarifications && context.enhancedMetadata) {
            return context;
        }
        return {
            ...context,
            accumulatedFilters: context.accumulatedFilters || {
                filters: {},
                confidence: {},
                history: {},
                source: {},
            },
            pendingClarifications: context.pendingClarifications || [],
            enhancedMetadata: context.enhancedMetadata || {
                filtersExtracted: 0,
                clarificationQuestions: 0,
                conversationQuality: 0.5,
                readyForRecommendations: false,
            },
        };
    }
    applyFilterCorrections(context, corrections) {
        if (!context.accumulatedFilters) {
            context.accumulatedFilters = { filters: {}, confidence: {}, history: {}, source: {} };
        }
        corrections.forEach(correction => {
            context.accumulatedFilters.filters[correction.filterKey] = correction.newValue;
            context.accumulatedFilters.confidence[correction.filterKey] = correction.confidence;
            context.accumulatedFilters.source[correction.filterKey] = 'user_correction';
            this.logger.debug(`Applied filter correction: ${correction.filterKey} = ${correction.newValue}`);
        });
    }
    convertIntelligentResponseToChatResponse(intelligentResponse, context) {
        return {
            message: intelligentResponse.message,
            conversationStage: intelligentResponse.conversationStage,
            intent: {
                type: 'discovery',
                confidence: 0.8,
            },
            suggestedActions: intelligentResponse.suggestedActions?.map((action) => ({
                type: action.type,
                label: action.label,
                data: { description: action.description, priority: action.priority },
            })) || [],
            discoveredEntities: intelligentResponse.discoveredEntities?.map((entity) => ({
                id: entity.id,
                name: entity.name,
                relevanceScore: entity.relevanceScore,
                reason: `Matches ${entity.matchedFilters.join(', ')}`,
            })) || [],
            followUpQuestions: intelligentResponse.strategicQuestions?.map((q) => q.question) || [],
            shouldTransitionToRecommendations: intelligentResponse.readyForRecommendations,
            metadata: {
                llmProvider: 'INTELLIGENT_SYSTEM',
                responseTime: 0,
                tokensUsed: 0,
                enhancedFeatures: {
                    filtersAccumulated: intelligentResponse.metadata.filtersAccumulated,
                    correctionHandled: intelligentResponse.metadata.correctionHandled,
                    actionType: intelligentResponse.metadata.actionType,
                    conversationGuidance: intelligentResponse.conversationGuidance,
                },
            },
        };
    }
    getPerformanceMetrics() {
        return this.performanceMonitor.getMetrics();
    }
    getPerformanceHealth() {
        return this.performanceMonitor.getHealthStatus();
    }
    getCacheStats() {
        return this.cacheService.getCacheStats();
    }
    clearCaches() {
        this.cacheService.clearAllCaches();
    }
    async getCurrentLlmProvider() {
        try {
            const providers = this.llmFactoryService.getAvailableProviders();
            return providers[0] || 'UNKNOWN';
        }
        catch (error) {
            this.logger.error('Error getting current LLM provider', error.stack);
            return 'UNKNOWN';
        }
    }
};
exports.ChatService = ChatService;
exports.ChatService = ChatService = ChatService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(14, (0, common_1.Inject)('ILlmService')),
    __metadata("design:paramtypes", [conversation_manager_service_1.ConversationManagerService,
        enhanced_conversation_manager_service_1.EnhancedConversationManagerService,
        chat_error_handler_service_1.ChatErrorHandlerService,
        llm_failover_service_1.LlmFailoverService,
        chat_performance_monitor_service_1.ChatPerformanceMonitorService,
        chat_cache_service_1.ChatCacheService,
        enhanced_intent_classification_service_1.EnhancedIntentClassificationService,
        conversation_flow_manager_service_1.ConversationFlowManagerService,
        intelligent_response_generator_service_1.IntelligentResponseGeneratorService,
        intelligent_question_generator_service_1.IntelligentQuestionGeneratorService,
        advanced_prompt_generator_service_1.AdvancedPromptGeneratorService,
        advanced_entity_ranking_service_1.AdvancedEntityRankingService,
        performance_optimization_service_1.PerformanceOptimizationService,
        entities_service_1.EntitiesService, Object, llm_factory_service_1.LlmFactoryService])
], ChatService);
//# sourceMappingURL=chat.service.js.map