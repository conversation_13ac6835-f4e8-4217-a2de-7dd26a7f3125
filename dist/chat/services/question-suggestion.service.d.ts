import { ConversationContext } from '../../common/llm/interfaces/llm.service.interface';
export declare class QuestionSuggestionService {
    private readonly logger;
    generateFollowUpQuestions(context: ConversationContext, userMessage: string, maxQuestions?: number): string[];
    private getInitialQuestions;
    private getExplorationQuestions;
    private getRefinementQuestions;
    private filterAndRankQuestions;
    private calculateRelevance;
    isQuestionCategoryRecent(context: ConversationContext, category: string): boolean;
    generateTopicQuestions(topic: string, context: ConversationContext): string[];
}
