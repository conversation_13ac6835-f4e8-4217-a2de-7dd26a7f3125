import { OnModuleInit, OnModule<PERSON><PERSON>roy } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { IConversationStateService } from '../interfaces/conversation-state.interface';
import { ConversationContext } from '../../common/llm/interfaces/llm.service.interface';
export declare class MemoryConversationStateService implements IConversationStateService, OnModuleInit, OnModuleDestroy {
    private readonly configService;
    private readonly logger;
    private readonly cache;
    private readonly userSessionsIndex;
    private cleanupInterval;
    private readonly config;
    constructor(configService: ConfigService);
    onModuleInit(): void;
    onModuleDestroy(): void;
    setConversationContext(sessionId: string, context: ConversationContext, ttlSeconds?: number): Promise<void>;
    getConversationContext(sessionId: string): Promise<ConversationContext | null>;
    deleteConversationContext(sessionId: string): Promise<void>;
    hasConversationContext(sessionId: string): Promise<boolean>;
    getUserActiveSessions(userId: string): Promise<string[]>;
    cleanupExpiredConversations(): Promise<number>;
    getStats(): Promise<{
        totalSessions: number;
        activeSessions: number;
        memoryUsage?: number;
    }>;
    private addToUserIndex;
    private removeFromUserIndex;
    private estimateMemoryUsage;
}
