import { ConversationContext, ChatMessage, UserIntent } from '../../common/llm/interfaces/llm.service.interface';
import { IConversationStateService } from '../interfaces/conversation-state.interface';
export declare class ConversationManagerService {
    private readonly conversationStateService;
    private readonly logger;
    constructor(conversationStateService: IConversationStateService);
    initializeConversation(userId: string, sessionId?: string): Promise<ConversationContext>;
    getOrCreateConversation(userId: string, sessionId?: string): Promise<ConversationContext>;
    addMessage(sessionId: string, message: Omit<ChatMessage, 'id' | 'timestamp'>, intent?: UserIntent): Promise<ConversationContext>;
    updateUserPreferences(sessionId: string, preferences: Partial<ConversationContext['userPreferences']>): Promise<ConversationContext>;
    addDiscoveredEntities(sessionId: string, entityIds: string[]): Promise<ConversationContext>;
    updateConversationStage(sessionId: string, stage: ConversationContext['conversationStage']): Promise<ConversationContext>;
    getConversationHistory(sessionId: string, limit?: number): Promise<ChatMessage[]>;
    endConversation(sessionId: string): Promise<void>;
    getUserActiveSessions(userId: string): Promise<string[]>;
    private generateSessionId;
    private autoUpdateConversationStage;
}
