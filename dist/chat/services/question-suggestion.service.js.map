{"version": 3, "file": "question-suggestion.service.js", "sourceRoot": "", "sources": ["../../../src/chat/services/question-suggestion.service.ts"], "names": [], "mappings": ";;;;;;;;;;AAAA,2CAAoD;AAQ7C,IAAM,yBAAyB,iCAA/B,MAAM,yBAAyB;IAA/B;QACY,WAAM,GAAG,IAAI,eAAM,CAAC,2BAAyB,CAAC,IAAI,CAAC,CAAC;IA8NvE,CAAC;IAzNC,yBAAyB,CACvB,OAA4B,EAC5B,WAAmB,EACnB,eAAuB,CAAC;QAExB,IAAI,CAAC;YACH,MAAM,SAAS,GAAa,EAAE,CAAC;YAC/B,MAAM,YAAY,GAAG,OAAO,CAAC,QAAQ,EAAE,MAAM,IAAI,CAAC,CAAC;YACnD,MAAM,SAAS,GAAG,OAAO,CAAC,eAAe,IAAI,EAAE,CAAC;YAGhD,IAAI,YAAY,IAAI,CAAC,EAAE,CAAC;gBACtB,SAAS,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,mBAAmB,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC,CAAC;YACtE,CAAC;iBAEI,IAAI,YAAY,IAAI,CAAC,EAAE,CAAC;gBAC3B,SAAS,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,uBAAuB,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC,CAAC;YAC1E,CAAC;iBAEI,CAAC;gBACJ,SAAS,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,sBAAsB,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC,CAAC;YACzE,CAAC;YAGD,OAAO,IAAI,CAAC,sBAAsB,CAAC,SAAS,EAAE,OAAO,EAAE,YAAY,CAAC,CAAC;QACvE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,sCAAsC,EAAE,KAAK,CAAC,CAAC;YACjE,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAKO,mBAAmB,CAAC,WAAmB,EAAE,SAAc;QAC7D,MAAM,SAAS,GAAa,EAAE,CAAC;QAC/B,MAAM,OAAO,GAAG,WAAW,CAAC,WAAW,EAAE,CAAC;QAG1C,IAAI,CAAC,SAAS,CAAC,QAAQ,IAAI,CAAC,SAAS,CAAC,YAAY,EAAE,CAAC;YACnD,IAAI,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;gBAC5F,SAAS,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;YAClD,CAAC;iBAAM,CAAC;gBACN,SAAS,CAAC,IAAI,CAAC,yEAAyE,CAAC,CAAC;YAC5F,CAAC;QACH,CAAC;QAGD,IAAI,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;YACxF,SAAS,CAAC,IAAI,CAAC,0DAA0D,CAAC,CAAC;QAC7E,CAAC;QAGD,IAAI,CAAC,SAAS,CAAC,eAAe,EAAE,CAAC;YAC/B,SAAS,CAAC,IAAI,CAAC,qDAAqD,CAAC,CAAC;QACxE,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAKO,uBAAuB,CAAC,WAAmB,EAAE,SAAc;QACjE,MAAM,SAAS,GAAa,EAAE,CAAC;QAC/B,MAAM,OAAO,GAAG,WAAW,CAAC,WAAW,EAAE,CAAC;QAG1C,IAAI,SAAS,CAAC,QAAQ,KAAK,WAAW,IAAI,SAAS,CAAC,YAAY,KAAK,WAAW,EAAE,CAAC;YACjF,SAAS,CAAC,IAAI,CAAC,4FAA4F,CAAC,CAAC;QAC/G,CAAC;aAAM,IAAI,SAAS,CAAC,QAAQ,KAAK,WAAW,EAAE,CAAC;YAC9C,SAAS,CAAC,IAAI,CAAC,0EAA0E,CAAC,CAAC;QAC7F,CAAC;aAAM,IAAI,SAAS,CAAC,QAAQ,KAAK,YAAY,EAAE,CAAC;YAC/C,SAAS,CAAC,IAAI,CAAC,gFAAgF,CAAC,CAAC;QACnG,CAAC;QAGD,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC;YACtB,SAAS,CAAC,IAAI,CAAC,8EAA8E,CAAC,CAAC;QACjG,CAAC;QAGD,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,CAAC;YACzB,SAAS,CAAC,IAAI,CAAC,gFAAgF,CAAC,CAAC;QACnG,CAAC;QAGD,IAAI,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;YAC1F,SAAS,CAAC,IAAI,CAAC,sDAAsD,CAAC,CAAC;QACzE,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAKO,sBAAsB,CAAC,WAAmB,EAAE,SAAc;QAChE,MAAM,SAAS,GAAa,EAAE,CAAC;QAG/B,SAAS,CAAC,IAAI,CAAC,oFAAoF,CAAC,CAAC;QAGrG,SAAS,CAAC,IAAI,CAAC,sDAAsD,CAAC,CAAC;QAGvE,SAAS,CAAC,IAAI,CAAC,4EAA4E,CAAC,CAAC;QAG7F,SAAS,CAAC,IAAI,CAAC,+DAA+D,CAAC,CAAC;QAEhF,OAAO,SAAS,CAAC;IACnB,CAAC;IAKO,sBAAsB,CAC5B,SAAmB,EACnB,OAA4B,EAC5B,YAAoB;QAGpB,MAAM,eAAe,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC;QAGhD,MAAM,cAAc,GAAG,OAAO,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;QACzD,MAAM,aAAa,GAAG,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAEjF,MAAM,eAAe,GAAG,eAAe;aACpC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;YAChB,QAAQ;YACR,SAAS,EAAE,IAAI,CAAC,kBAAkB,CAAC,QAAQ,EAAE,aAAa,CAAC;SAC5D,CAAC,CAAC;aACF,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,SAAS,CAAC;aACzC,KAAK,CAAC,CAAC,EAAE,YAAY,CAAC;aACtB,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAE9B,OAAO,eAAe,CAAC;IACzB,CAAC;IAKO,kBAAkB,CAAC,QAAgB,EAAE,aAAqB;QAChE,IAAI,KAAK,GAAG,GAAG,CAAC;QAGhB,MAAM,aAAa,GAAG,QAAQ,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACxD,MAAM,QAAQ,GAAG,aAAa,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAC3C,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,CACvF,CAAC;QAEF,KAAK,MAAM,IAAI,IAAI,QAAQ,EAAE,CAAC;YAC5B,IAAI,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;gBACjC,KAAK,IAAI,GAAG,CAAC;YACf,CAAC;QACH,CAAC;QAED,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;IAC9B,CAAC;IAKD,wBAAwB,CAAC,OAA4B,EAAE,QAAgB;QACrE,MAAM,cAAc,GAAG,OAAO,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;QACzD,MAAM,aAAa,GAAG,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAEjF,MAAM,gBAAgB,GAA6B;YACjD,QAAQ,EAAE,CAAC,UAAU,EAAE,MAAM,EAAE,UAAU,EAAE,SAAS,CAAC;YACrD,MAAM,EAAE,CAAC,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,CAAC;YACnD,IAAI,EAAE,CAAC,MAAM,EAAE,UAAU,EAAE,YAAY,EAAE,OAAO,CAAC;YACjD,SAAS,EAAE,CAAC,aAAa,EAAE,YAAY,EAAE,WAAW,EAAE,UAAU,CAAC;YACjE,QAAQ,EAAE,CAAC,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,UAAU,CAAC;YAClD,WAAW,EAAE,CAAC,WAAW,EAAE,UAAU,EAAE,UAAU,EAAE,KAAK,CAAC;SAC1D,CAAC;QAEF,MAAM,QAAQ,GAAG,gBAAgB,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;QAClD,OAAO,QAAQ,CAAC,IAAI,CAAC,CAAC,OAAe,EAAE,EAAE,CAAC,aAAa,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;IAC7E,CAAC;IAKD,sBAAsB,CAAC,KAAa,EAAE,OAA4B;QAChE,MAAM,cAAc,GAA6B;YAC/C,MAAM,EAAE;gBACN,wCAAwC;gBACxC,sEAAsE;gBACtE,mEAAmE;aACpE;YACD,QAAQ,EAAE;gBACR,+BAA+B;gBAC/B,qDAAqD;gBACrD,iCAAiC;aAClC;YACD,eAAe,EAAE;gBACf,uDAAuD;gBACvD,wFAAwF;gBACxF,6CAA6C;aAC9C;YACD,SAAS,EAAE;gBACT,yCAAyC;gBACzC,2CAA2C;gBAC3C,qCAAqC;aACtC;YACD,QAAQ,EAAE;gBACR,kDAAkD;gBAClD,0CAA0C;gBAC1C,uCAAuC;aACxC;SACF,CAAC;QAEF,OAAO,cAAc,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;IACrC,CAAC;CACF,CAAA;AA/NY,8DAAyB;oCAAzB,yBAAyB;IADrC,IAAA,mBAAU,GAAE;GACA,yBAAyB,CA+NrC"}