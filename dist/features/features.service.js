"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FeaturesService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const _generated_prisma_1 = require("@generated-prisma");
const slug_utils_1 = require("../utils/slug.utils");
let FeaturesService = class FeaturesService {
    constructor(prisma) {
        this.prisma = prisma;
    }
    async getUniqueSlug(name) {
        let slug = (0, slug_utils_1.generateSlug)(name);
        let count = 0;
        while (await this.prisma.feature.findUnique({ where: { slug } })) {
            count++;
            slug = `${(0, slug_utils_1.generateSlug)(name)}-${count}`;
        }
        return slug;
    }
    async create(createFeatureDto) {
        const { name, description, iconUrl } = createFeatureDto;
        const slug = await this.getUniqueSlug(name);
        return this.prisma.feature.create({
            data: {
                name,
                slug,
                description,
                iconUrl,
            },
        });
    }
    async findAll() {
        return this.prisma.feature.findMany({
            orderBy: {
                name: 'asc',
            },
        });
    }
    async findOne(id) {
        const feature = await this.prisma.feature.findUnique({
            where: { id },
        });
        if (!feature) {
            throw new common_1.NotFoundException(`Feature with ID ${id} not found`);
        }
        return feature;
    }
    async findBySlug(slug) {
        const feature = await this.prisma.feature.findUnique({
            where: { slug },
        });
        if (!feature) {
            throw new common_1.NotFoundException(`Feature with slug ${slug} not found`);
        }
        return feature;
    }
    async update(id, updateFeatureDto) {
        await this.findOne(id);
        const data = { ...updateFeatureDto };
        if (updateFeatureDto.name) {
            data.slug = await this.getUniqueSlug(updateFeatureDto.name);
        }
        try {
            return await this.prisma.feature.update({
                where: { id },
                data,
            });
        }
        catch (error) {
            if (error instanceof _generated_prisma_1.Prisma.PrismaClientKnownRequestError && error.code === 'P2025') {
                throw new common_1.NotFoundException(`Feature with ID ${id} not found`);
            }
            throw error;
        }
    }
    async remove(id) {
        await this.findOne(id);
        try {
            return await this.prisma.feature.delete({
                where: { id },
            });
        }
        catch (error) {
            if (error instanceof _generated_prisma_1.Prisma.PrismaClientKnownRequestError && error.code === 'P2025') {
                throw new common_1.NotFoundException(`Feature with ID ${id} not found`);
            }
            throw error;
        }
    }
};
exports.FeaturesService = FeaturesService;
exports.FeaturesService = FeaturesService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], FeaturesService);
//# sourceMappingURL=features.service.js.map