"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BookmarksService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const prisma_1 = require("generated/prisma");
const logger_service_1 = require("../common/logger/logger.service");
const activity_logger_service_1 = require("../common/activity-logger.service");
const validation_service_1 = require("../common/validation.service");
let BookmarksService = class BookmarksService {
    constructor(prisma, logger, activityLogger, validationService) {
        this.prisma = prisma;
        this.logger = logger;
        this.activityLogger = activityLogger;
        this.validationService = validationService;
    }
    async saveBookmark(userId, createBookmarkDto) {
        const { entity_id } = createBookmarkDto;
        await this.validationService.validateBookmarkAction(userId, entity_id);
        const entity = await this.prisma.entity.findUnique({
            where: { id: entity_id },
            select: { id: true, name: true, slug: true },
        });
        if (!entity) {
            throw new common_1.NotFoundException(`Entity with ID "${entity_id}" not found.`);
        }
        try {
            const savedBookmark = await this.prisma.userSavedEntity.upsert({
                where: {
                    userId_entityId: {
                        userId: userId,
                        entityId: entity_id,
                    },
                },
                create: {
                    userId: userId,
                    entityId: entity_id,
                },
                update: {},
            });
            await this.activityLogger.logBookmarkActivity(userId, entity_id, entity.name, entity.slug || entity.id, 'added');
            return savedBookmark;
        }
        catch (error) {
            this.logger.logError(error, {
                operation: 'saveBookmark',
                userId,
                entityId: entity_id,
                type: 'bookmark_save_error',
            });
            if (error instanceof prisma_1.Prisma.PrismaClientKnownRequestError) {
                switch (error.code) {
                    case 'P2002':
                        this.logger.warn('Unique constraint violation during bookmark save', {
                            operation: 'saveBookmark',
                            userId,
                            entityId: entity_id,
                            errorCode: error.code,
                        });
                        const existingBookmark = await this.prisma.userSavedEntity.findUnique({
                            where: {
                                userId_entityId: {
                                    userId: userId,
                                    entityId: entity_id,
                                },
                            },
                        });
                        if (existingBookmark) {
                            return existingBookmark;
                        }
                        break;
                    case 'P2003':
                        throw new common_1.NotFoundException(`Invalid user ID or entity ID provided.`);
                    default:
                        this.logger.logError(error, {
                            operation: 'saveBookmark',
                            userId,
                            entityId: entity_id,
                            errorCode: error.code,
                            type: 'prisma_unknown_error',
                        });
                        break;
                }
            }
            throw new common_1.InternalServerErrorException('Could not save the bookmark. Please try again later.');
        }
    }
    async unsaveBookmark(userId, entityId) {
        const entity = await this.prisma.entity.findUnique({
            where: { id: entityId },
            select: { id: true, name: true, slug: true },
        });
        try {
            await this.prisma.userSavedEntity.delete({
                where: {
                    userId_entityId: {
                        userId: userId,
                        entityId: entityId,
                    },
                },
            });
            this.logger.log('Bookmark successfully removed', {
                operation: 'unsaveBookmark',
                userId,
                entityId,
            });
            if (entity) {
                await this.activityLogger.logBookmarkActivity(userId, entityId, entity.name, entity.slug || entity.id, 'removed');
            }
        }
        catch (error) {
            if (error instanceof prisma_1.Prisma.PrismaClientKnownRequestError) {
                if (error.code === 'P2025') {
                    this.logger.log('Bookmark not found during delete - treating as success', {
                        operation: 'unsaveBookmark',
                        userId,
                        entityId,
                        reason: 'bookmark_not_found',
                    });
                    return;
                }
            }
            this.logger.logError(error, {
                operation: 'unsaveBookmark',
                userId,
                entityId,
                type: 'bookmark_delete_error',
            });
            throw new common_1.InternalServerErrorException('Could not unsave the bookmark. Please try again later.');
        }
    }
    async listSavedBookmarks(userId, listBookmarksDto) {
        const { page = 1, limit = 10 } = listBookmarksDto;
        const skip = (page - 1) * limit;
        this.logger.log('Fetching saved bookmarks', {
            operation: 'listSavedBookmarks',
            userId,
            page,
            limit,
            skip,
        });
        try {
            const [savedJoins, total] = await this.prisma.$transaction([
                this.prisma.userSavedEntity.findMany({
                    where: { userId },
                    skip,
                    take: limit,
                    orderBy: {
                        createdAt: prisma_1.Prisma.SortOrder.desc,
                    },
                    include: {
                        entity: {
                            include: {
                                entityType: { select: { id: true, name: true, slug: true } },
                                submitter: { select: { id: true, username: true, profilePictureUrl: true } },
                            },
                        },
                    },
                }),
                this.prisma.userSavedEntity.count({ where: { userId } }),
            ]);
            const entities = savedJoins.map(joinRecord => joinRecord.entity);
            const response = {
                data: entities,
                meta: {
                    total: total,
                    page: page,
                    limit: limit,
                    totalPages: Math.ceil(total / limit),
                },
            };
            this.logger.log('Returning bookmarks response', {
                operation: 'listSavedBookmarks',
                userId,
                total: total,
                itemCount: entities.length,
                page: page,
                limit: limit,
                totalPages: response.meta.totalPages,
            });
            return response;
        }
        catch (error) {
            this.logger.logError(error, {
                operation: 'listSavedBookmarks',
                userId,
                page,
                limit,
                type: 'bookmark_list_error',
            });
            throw new common_1.InternalServerErrorException('Could not retrieve saved bookmarks. Please try again later.');
        }
    }
};
exports.BookmarksService = BookmarksService;
exports.BookmarksService = BookmarksService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        logger_service_1.AppLoggerService,
        activity_logger_service_1.ActivityLoggerService,
        validation_service_1.ValidationService])
], BookmarksService);
//# sourceMappingURL=bookmarks.service.js.map