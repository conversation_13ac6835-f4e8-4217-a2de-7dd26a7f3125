"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ListEntitiesDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_transformer_1 = require("class-transformer");
const class_validator_1 = require("class-validator");
const _generated_prisma_1 = require("@generated-prisma");
const transformQueryArray = ({ value }) => {
    if (value === null || value === undefined) {
        return value;
    }
    if (Array.isArray(value)) {
        return value.map(v => String(v).trim()).filter(Boolean);
    }
    return value.split(',').map(s => s.trim()).filter(Boolean);
};
class ListEntitiesDto {
    constructor() {
        this.page = 1;
        this.limit = 10;
        this.sortBy = 'createdAt';
        this.sortOrder = _generated_prisma_1.Prisma.SortOrder.desc;
    }
}
exports.ListEntitiesDto = ListEntitiesDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Page number for pagination',
        default: 1,
        type: Number,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(1),
    __metadata("design:type", Number)
], ListEntitiesDto.prototype, "page", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Number of items per page',
        default: 10,
        type: Number,
        maximum: 100,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(100),
    __metadata("design:type", Number)
], ListEntitiesDto.prototype, "limit", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by entity status',
        enum: _generated_prisma_1.EntityStatus,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(_generated_prisma_1.EntityStatus),
    __metadata("design:type", String)
], ListEntitiesDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by one or more entity type IDs (UUIDs). Provide as comma-separated string or multiple query parameters.',
        type: [String],
        example: ['uuid-for-type1', 'uuid-for-type2'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(transformQueryArray),
    (0, class_validator_1.IsUUID)('4', { each: true, message: 'Each entity type ID must be a valid UUID version 4.' }),
    __metadata("design:type", Array)
], ListEntitiesDto.prototype, "entityTypeIds", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by one or more category IDs (UUIDs). Provide as comma-separated string or multiple query parameters.',
        type: [String],
        example: ['uuid-for-category1', 'uuid-for-category2'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(transformQueryArray),
    (0, class_validator_1.IsUUID)('4', { each: true }),
    __metadata("design:type", Array)
], ListEntitiesDto.prototype, "categoryIds", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by one or more tag IDs (UUIDs). Provide as comma-separated string or multiple query parameters.',
        type: [String],
        example: ['uuid-for-tag1', 'uuid-for-tag2'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(transformQueryArray),
    (0, class_validator_1.IsUUID)('4', { each: true }),
    __metadata("design:type", Array)
], ListEntitiesDto.prototype, "tagIds", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by one or more feature IDs (UUIDs). Provide as comma-separated string or multiple query parameters.',
        type: [String],
        example: ['uuid-for-feature1', 'uuid-for-feature2'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(transformQueryArray),
    (0, class_validator_1.IsUUID)('4', { each: true, message: 'Each feature ID must be a valid UUID version 4.' }),
    __metadata("design:type", Array)
], ListEntitiesDto.prototype, "featureIds", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Search term to filter by (e.g., in name, short_description, description)',
        example: 'AI tool for images',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ListEntitiesDto.prototype, "searchTerm", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Field to sort by - supports entity fields and computed values',
        enum: [
            'createdAt', 'updatedAt', 'name', 'foundedYear',
            'averageRating', 'reviewCount', 'saveCount',
            'relevance', 'popularity'
        ],
        example: 'averageRating',
        default: 'createdAt',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)([
        'createdAt', 'updatedAt', 'name', 'foundedYear',
        'averageRating', 'reviewCount', 'saveCount',
        'relevance', 'popularity'
    ]),
    __metadata("design:type", String)
], ListEntitiesDto.prototype, "sortBy", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Sort order',
        enum: ['asc', 'desc'],
        default: 'desc',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(_generated_prisma_1.Prisma.SortOrder),
    __metadata("design:type", String)
], ListEntitiesDto.prototype, "sortOrder", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by submitter user ID (UUID of the user who submitted the entity)',
        example: 'user-uuid-of-submitter',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUUID)('4'),
    __metadata("design:type", String)
], ListEntitiesDto.prototype, "submitterId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter entities created on or after this date (YYYY-MM-DD or ISO 8601 string).',
        type: Date,
        format: 'date-time',
        example: '2023-01-01T00:00:00.000Z',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Date),
    (0, class_validator_1.IsDate)(),
    __metadata("design:type", Date)
], ListEntitiesDto.prototype, "createdAtFrom", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter entities created on or before this date (YYYY-MM-DD or ISO 8601 string).',
        type: Date,
        format: 'date-time',
        example: '2023-12-31T23:59:59.999Z',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Date),
    (0, class_validator_1.IsDate)(),
    __metadata("design:type", Date)
], ListEntitiesDto.prototype, "createdAtTo", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter entities by whether they have a free tier. Can be true or false.',
        type: Boolean,
        example: true,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => {
        if (value === 'true' || value === true)
            return true;
        if (value === 'false' || value === false)
            return false;
        return undefined;
    }),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], ListEntitiesDto.prototype, "hasFreeTier", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by one or more employee count ranges.',
        type: [String],
        enum: _generated_prisma_1.EmployeeCountRange,
        example: [_generated_prisma_1.EmployeeCountRange.C1_10, _generated_prisma_1.EmployeeCountRange.C11_50],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(transformQueryArray),
    (0, class_validator_1.IsEnum)(_generated_prisma_1.EmployeeCountRange, { each: true }),
    __metadata("design:type", Array)
], ListEntitiesDto.prototype, "employeeCountRanges", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by one or more funding stages.',
        type: [String],
        enum: _generated_prisma_1.FundingStage,
        example: [_generated_prisma_1.FundingStage.SEED, _generated_prisma_1.FundingStage.SERIES_A],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(transformQueryArray),
    (0, class_validator_1.IsEnum)(_generated_prisma_1.FundingStage, { each: true }),
    __metadata("design:type", Array)
], ListEntitiesDto.prototype, "fundingStages", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Search by location summary.',
        example: 'San Francisco',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ListEntitiesDto.prototype, "locationSearch", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by API access (for tools).',
        type: Boolean,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => {
        if (value === 'true' || value === true)
            return true;
        if (value === 'false' || value === false)
            return false;
        return undefined;
    }),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], ListEntitiesDto.prototype, "apiAccess", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by one or more pricing models.',
        type: [String],
        enum: _generated_prisma_1.PricingModel,
        example: [_generated_prisma_1.PricingModel.SUBSCRIPTION, _generated_prisma_1.PricingModel.PAY_PER_USE],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(transformQueryArray),
    (0, class_validator_1.IsEnum)(_generated_prisma_1.PricingModel, { each: true }),
    __metadata("design:type", Array)
], ListEntitiesDto.prototype, "pricingModels", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by one or more price ranges.',
        type: [String],
        enum: _generated_prisma_1.PriceRange,
        example: [_generated_prisma_1.PriceRange.LOW, _generated_prisma_1.PriceRange.MEDIUM],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(transformQueryArray),
    (0, class_validator_1.IsEnum)(_generated_prisma_1.PriceRange, { each: true }),
    __metadata("design:type", Array)
], ListEntitiesDto.prototype, "priceRanges", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by integrations (e.g., "GitHub", "Slack"). Provide as a comma-separated string.',
        type: [String],
        example: 'GitHub,Slack',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(transformQueryArray),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], ListEntitiesDto.prototype, "integrations", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by supported platforms/OS (e.g., "Windows", "macOS"). Provide as a comma-separated string.',
        type: [String],
        example: 'Windows,macOS',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(transformQueryArray),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], ListEntitiesDto.prototype, "platforms", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by target audience (e.g., "Developers", "Marketers"). Provide as a comma-separated string.',
        type: [String],
        example: 'Developers,Marketers',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(transformQueryArray),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], ListEntitiesDto.prototype, "targetAudience", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Minimum average rating (1-5 scale)',
        type: Number,
        minimum: 1,
        maximum: 5,
        example: 4.0,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(5),
    __metadata("design:type", Number)
], ListEntitiesDto.prototype, "rating_min", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Maximum average rating (1-5 scale)',
        type: Number,
        minimum: 1,
        maximum: 5,
        example: 5.0,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(5),
    __metadata("design:type", Number)
], ListEntitiesDto.prototype, "rating_max", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Minimum number of reviews',
        type: Number,
        minimum: 0,
        example: 10,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], ListEntitiesDto.prototype, "review_count_min", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Maximum number of reviews',
        type: Number,
        minimum: 0,
        example: 1000,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], ListEntitiesDto.prototype, "review_count_max", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by affiliate status',
        enum: ['NONE', 'APPLIED', 'APPROVED', 'REJECTED'],
        example: 'APPROVED',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ListEntitiesDto.prototype, "affiliate_status", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter entities that have affiliate/referral links available',
        type: Boolean,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => {
        if (value === 'true' || value === true)
            return true;
        if (value === 'false' || value === false)
            return false;
        return undefined;
    }),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], ListEntitiesDto.prototype, "has_affiliate_link", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by technical level required to use the tool',
        enum: _generated_prisma_1.TechnicalLevel,
        isArray: true,
        example: [_generated_prisma_1.TechnicalLevel.BEGINNER, _generated_prisma_1.TechnicalLevel.INTERMEDIATE],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(transformQueryArray),
    (0, class_validator_1.IsEnum)(_generated_prisma_1.TechnicalLevel, { each: true }),
    __metadata("design:type", Array)
], ListEntitiesDto.prototype, "technical_levels", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by learning curve difficulty',
        enum: _generated_prisma_1.LearningCurve,
        isArray: true,
        example: [_generated_prisma_1.LearningCurve.LOW, _generated_prisma_1.LearningCurve.MEDIUM],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(transformQueryArray),
    (0, class_validator_1.IsEnum)(_generated_prisma_1.LearningCurve, { each: true }),
    __metadata("design:type", Array)
], ListEntitiesDto.prototype, "learning_curves", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter tools that have API access',
        type: Boolean,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => {
        if (value === 'true' || value === true)
            return true;
        if (value === 'false' || value === false)
            return false;
        return undefined;
    }),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], ListEntitiesDto.prototype, "has_api", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter tools that have a free tier',
        type: Boolean,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => {
        if (value === 'true' || value === true)
            return true;
        if (value === 'false' || value === false)
            return false;
        return undefined;
    }),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], ListEntitiesDto.prototype, "has_free_tier", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter tools that are open source',
        type: Boolean,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => {
        if (value === 'true' || value === true)
            return true;
        if (value === 'false' || value === false)
            return false;
        return undefined;
    }),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], ListEntitiesDto.prototype, "open_source", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter tools that have mobile support',
        type: Boolean,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => {
        if (value === 'true' || value === true)
            return true;
        if (value === 'false' || value === false)
            return false;
        return undefined;
    }),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], ListEntitiesDto.prototype, "mobile_support", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter tools that have demo available',
        type: Boolean,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => {
        if (value === 'true' || value === true)
            return true;
        if (value === 'false' || value === false)
            return false;
        return undefined;
    }),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], ListEntitiesDto.prototype, "demo_available", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by frameworks supported',
        type: [String],
        example: ['TensorFlow', 'PyTorch', 'Hugging Face'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(transformQueryArray),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], ListEntitiesDto.prototype, "frameworks", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by libraries supported',
        type: [String],
        example: ['OpenAI', 'Anthropic', 'Cohere'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(transformQueryArray),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], ListEntitiesDto.prototype, "libraries", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Search in key features (searches within the JSON array)',
        example: 'natural language processing',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ListEntitiesDto.prototype, "key_features_search", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Search in use cases (searches within the JSON array)',
        example: 'content generation',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ListEntitiesDto.prototype, "use_cases_search", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Search in target audience (searches within the JSON array)',
        example: 'developers',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ListEntitiesDto.prototype, "target_audience_search", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by deployment options',
        type: [String],
        example: ['Cloud', 'On-premise', 'Hybrid'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(transformQueryArray),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], ListEntitiesDto.prototype, "deployment_options", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by support channels available',
        type: [String],
        example: ['Email', 'Chat', 'Phone', 'Community'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(transformQueryArray),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], ListEntitiesDto.prototype, "support_channels", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter tools that have live chat support',
        type: Boolean,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => {
        if (value === 'true' || value === true)
            return true;
        if (value === 'false' || value === false)
            return false;
        return undefined;
    }),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], ListEntitiesDto.prototype, "has_live_chat", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Search by customization level',
        example: 'high',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ListEntitiesDto.prototype, "customization_level", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Search in pricing details',
        example: 'per user per month',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ListEntitiesDto.prototype, "pricing_details_search", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by skill levels required for the course',
        enum: _generated_prisma_1.SkillLevel,
        isArray: true,
        example: [_generated_prisma_1.SkillLevel.BEGINNER, _generated_prisma_1.SkillLevel.INTERMEDIATE],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(transformQueryArray),
    (0, class_validator_1.IsEnum)(_generated_prisma_1.SkillLevel, { each: true }),
    __metadata("design:type", Array)
], ListEntitiesDto.prototype, "skill_levels", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by whether a certificate is available',
        type: Boolean,
        example: true,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => {
        if (value === 'true' || value === true)
            return true;
        if (value === 'false' || value === false)
            return false;
        return undefined;
    }),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], ListEntitiesDto.prototype, "certificate_available", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Search by instructor name (partial match)',
        example: 'Dr. Smith',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ListEntitiesDto.prototype, "instructor_name", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Search by course duration text (partial match)',
        example: '10 hours',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ListEntitiesDto.prototype, "duration_text", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Minimum enrollment count',
        type: Number,
        minimum: 0,
        example: 100,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], ListEntitiesDto.prototype, "enrollment_min", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Maximum enrollment count',
        type: Number,
        minimum: 0,
        example: 10000,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], ListEntitiesDto.prototype, "enrollment_max", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Search in course prerequisites (partial match)',
        example: 'programming',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ListEntitiesDto.prototype, "prerequisites", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter courses that have a syllabus URL available',
        type: Boolean,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => {
        if (value === 'true' || value === true)
            return true;
        if (value === 'false' || value === false)
            return false;
        return undefined;
    }),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], ListEntitiesDto.prototype, "has_syllabus", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by employment types',
        type: [String],
        enum: _generated_prisma_1.EmploymentTypeEnum,
        example: [_generated_prisma_1.EmploymentTypeEnum.FULL_TIME, _generated_prisma_1.EmploymentTypeEnum.PART_TIME, _generated_prisma_1.EmploymentTypeEnum.CONTRACT],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(transformQueryArray),
    (0, class_validator_1.IsEnum)(_generated_prisma_1.EmploymentTypeEnum, { each: true }),
    __metadata("design:type", Array)
], ListEntitiesDto.prototype, "employment_types", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by experience levels',
        type: [String],
        example: ['Entry', 'Mid', 'Senior', 'Lead'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(transformQueryArray),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], ListEntitiesDto.prototype, "experience_levels", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by location types',
        type: [String],
        example: ['Remote', 'On-site', 'Hybrid'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(transformQueryArray),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], ListEntitiesDto.prototype, "location_types", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Search by company name (partial match)',
        example: 'Google',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ListEntitiesDto.prototype, "company_name", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Search by job title (partial match)',
        example: 'AI Engineer',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ListEntitiesDto.prototype, "job_title", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Minimum salary (in thousands, e.g., 80 for $80k)',
        type: Number,
        minimum: 0,
        maximum: 1000,
        example: 80,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.Max)(1000),
    __metadata("design:type", Number)
], ListEntitiesDto.prototype, "salary_min", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Maximum salary (in thousands, e.g., 150 for $150k)',
        type: Number,
        minimum: 0,
        maximum: 1000,
        example: 150,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.Max)(1000),
    __metadata("design:type", Number)
], ListEntitiesDto.prototype, "salary_max", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Search in job description (partial match)',
        example: 'machine learning',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ListEntitiesDto.prototype, "job_description", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter jobs that have application URL available',
        type: Boolean,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => {
        if (value === 'true' || value === true)
            return true;
        if (value === 'false' || value === false)
            return false;
        return undefined;
    }),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], ListEntitiesDto.prototype, "has_application_url", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by hardware types',
        type: [String],
        enum: _generated_prisma_1.HardwareTypeEnum,
        example: [_generated_prisma_1.HardwareTypeEnum.GPU, _generated_prisma_1.HardwareTypeEnum.CPU, _generated_prisma_1.HardwareTypeEnum.FPGA],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(transformQueryArray),
    (0, class_validator_1.IsEnum)(_generated_prisma_1.HardwareTypeEnum, { each: true }),
    __metadata("design:type", Array)
], ListEntitiesDto.prototype, "hardware_types", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by manufacturers',
        type: [String],
        example: ['NVIDIA', 'Intel', 'AMD', 'Apple'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(transformQueryArray),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], ListEntitiesDto.prototype, "manufacturers", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by release date from (YYYY-MM-DD)',
        example: '2023-01-01',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], ListEntitiesDto.prototype, "release_date_from", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by release date to (YYYY-MM-DD)',
        example: '2024-12-31',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], ListEntitiesDto.prototype, "release_date_to", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Search in price range text (partial match)',
        example: '$500',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ListEntitiesDto.prototype, "price_range", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Minimum price (in dollars)',
        type: Number,
        minimum: 0,
        example: 500,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], ListEntitiesDto.prototype, "price_min", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Maximum price (in dollars)',
        type: Number,
        minimum: 0,
        example: 2000,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], ListEntitiesDto.prototype, "price_max", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Search in specifications (searches within the JSON object)',
        example: 'GDDR6',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ListEntitiesDto.prototype, "specifications_search", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter hardware that has datasheet URL available',
        type: Boolean,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => {
        if (value === 'true' || value === true)
            return true;
        if (value === 'false' || value === false)
            return false;
        return undefined;
    }),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], ListEntitiesDto.prototype, "has_datasheet", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Search by memory specifications (partial match)',
        example: '16GB',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ListEntitiesDto.prototype, "memory_search", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Search by processor specifications (partial match)',
        example: 'Intel i7',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ListEntitiesDto.prototype, "processor_search", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by event types',
        type: [String],
        example: ['Conference', 'Workshop', 'Webinar', 'Meetup', 'Hackathon'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(transformQueryArray),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], ListEntitiesDto.prototype, "event_types", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter events starting from this date (YYYY-MM-DD)',
        example: '2024-01-01',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], ListEntitiesDto.prototype, "start_date_from", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter events starting before this date (YYYY-MM-DD)',
        example: '2024-12-31',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], ListEntitiesDto.prototype, "start_date_to", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter events ending from this date (YYYY-MM-DD)',
        example: '2024-01-01',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], ListEntitiesDto.prototype, "end_date_from", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter events ending before this date (YYYY-MM-DD)',
        example: '2024-12-31',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], ListEntitiesDto.prototype, "end_date_to", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by whether the event is online',
        type: Boolean,
        example: true,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => {
        if (value === 'true' || value === true)
            return true;
        if (value === 'false' || value === false)
            return false;
        return undefined;
    }),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], ListEntitiesDto.prototype, "is_online", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Search by event location (partial match)',
        example: 'San Francisco',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ListEntitiesDto.prototype, "location", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Search in event price text (partial match)',
        example: 'Free',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ListEntitiesDto.prototype, "price_text", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter events that require registration',
        type: Boolean,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => {
        if (value === 'true' || value === true)
            return true;
        if (value === 'false' || value === false)
            return false;
        return undefined;
    }),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], ListEntitiesDto.prototype, "registration_required", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter events that have registration URL available',
        type: Boolean,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => {
        if (value === 'true' || value === true)
            return true;
        if (value === 'false' || value === false)
            return false;
        return undefined;
    }),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], ListEntitiesDto.prototype, "has_registration_url", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Search by key speakers (partial match)',
        example: 'Elon Musk',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ListEntitiesDto.prototype, "speakers_search", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by services offered (searches within the JSON array)',
        type: [String],
        example: ['AI Strategy', 'Machine Learning', 'Data Science', 'Automation'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(transformQueryArray),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], ListEntitiesDto.prototype, "services_offered", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by industry focus (searches within the JSON array)',
        type: [String],
        example: ['Healthcare', 'Finance', 'E-commerce', 'Manufacturing'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(transformQueryArray),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], ListEntitiesDto.prototype, "industry_focus", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter agencies that have portfolio URL available',
        type: Boolean,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => {
        if (value === 'true' || value === true)
            return true;
        if (value === 'false' || value === false)
            return false;
        return undefined;
    }),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], ListEntitiesDto.prototype, "has_portfolio", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by license type',
        type: [String],
        example: ['MIT', 'Apache 2.0', 'GPL', 'Commercial', 'Proprietary'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(transformQueryArray),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], ListEntitiesDto.prototype, "license_types", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by programming languages (searches within the JSON array)',
        type: [String],
        example: ['Python', 'JavaScript', 'Java', 'C++', 'R'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(transformQueryArray),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], ListEntitiesDto.prototype, "programming_languages", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by platform compatibility (searches within the JSON array)',
        type: [String],
        example: ['Windows', 'macOS', 'Linux', 'Web', 'Mobile'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(transformQueryArray),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], ListEntitiesDto.prototype, "platform_compatibility", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter software that has repository URL available',
        type: Boolean,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => {
        if (value === 'true' || value === true)
            return true;
        if (value === 'false' || value === false)
            return false;
        return undefined;
    }),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], ListEntitiesDto.prototype, "has_repository", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Search by current version (partial match)',
        example: '2.0',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ListEntitiesDto.prototype, "current_version", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by research areas',
        type: [String],
        example: ['Machine Learning', 'Computer Vision', 'NLP', 'Robotics'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(transformQueryArray),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], ListEntitiesDto.prototype, "research_areas", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Search by authors (partial match)',
        example: 'Geoffrey Hinton',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ListEntitiesDto.prototype, "authors_search", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by publication date from (YYYY-MM-DD)',
        example: '2020-01-01',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], ListEntitiesDto.prototype, "publication_date_from", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by publication date to (YYYY-MM-DD)',
        example: '2024-12-31',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], ListEntitiesDto.prototype, "publication_date_to", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Search by author name (partial match)',
        example: 'Andrew Ng',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ListEntitiesDto.prototype, "author_name", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Search by ISBN',
        example: '978-0262035613',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ListEntitiesDto.prototype, "isbn", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by book format',
        type: [String],
        example: ['Hardcover', 'Paperback', 'eBook', 'Audiobook'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(transformQueryArray),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], ListEntitiesDto.prototype, "formats", void 0);
//# sourceMappingURL=list-entities.dto.js.map