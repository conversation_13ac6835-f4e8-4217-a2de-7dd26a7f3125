"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateSoftwareDetailsDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const prisma_1 = require("../../../../generated/prisma/index.js");
class CreateSoftwareDetailsDto {
}
exports.CreateSoftwareDetailsDto = CreateSoftwareDetailsDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'URL of the software repository',
        example: 'https://github.com/company/awesome-ai-tool'
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUrl)(),
    __metadata("design:type", String)
], CreateSoftwareDetailsDto.prototype, "repository_url", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Type of license',
        example: 'MIT',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateSoftwareDetailsDto.prototype, "license_type", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'List of programming languages used',
        type: [String],
        example: ['Python', 'JavaScript', 'TypeScript']
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], CreateSoftwareDetailsDto.prototype, "programming_languages", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'List of compatible platforms',
        type: [String],
        example: ['Windows', 'macOS', 'Linux', 'Web']
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], CreateSoftwareDetailsDto.prototype, "platform_compatibility", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Current version of the software',
        example: '2.1.0'
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateSoftwareDetailsDto.prototype, "current_version", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Release date of the software (YYYY-MM-DD)',
        type: String,
        example: '2024-01-15'
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", Date)
], CreateSoftwareDetailsDto.prototype, "release_date", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Whether the software is open source',
        example: true
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], CreateSoftwareDetailsDto.prototype, "open_source", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Does the software have a free tier?', type: Boolean }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], CreateSoftwareDetailsDto.prototype, "has_free_tier", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'List of use cases for the software', type: [String] }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], CreateSoftwareDetailsDto.prototype, "use_cases", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Pricing model', enum: prisma_1.PricingModel }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(prisma_1.PricingModel),
    __metadata("design:type", String)
], CreateSoftwareDetailsDto.prototype, "pricing_model", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Price range', enum: prisma_1.PriceRange }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(prisma_1.PriceRange),
    __metadata("design:type", String)
], CreateSoftwareDetailsDto.prototype, "price_range", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Detailed pricing information' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateSoftwareDetailsDto.prototype, "pricing_details", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'URL to the pricing page' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUrl)(),
    __metadata("design:type", String)
], CreateSoftwareDetailsDto.prototype, "pricing_url", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'List of integrations', type: [String] }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], CreateSoftwareDetailsDto.prototype, "integrations", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Support email address' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateSoftwareDetailsDto.prototype, "support_email", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Does the software offer live chat support?', type: Boolean }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], CreateSoftwareDetailsDto.prototype, "has_live_chat", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'URL to the community forum or support page' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUrl)(),
    __metadata("design:type", String)
], CreateSoftwareDetailsDto.prototype, "community_url", void 0);
//# sourceMappingURL=create-software-details.dto.js.map