"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PlatformDetailsResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const _generated_prisma_1 = require("@generated-prisma");
class PlatformDetailsResponseDto {
}
exports.PlatformDetailsResponseDto = PlatformDetailsResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Entity ID (UUID) this detail record is associated with.',
        example: 'o5p6q7r8-s9t0-u1v2-w3x4-y5z6a7b8c9d0',
    }),
    __metadata("design:type", String)
], PlatformDetailsResponseDto.prototype, "entityId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Key features of the platform.',
        type: 'array',
        items: { type: 'string' },
        example: ['Model Training', 'Data Annotation', 'Deployment'],
    }),
    __metadata("design:type", Object)
], PlatformDetailsResponseDto.prototype, "keyFeatures", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Common use cases for the platform.',
        type: 'array',
        items: { type: 'string' },
        example: ['Building and deploying AI applications', 'Managing ML workflows'],
    }),
    __metadata("design:type", Object)
], PlatformDetailsResponseDto.prototype, "useCases", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Known integrations with other tools or services.',
        type: 'array',
        items: { type: 'string' },
        example: ['GitHub', 'GitLab', 'Bitbucket'],
    }),
    __metadata("design:type", Object)
], PlatformDetailsResponseDto.prototype, "integrations", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Target audience for the platform.',
        type: 'array',
        items: { type: 'string' },
        example: ['Data Scientists', 'ML Engineers'],
    }),
    __metadata("design:type", Object)
], PlatformDetailsResponseDto.prototype, "targetAudience", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Options for deploying applications on the platform.',
        type: 'array',
        items: { type: 'string' },
        example: ['Cloud', 'On-premise'],
    }),
    __metadata("design:type", Object)
], PlatformDetailsResponseDto.prototype, "deploymentOptions", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Operating systems supported by the platform or its tools.',
        type: 'array',
        items: { type: 'string' },
        example: ['Linux', 'Windows', 'macOS'],
    }),
    __metadata("design:type", Object)
], PlatformDetailsResponseDto.prototype, "supportedOs", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Indicates if the platform has mobile support.',
        example: false,
    }),
    __metadata("design:type", Object)
], PlatformDetailsResponseDto.prototype, "mobileSupport", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Indicates if the platform provides API access.',
        example: true,
    }),
    __metadata("design:type", Object)
], PlatformDetailsResponseDto.prototype, "apiAccess", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Indicates if the platform has a free tier.',
        example: true,
    }),
    __metadata("design:type", Object)
], PlatformDetailsResponseDto.prototype, "hasFreeTier", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        enum: _generated_prisma_1.PricingModel,
        description: 'The pricing model of the platform.',
        example: _generated_prisma_1.PricingModel.PAY_PER_USE,
    }),
    __metadata("design:type", Object)
], PlatformDetailsResponseDto.prototype, "pricingModel", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        enum: _generated_prisma_1.PriceRange,
        description: 'The price range of the platform.',
        example: _generated_prisma_1.PriceRange.MEDIUM,
    }),
    __metadata("design:type", Object)
], PlatformDetailsResponseDto.prototype, "priceRange", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Specific details about pricing.',
        example: 'Based on usage, starting at $0.01 per API call.',
    }),
    __metadata("design:type", Object)
], PlatformDetailsResponseDto.prototype, "pricingDetails", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'URL to the pricing page of the platform.',
        example: 'https://example.com/platform/pricing',
    }),
    __metadata("design:type", Object)
], PlatformDetailsResponseDto.prototype, "pricingUrl", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Available support channels.',
        type: 'array',
        items: { type: 'string' },
        example: ['Email', 'Community Forum'],
    }),
    __metadata("design:type", Object)
], PlatformDetailsResponseDto.prototype, "supportChannels", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Contact email for support.',
        example: '<EMAIL>',
    }),
    __metadata("design:type", Object)
], PlatformDetailsResponseDto.prototype, "supportEmail", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Indicates if live chat support is available.',
        example: false,
    }),
    __metadata("design:type", Object)
], PlatformDetailsResponseDto.prototype, "hasLiveChat", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'URL to the community forum or Discord.',
        example: 'https://example.com/platform/community',
    }),
    __metadata("design:type", Object)
], PlatformDetailsResponseDto.prototype, "communityUrl", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Timestamp of when the platform details were created',
        example: '2024-11-01T00:00:00.000Z',
    }),
    __metadata("design:type", Date)
], PlatformDetailsResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Timestamp of the last update to the platform details',
        example: '2024-11-10T10:00:00.000Z',
    }),
    __metadata("design:type", Date)
], PlatformDetailsResponseDto.prototype, "updatedAt", void 0);
//# sourceMappingURL=platform-details-response.dto.js.map