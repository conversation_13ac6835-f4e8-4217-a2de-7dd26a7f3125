"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateEventDetailsDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const prisma_1 = require("generated/prisma");
class CreateEventDetailsDto {
}
exports.CreateEventDetailsDto = CreateEventDetailsDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Type of the event',
        example: 'Conference',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateEventDetailsDto.prototype, "event_type", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Start date of the event (YYYY-MM-DD or ISO8601 DateTime)',
        example: '2024-09-15T09:00:00Z',
        type: String,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", Date)
], CreateEventDetailsDto.prototype, "start_date", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'End date of the event (YYYY-MM-DD or ISO8601 DateTime)',
        example: '2024-09-17T17:00:00Z',
        type: String,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", Date)
], CreateEventDetailsDto.prototype, "end_date", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Location of the event (physical address or "Online")',
        example: 'San Francisco, CA',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateEventDetailsDto.prototype, "location", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Whether this is an online event',
        example: true,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], CreateEventDetailsDto.prototype, "is_online", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Event format',
        enum: prisma_1.EventFormatEnum,
        example: prisma_1.EventFormatEnum.hybrid,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(prisma_1.EventFormatEnum),
    __metadata("design:type", String)
], CreateEventDetailsDto.prototype, "event_format", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Whether registration is required',
        example: true,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], CreateEventDetailsDto.prototype, "registration_required", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'URL for event registration',
        example: 'https://eventbrite.com/event/123',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUrl)(),
    __metadata("design:type", String)
], CreateEventDetailsDto.prototype, "registration_url", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Event capacity (maximum attendees)',
        example: 500,
        type: Number,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CreateEventDetailsDto.prototype, "capacity", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Event organizer name',
        example: 'AI Conference Organization',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateEventDetailsDto.prototype, "organizer", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Key speakers at the event',
        type: [String],
        example: ['Dr. AI Expert', 'Jane Innovations', 'Prof. Machine Learning'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], CreateEventDetailsDto.prototype, "key_speakers", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Target audience for the event',
        type: [String],
        example: ['Developers', 'Data Scientists', 'AI Researchers', 'Business Leaders'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], CreateEventDetailsDto.prototype, "target_audience", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Topics covered at the event',
        type: [String],
        example: ['Machine Learning', 'Natural Language Processing', 'Computer Vision'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], CreateEventDetailsDto.prototype, "topics", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Price of the event (e.g., "Free", "$99")',
        example: '$99',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateEventDetailsDto.prototype, "price", void 0);
//# sourceMappingURL=create-event-details.dto.js.map