"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateNewsDetailsDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const _generated_prisma_1 = require("@generated-prisma");
class CreateNewsDetailsDto {
}
exports.CreateNewsDetailsDto = CreateNewsDetailsDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Publication date of the news article (YYYY-MM-DD)',
        example: '2024-03-20',
        type: String,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", Date)
], CreateNewsDetailsDto.prototype, "publication_date", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Name of the source of the news (e.g., TechCrunch, Reuters)',
        example: 'TechCrunch',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateNewsDetailsDto.prototype, "source_name", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'URL to the news article',
        example: 'https://techcrunch.com/ai-breakthrough',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUrl)(),
    __metadata("design:type", String)
], CreateNewsDetailsDto.prototype, "articleUrl", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Author of the news article',
        example: 'Jane Doe',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateNewsDetailsDto.prototype, "author", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Brief summary of the news article',
        example: 'A new AI model has achieved state-of-the-art results in...',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateNewsDetailsDto.prototype, "summary", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Status of the news item.',
        enum: _generated_prisma_1.EntityStatus,
        example: _generated_prisma_1.EntityStatus.ACTIVE,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(_generated_prisma_1.EntityStatus),
    __metadata("design:type", String)
], CreateNewsDetailsDto.prototype, "status", void 0);
//# sourceMappingURL=create-news-details.dto.js.map