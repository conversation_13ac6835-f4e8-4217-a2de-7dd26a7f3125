"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateJobDetailsDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const prisma_1 = require("../../../../generated/prisma/index.js");
class CreateJobDetailsDto {
}
exports.CreateJobDetailsDto = CreateJobDetailsDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Title of the job', example: 'AI Engineer' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateJobDetailsDto.prototype, "job_title", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Name of the hiring company', example: 'Tech Solutions Inc.' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateJobDetailsDto.prototype, "company_name", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Employment types for this job',
        type: [String],
        enum: prisma_1.EmploymentTypeEnum,
        example: [prisma_1.EmploymentTypeEnum.FULL_TIME, prisma_1.EmploymentTypeEnum.CONTRACT],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsEnum)(prisma_1.EmploymentTypeEnum, { each: true }),
    __metadata("design:type", Array)
], CreateJobDetailsDto.prototype, "employment_types", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Experience level required',
        enum: prisma_1.ExperienceLevelEnum,
        example: prisma_1.ExperienceLevelEnum.MID,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(prisma_1.ExperienceLevelEnum),
    __metadata("design:type", String)
], CreateJobDetailsDto.prototype, "experience_level", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Location types for this job',
        type: [String],
        enum: prisma_1.LocationTypeEnum,
        example: [prisma_1.LocationTypeEnum.Remote, prisma_1.LocationTypeEnum.Hybrid],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsEnum)(prisma_1.LocationTypeEnum, { each: true }),
    __metadata("design:type", Array)
], CreateJobDetailsDto.prototype, "location_types", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Minimum salary in thousands (e.g., 80 for $80k)',
        example: 80,
        type: Number,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CreateJobDetailsDto.prototype, "salary_min", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Maximum salary in thousands (e.g., 120 for $120k)',
        example: 120,
        type: Number,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CreateJobDetailsDto.prototype, "salary_max", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'URL to the job application page',
        example: 'https://jobs.example.com/apply/123',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUrl)(),
    __metadata("design:type", String)
], CreateJobDetailsDto.prototype, "application_url", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Full description of the job responsibilities and requirements',
        example: 'Seeking an experienced AI engineer to develop cutting-edge solutions...',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateJobDetailsDto.prototype, "job_description", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Whether this is a remote job',
        example: true,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], CreateJobDetailsDto.prototype, "is_remote", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Job location (if not remote)',
        example: 'San Francisco, CA',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateJobDetailsDto.prototype, "location", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Job type/category',
        example: 'Software Engineering',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateJobDetailsDto.prototype, "job_type", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Key responsibilities for this role',
        type: [String],
        example: ['Develop AI models', 'Collaborate with data scientists', 'Deploy ML solutions'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], CreateJobDetailsDto.prototype, "key_responsibilities", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Required skills for this position',
        type: [String],
        example: ['Python', 'TensorFlow', 'Machine Learning', 'Docker'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], CreateJobDetailsDto.prototype, "required_skills", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Benefits offered with this position',
        type: [String],
        example: ['Health Insurance', 'Stock Options', 'Remote Work', 'Flexible Hours'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], CreateJobDetailsDto.prototype, "benefits", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Remote work policy',
        example: 'Fully Remote',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateJobDetailsDto.prototype, "remote_policy", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Whether visa sponsorship is available',
        example: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], CreateJobDetailsDto.prototype, "visa_sponsorship", void 0);
//# sourceMappingURL=create-job-details.dto.js.map